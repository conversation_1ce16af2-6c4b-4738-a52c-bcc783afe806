#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Processing Module for Contract Analysis
Handles PDF, DOCX, and TXT file processing with text extraction
"""

import io
import os
import tempfile
from typing import Dict, List, Optional, Any, Tuple
import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileProcessor:
    """Handles file upload and text extraction for contract analysis"""
    
    def __init__(self):
        """Initialize file processor with supported formats"""
        self.supported_formats = {
            'pdf': ['pdf'],
            'docx': ['docx', 'doc'],
            'text': ['txt', 'text']
        }
        self.max_file_size = 50 * 1024 * 1024  # 50MB limit
    
    def process_uploaded_file(self, uploaded_file) -> Dict[str, Any]:
        """Process uploaded file and extract text"""
        try:
            if uploaded_file is None:
                return {'success': False, 'error': 'لم يتم رفع أي ملف'}
            
            # Check file size
            if uploaded_file.size > self.max_file_size:
                return {
                    'success': False, 
                    'error': f'حجم الملف كبير جداً. الحد الأقصى {self.max_file_size // (1024*1024)} ميجابايت'
                }
            
            # Get file extension
            file_extension = self._get_file_extension(uploaded_file.name)
            
            if not self._is_supported_format(file_extension):
                return {
                    'success': False,
                    'error': f'صيغة الملف غير مدعومة. الصيغ المدعومة: {", ".join(self._get_all_extensions())}'
                }
            
            # Extract text based on file type
            text_content = self._extract_text(uploaded_file, file_extension)
            
            if not text_content.strip():
                return {'success': False, 'error': 'الملف فارغ أو لا يحتوي على نص قابل للقراءة'}
            
            # Analyze file content
            analysis = self._analyze_file_content(text_content, uploaded_file)
            
            return {
                'success': True,
                'text': text_content,
                'filename': uploaded_file.name,
                'file_type': file_extension,
                'file_size': uploaded_file.size,
                'analysis': analysis
            }
            
        except Exception as e:
            logger.error(f"File processing error: {e}")
            return {'success': False, 'error': f'خطأ في معالجة الملف: {str(e)}'}
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return filename.split('.')[-1].lower() if '.' in filename else ''
    
    def _is_supported_format(self, extension: str) -> bool:
        """Check if file format is supported"""
        all_extensions = self._get_all_extensions()
        return extension in all_extensions
    
    def _get_all_extensions(self) -> List[str]:
        """Get all supported file extensions"""
        extensions = []
        for format_list in self.supported_formats.values():
            extensions.extend(format_list)
        return extensions
    
    def _extract_text(self, uploaded_file, file_extension: str) -> str:
        """Extract text from uploaded file based on its type"""
        try:
            if file_extension == 'pdf':
                return self._extract_pdf_text(uploaded_file)
            elif file_extension in ['docx', 'doc']:
                return self._extract_docx_text(uploaded_file)
            elif file_extension in ['txt', 'text']:
                return self._extract_txt_text(uploaded_file)
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")
        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            raise
    
    def _extract_pdf_text(self, uploaded_file) -> str:
        """Extract text from PDF file"""
        try:
            import PyPDF2
            
            # Read PDF content
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(uploaded_file.read()))
            text_content = ""
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text_content += page.extract_text() + "\n"
            
            return text_content.strip()
            
        except ImportError:
            # Fallback to pdfplumber if PyPDF2 is not available
            try:
                import pdfplumber
                
                with pdfplumber.open(io.BytesIO(uploaded_file.read())) as pdf:
                    text_content = ""
                    for page in pdf.pages:
                        text_content += page.extract_text() + "\n"
                
                return text_content.strip()
                
            except ImportError:
                raise ImportError("مكتبة قراءة PDF غير متوفرة. يرجى تثبيت PyPDF2 أو pdfplumber")
    
    def _extract_docx_text(self, uploaded_file) -> str:
        """Extract text from DOCX file"""
        try:
            from docx import Document
            
            # Read DOCX content
            doc = Document(io.BytesIO(uploaded_file.read()))
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text_content += cell.text + " "
                    text_content += "\n"
            
            return text_content.strip()
            
        except ImportError:
            raise ImportError("مكتبة قراءة DOCX غير متوفرة. يرجى تثبيت python-docx")
    
    def _extract_txt_text(self, uploaded_file) -> str:
        """Extract text from TXT file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'cp1256', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    uploaded_file.seek(0)  # Reset file pointer
                    content = uploaded_file.read()
                    if isinstance(content, bytes):
                        text_content = content.decode(encoding)
                    else:
                        text_content = str(content)
                    return text_content.strip()
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, try with error handling
            uploaded_file.seek(0)
            content = uploaded_file.read()
            if isinstance(content, bytes):
                text_content = content.decode('utf-8', errors='ignore')
            else:
                text_content = str(content)
            
            return text_content.strip()
            
        except Exception as e:
            raise Exception(f"خطأ في قراءة الملف النصي: {str(e)}")
    
    def _analyze_file_content(self, text: str, uploaded_file) -> Dict[str, Any]:
        """Analyze extracted text content"""
        words = text.split()
        lines = text.split('\n')
        
        # Basic content analysis
        analysis = {
            'word_count': len(words),
            'line_count': len(lines),
            'character_count': len(text),
            'character_count_no_spaces': len(text.replace(' ', '')),
            'paragraph_count': len([line for line in lines if line.strip()]),
            'estimated_pages': max(1, len(words) // 250),  # Estimate based on ~250 words per page
            'language_detected': self._detect_language(text),
            'contains_arabic': self._contains_arabic(text),
            'file_info': {
                'name': uploaded_file.name,
                'size_bytes': uploaded_file.size,
                'size_mb': round(uploaded_file.size / (1024 * 1024), 2)
            }
        }
        
        return analysis
    
    def _detect_language(self, text: str) -> str:
        """Simple language detection"""
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        english_chars = sum(1 for char in text if char.isalpha() and char.isascii())
        
        if arabic_chars > english_chars:
            return 'Arabic'
        elif english_chars > arabic_chars:
            return 'English'
        else:
            return 'Mixed'
    
    def _contains_arabic(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        return any('\u0600' <= char <= '\u06FF' for char in text)
    
    def render_file_uploader(self, key: str = "file_uploader") -> Optional[Any]:
        """Render file uploader component with enhanced styling"""
        st.markdown("""
        <div class="file-upload-container">
            <h3 style="color: #1e3a8a; font-weight: 700; margin-bottom: 1rem;">
                📁 رفع ملف العقد
            </h3>
            <p style="color: #64748b; margin-bottom: 1.5rem;">
                يدعم النظام ملفات PDF و DOCX و TXT بحجم أقصى 50 ميجابايت
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        uploaded_file = st.file_uploader(
            "اختر ملف العقد",
            type=['pdf', 'docx', 'doc', 'txt'],
            key=key,
            help="اسحب الملف هنا أو انقر للاختيار"
        )
        
        return uploaded_file
    
    def display_file_info(self, file_result: Dict[str, Any]):
        """Display file information and analysis"""
        if not file_result.get('success'):
            st.error(f"❌ {file_result.get('error', 'خطأ غير معروف')}")
            return
        
        analysis = file_result.get('analysis', {})
        
        # File info card
        st.markdown(f"""
        <div class="analysis-card-enhanced">
            <h4 style="color: #1e3a8a; margin-bottom: 1rem;">📄 معلومات الملف</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div>
                    <strong>اسم الملف:</strong> {file_result.get('filename', 'غير محدد')}<br>
                    <strong>نوع الملف:</strong> {file_result.get('file_type', 'غير محدد').upper()}<br>
                    <strong>حجم الملف:</strong> {analysis.get('file_info', {}).get('size_mb', 0)} ميجابايت
                </div>
                <div>
                    <strong>عدد الكلمات:</strong> {analysis.get('word_count', 0):,}<br>
                    <strong>عدد الأسطر:</strong> {analysis.get('line_count', 0):,}<br>
                    <strong>الصفحات المقدرة:</strong> {analysis.get('estimated_pages', 1)}
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Language detection
        if analysis.get('language_detected'):
            if analysis.get('contains_arabic'):
                st.success("✅ تم اكتشاف نص عربي في الملف")
            else:
                st.info("ℹ️ الملف يحتوي على نص باللغة الإنجليزية")
