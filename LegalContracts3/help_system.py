#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Help and Documentation System for Legal Contract Analysis
Provides comprehensive user guides, tutorials, and documentation
"""

import streamlit as st
from typing import Dict, List, Optional, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HelpSystem:
    """Comprehensive help and documentation system"""
    
    def __init__(self):
        """Initialize help system"""
        self.help_sections = {
            'getting_started': 'البدء السريع',
            'contract_analysis': 'تحليل العقود',
            'templates': 'إدارة القوالب',
            'export': 'تصدير النتائج',
            'settings': 'الإعدادات',
            'troubleshooting': 'حل المشاكل',
            'faq': 'الأسئلة الشائعة',
            'legal_info': 'المعلومات القانونية'
        }
        
        self.tutorials = {
            'first_analysis': 'أول تحليل عقد',
            'upload_file': 'رفع الملفات',
            'use_templates': 'استخدام القوالب',
            'export_results': 'تصدير النتائج',
            'customize_settings': 'تخصيص الإعدادات'
        }
    
    def render_help_page(self):
        """Render main help page"""
        st.markdown("# 📚 المساعدة والدليل")
        
        # Help navigation
        tab1, tab2, tab3, tab4 = st.tabs(["📖 الدليل", "🎥 الدروس", "❓ الأسئلة الشائعة", "📞 الدعم"])
        
        with tab1:
            self._render_documentation()
        
        with tab2:
            self._render_tutorials()
        
        with tab3:
            self._render_faq()
        
        with tab4:
            self._render_support()
    
    def _render_documentation(self):
        """Render comprehensive documentation"""
        st.markdown("### 📖 دليل المستخدم الشامل")
        
        # Documentation sections
        selected_section = st.selectbox(
            "اختر القسم",
            options=list(self.help_sections.keys()),
            format_func=lambda x: self.help_sections[x],
            key="help_section_selector"
        )
        
        if selected_section == 'getting_started':
            self._render_getting_started()
        elif selected_section == 'contract_analysis':
            self._render_contract_analysis_help()
        elif selected_section == 'templates':
            self._render_templates_help()
        elif selected_section == 'export':
            self._render_export_help()
        elif selected_section == 'settings':
            self._render_settings_help()
        elif selected_section == 'troubleshooting':
            self._render_troubleshooting()
        elif selected_section == 'faq':
            self._render_faq()
        elif selected_section == 'legal_info':
            self._render_legal_info()
    
    def _render_getting_started(self):
        """Render getting started guide"""
        st.markdown("## 🚀 البدء السريع")
        
        st.markdown("""
        ### مرحباً بك في محلل العقود القانونية المتقدم!
        
        هذا النظام يساعدك على تحليل العقود القانونية باستخدام الذكاء الاصطناعي المتقدم.
        
        #### الخطوات الأولى:
        
        1. **تسجيل الدخول**
           - استخدم: اسم المستخدم `admin` وكلمة المرور `admin123`
           - أو ادخل كضيف للتجربة
        
        2. **اختيار النظام القانوني**
           - القانون الكويتي (افتراضي)
           - القانون السعودي
        
        3. **رفع العقد أو إدخال النص**
           - يدعم النظام ملفات PDF و DOCX و TXT
           - أو يمكنك لصق النص مباشرة
        
        4. **بدء التحليل**
           - انقر على زر "تحليل العقد"
           - انتظر النتائج (قد يستغرق دقائق قليلة)
        
        5. **مراجعة النتائج**
           - تقييم المخاطر
           - النقاط القانونية المهمة
           - التوصيات للتحسين
        
        6. **تصدير النتائج**
           - تصدير PDF أو Word
           - حفظ للمراجعة لاحقاً
        """)
        
        # Quick start video placeholder
        st.markdown("---")
        st.markdown("### 🎥 فيديو البدء السريع")
        st.info("فيديو تعليمي قادم قريباً...")
        
        # Quick tips
        st.markdown("---")
        st.markdown("### 💡 نصائح سريعة")
        
        tips = [
            "استخدم ملفات واضحة وعالية الجودة للحصول على أفضل النتائج",
            "راجع النظام القانوني المحدد قبل بدء التحليل",
            "احفظ تحليلاتك المهمة للرجوع إليها لاحقاً",
            "استخدم القوالب الجاهزة لتوفير الوقت",
            "راجع الإعدادات لتخصيص النظام حسب احتياجاتك"
        ]
        
        for i, tip in enumerate(tips, 1):
            st.markdown(f"**{i}.** {tip}")
    
    def _render_contract_analysis_help(self):
        """Render contract analysis help"""
        st.markdown("## 🔍 دليل تحليل العقود")
        
        st.markdown("""
        ### كيفية تحليل العقود بفعالية
        
        #### 1. إعداد العقد للتحليل
        
        **تنسيقات الملفات المدعومة:**
        - PDF: الأكثر شيوعاً، يدعم النصوص القابلة للبحث
        - DOCX: ملفات Word الحديثة
        - TXT: الملفات النصية البسيطة
        
        **نصائح لأفضل النتائج:**
        - تأكد من وضوح النص في ملفات PDF
        - تجنب الملفات المصورة (Scanned) إذا أمكن
        - استخدم ملفات بحجم أقل من 50 ميجابايت
        
        #### 2. اختيار النظام القانوني
        
        **القانون الكويتي:**
        - يركز على القوانين والأنظمة الكويتية
        - يتضمن قانون العمل الكويتي
        - يراعي الأحكام الشرعية المعمول بها
        
        **القانون السعودي:**
        - يركز على الأنظمة السعودية
        - يتضمن نظام العمل السعودي
        - يراعي أحكام الشريعة الإسلامية
        
        #### 3. فهم نتائج التحليل
        
        **تقييم المخاطر:**
        - 0-30%: مخاطر منخفضة (أخضر)
        - 31-70%: مخاطر متوسطة (أصفر)
        - 71-100%: مخاطر عالية (أحمر)
        
        **النقاط القانونية:**
        - البنود المهمة في العقد
        - النقاط التي تحتاج مراجعة
        - البنود المفقودة أو الناقصة
        
        **التوصيات:**
        - اقتراحات لتحسين العقد
        - بنود إضافية مقترحة
        - تعديلات لتقليل المخاطر
        """)
        
        # Analysis examples
        st.markdown("---")
        st.markdown("### 📋 أمثلة على التحليل")
        
        with st.expander("مثال: عقد عمل", expanded=False):
            st.markdown("""
            **النقاط المهمة في عقد العمل:**
            - تحديد الراتب والمزايا بوضوح
            - مدة العقد وشروط التجديد
            - ساعات العمل والإجازات
            - شروط إنهاء العقد
            - التزامات الطرفين
            
            **المخاطر الشائعة:**
            - عدم تحديد مدة العقد
            - غموض في تحديد المسؤوليات
            - عدم وجود بند لحل النزاعات
            """)
        
        with st.expander("مثال: عقد توريد", expanded=False):
            st.markdown("""
            **النقاط المهمة في عقد التوريد:**
            - مواصفات السلع أو الخدمات
            - مواعيد التسليم والدفع
            - شروط الجودة والضمان
            - المسؤولية عن التأخير
            - شروط القوة القاهرة
            
            **المخاطر الشائعة:**
            - عدم تحديد مواصفات دقيقة
            - غياب شروط الجزاءات
            - عدم وضوح آلية الدفع
            """)
    
    def _render_templates_help(self):
        """Render templates help"""
        st.markdown("## 📋 دليل إدارة القوالب")
        
        st.markdown("""
        ### استخدام وإدارة قوالب العقود
        
        #### ما هي القوالب؟
        القوالب هي نماذج جاهزة للعقود يمكن استخدامها كنقطة بداية لإنشاء عقود جديدة.
        
        #### فوائد استخدام القوالب:
        - توفير الوقت والجهد
        - ضمان تضمين البنود المهمة
        - الامتثال للمعايير القانونية
        - تقليل الأخطاء والنسيان
        
        #### كيفية استخدام القوالب:
        
        1. **تصفح القوالب المتاحة**
           - انتقل إلى صفحة "إدارة القوالب"
           - استخدم المرشحات للبحث
           - اختر القالب المناسب
        
        2. **معاينة القالب**
           - انقر على "معاينة" لرؤية محتوى القالب
           - راجع البنود والشروط
           - تأكد من ملاءمته لاحتياجاتك
        
        3. **استخدام القالب**
           - انقر على "استخدام القالب"
           - سيتم نقلك إلى صفحة التحليل
           - عدّل النص حسب الحاجة
        
        #### إضافة قوالب جديدة:
        
        **للمديرين فقط:**
        1. انتقل إلى تبويب "إضافة قالب"
        2. أدخل اسم ووصف القالب
        3. اختر الفئة والنظام القانوني
        4. أدخل نص القالب أو ارفع ملف
        5. احفظ القالب
        
        #### فئات القوالب:
        - **عقود العمل**: عقود الموظفين والعمالة
        - **العقود التجارية**: عقود البيع والشراء
        - **عقود العقارات**: عقود الإيجار والبيع العقاري
        - **عقود الخدمات**: عقود تقديم الخدمات
        - **عقود الشراكة**: عقود الشراكات التجارية
        - **عقود التوريد**: عقود توريد السلع
        - **عقود الاستشارات**: عقود الخدمات الاستشارية
        """)
    
    def _render_export_help(self):
        """Render export help"""
        st.markdown("## 📥 دليل تصدير النتائج")
        
        st.markdown("""
        ### كيفية تصدير وحفظ نتائج التحليل
        
        #### تنسيقات التصدير المتاحة:
        
        **1. تصدير PDF**
        - تقرير مهني منسق
        - يحتوي على جميع نتائج التحليل
        - مناسب للطباعة والأرشفة
        - يتضمن شعار الشركة والتاريخ
        
        **2. تصدير Word (DOCX)**
        - ملف قابل للتعديل
        - يمكن إضافة ملاحظات وتعديلات
        - مناسب للمشاركة والتعاون
        - يحافظ على التنسيق الأصلي
        
        #### محتويات التقرير المُصدَّر:
        
        1. **معلومات العقد**
           - اسم الملف وتاريخ التحليل
           - النظام القانوني المستخدم
           - إحصائيات أساسية (عدد الكلمات، الصفحات)
        
        2. **تقييم المخاطر**
           - درجة المخاطر الإجمالية
           - تفصيل أنواع المخاطر
           - مستوى المخاطر (منخفض/متوسط/عالي)
        
        3. **النقاط القانونية**
           - البنود المهمة المحددة
           - النقاط التي تحتاج مراجعة
           - التقييم القانوني لكل نقطة
        
        4. **التوصيات**
           - اقتراحات التحسين
           - البنود المقترح إضافتها
           - الأولويات والتصنيفات
        
        #### نصائح للتصدير:
        - اختر PDF للتقارير النهائية
        - اختر Word للتقارير التي تحتاج تعديل
        - احفظ نسخة احتياطية من التحليلات المهمة
        - راجع التقرير قبل المشاركة
        """)
    
    def _render_tutorials(self):
        """Render interactive tutorials"""
        st.markdown("### 🎥 الدروس التفاعلية")
        
        selected_tutorial = st.selectbox(
            "اختر الدرس",
            options=list(self.tutorials.keys()),
            format_func=lambda x: self.tutorials[x],
            key="tutorial_selector"
        )
        
        if selected_tutorial == 'first_analysis':
            self._render_first_analysis_tutorial()
        elif selected_tutorial == 'upload_file':
            self._render_upload_tutorial()
        elif selected_tutorial == 'use_templates':
            self._render_templates_tutorial()
        elif selected_tutorial == 'export_results':
            self._render_export_tutorial()
        elif selected_tutorial == 'customize_settings':
            self._render_settings_tutorial()
    
    def _render_first_analysis_tutorial(self):
        """Render first analysis tutorial"""
        st.markdown("## 🎯 درس: أول تحليل عقد")
        
        steps = [
            {
                'title': 'الخطوة 1: تسجيل الدخول',
                'content': 'ادخل باستخدام admin/admin123 أو كضيف',
                'image': None
            },
            {
                'title': 'الخطوة 2: انتقل إلى صفحة التحليل',
                'content': 'انقر على "تحليل العقود" من القائمة الجانبية',
                'image': None
            },
            {
                'title': 'الخطوة 3: اختر النظام القانوني',
                'content': 'اختر بين القانون الكويتي أو السعودي',
                'image': None
            },
            {
                'title': 'الخطوة 4: رفع العقد',
                'content': 'ارفع ملف PDF أو DOCX أو الصق النص مباشرة',
                'image': None
            },
            {
                'title': 'الخطوة 5: بدء التحليل',
                'content': 'انقر على "تحليل العقد" وانتظر النتائج',
                'image': None
            },
            {
                'title': 'الخطوة 6: مراجعة النتائج',
                'content': 'راجع تقييم المخاطر والنقاط القانونية والتوصيات',
                'image': None
            }
        ]
        
        for i, step in enumerate(steps, 1):
            with st.expander(f"{step['title']}", expanded=i==1):
                st.markdown(step['content'])
                if step['image']:
                    st.image(step['image'])
    
    def _render_faq(self):
        """Render frequently asked questions"""
        st.markdown("### ❓ الأسئلة الشائعة")
        
        faqs = [
            {
                'question': 'ما هي تنسيقات الملفات المدعومة؟',
                'answer': 'يدعم النظام ملفات PDF و DOCX و TXT بحجم أقصى 50 ميجابايت.'
            },
            {
                'question': 'كم يستغرق تحليل العقد؟',
                'answer': 'عادة ما يستغرق التحليل من 1-3 دقائق حسب طول العقد وتعقيده.'
            },
            {
                'question': 'هل يمكنني حفظ نتائج التحليل؟',
                'answer': 'نعم، يمكنك تصدير النتائج بصيغة PDF أو Word، كما يتم حفظها تلقائياً في حسابك.'
            },
            {
                'question': 'ما الفرق بين القانون الكويتي والسعودي في التحليل؟',
                'answer': 'كل نظام قانوني له معايير وقوانين مختلفة، والنظام يحلل العقد وفقاً للقانون المحدد.'
            },
            {
                'question': 'هل يمكنني استخدام النظام بدون تسجيل دخول؟',
                'answer': 'نعم، يمكنك الدخول كضيف، لكن لن تتمكن من حفظ التحليلات أو الوصول للميزات المتقدمة.'
            },
            {
                'question': 'كيف يمكنني تحسين دقة التحليل؟',
                'answer': 'استخدم ملفات واضحة، اختر النظام القانوني الصحيح، وتأكد من اكتمال نص العقد.'
            },
            {
                'question': 'هل النظام يحفظ العقود المرفوعة؟',
                'answer': 'يتم حفظ العقود بشكل آمن ومشفر، ولا يمكن الوصول إليها إلا من خلال حسابك.'
            },
            {
                'question': 'ماذا لو واجهت مشكلة تقنية؟',
                'answer': 'راجع قسم "حل المشاكل" أو تواصل مع الدعم الفني عبر البريد الإلكتروني.'
            }
        ]
        
        for faq in faqs:
            with st.expander(f"❓ {faq['question']}", expanded=False):
                st.markdown(faq['answer'])
    
    def _render_support(self):
        """Render support information"""
        st.markdown("### 📞 الدعم والتواصل")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            #### 📧 معلومات التواصل
            
            **البريد الإلكتروني:**
            <EMAIL>
            
            **الهاتف:**
            +965 1234 5678
            
            **ساعات العمل:**
            الأحد - الخميس: 8:00 ص - 5:00 م
            
            **العنوان:**
            الكويت، مدينة الكويت
            """)
        
        with col2:
            st.markdown("""
            #### 🔗 روابط مفيدة
            
            **الموقع الرسمي:**
            www.maxbit.com
            
            **التحديثات:**
            تابعنا للحصول على آخر التحديثات
            
            **التدريب:**
            دورات تدريبية متاحة عند الطلب
            
            **الدعم الفني:**
            متاح 24/7 للعملاء المميزين
            """)
        
        # Contact form
        st.markdown("---")
        st.markdown("#### 📝 نموذج التواصل")
        
        with st.form("contact_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                name = st.text_input("الاسم")
                email = st.text_input("البريد الإلكتروني")
            
            with col2:
                subject = st.selectbox(
                    "الموضوع",
                    ["دعم فني", "استفسار عام", "اقتراح تحسين", "شكوى", "أخرى"]
                )
                priority = st.selectbox(
                    "الأولوية",
                    ["منخفضة", "متوسطة", "عالية", "عاجلة"]
                )
            
            message = st.text_area("الرسالة", height=150)
            
            if st.form_submit_button("📤 إرسال الرسالة", type="primary"):
                if name and email and message:
                    st.success("✅ تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.")
                else:
                    st.error("❌ يرجى ملء جميع الحقول المطلوبة")
    
    def render_quick_help(self, context: str = "general"):
        """Render context-sensitive quick help"""
        if context == "analysis":
            st.info("""
            💡 **نصائح سريعة للتحليل:**
            - تأكد من وضوح النص في الملف
            - اختر النظام القانوني المناسب
            - راجع النتائج بعناية قبل اتخاذ القرارات
            """)
        elif context == "upload":
            st.info("""
            💡 **نصائح رفع الملفات:**
            - الحد الأقصى: 50 ميجابايت
            - الصيغ المدعومة: PDF, DOCX, TXT
            - تجنب الملفات المصورة للحصول على أفضل النتائج
            """)
        elif context == "templates":
            st.info("""
            💡 **نصائح القوالب:**
            - استخدم القوالب لتوفير الوقت
            - راجع القالب قبل الاستخدام
            - عدّل القالب حسب احتياجاتك
            """)
    
    def get_help_content(self, topic: str) -> str:
        """Get help content for specific topic"""
        help_content = {
            'risk_score': 'درجة المخاطر تتراوح من 0-100، حيث تشير القيم الأعلى إلى مخاطر أكبر.',
            'legal_points': 'النقاط القانونية هي البنود والشروط المهمة التي تم تحديدها في العقد.',
            'recommendations': 'التوصيات هي اقتراحات لتحسين العقد وتقليل المخاطر.',
            'export': 'يمكنك تصدير النتائج بصيغة PDF للأرشفة أو Word للتعديل.'
        }
        
        return help_content.get(topic, 'لا توجد معلومات متاحة لهذا الموضوع.')
