@echo off
REM Kuwaiti Legal Contract Analysis Application - LM Studio Version
REM محلل العقود القانونية الكويتية - نسخة LM Studio

echo 🏛️  محلل العقود القانونية الكويتية - LM Studio
echo    Kuwaiti Legal Contract Analysis - LM Studio Version
echo ============================================================

REM Check if Python is available
echo 🔍 Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found
    echo    Please install Python 3.8+ from: https://python.org/downloads
    pause
    exit /b 1
)
echo ✅ Python is available

REM Check if dependencies are installed
echo 📦 Checking Python dependencies...
python -c "import streamlit" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Streamlit not found
    echo 📦 Installing dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ Streamlit is installed
)

REM Check if LM Studio is running
echo 🤖 Checking LM Studio connection...
curl -s http://localhost:1234/v1/models >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ LM Studio is running and accessible
    
    REM Try to get model information
    for /f "delims=" %%i in ('curl -s http://localhost:1234/v1/models') do set models_response=%%i
    if defined models_response (
        echo 📋 LM Studio server is responding
    ) else (
        echo ⚠️  LM Studio server response unclear
    )
) else (
    echo ❌ LM Studio is not running or not accessible
    echo.
    echo 🔧 Please follow these steps:
    echo 1. Download LM Studio from: https://lmstudio.ai/
    echo 2. Install and open LM Studio
    echo 3. Download a model (recommended: llama-3.1-8b-instruct^)
    echo 4. Go to 'Local Server' tab
    echo 5. Start the server on port 1234
    echo 6. Run this script again
    echo.
    echo 📖 For detailed instructions, see: LM_STUDIO_SETUP.md
    echo.
    set /p continue="Do you want to continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        exit /b 1
    )
)

REM Start the Streamlit application
echo.
echo 🚀 Starting Streamlit application with LM Studio backend...
echo 🌐 The application will open in your browser at: http://localhost:8501
echo ⏹️  Press Ctrl+C to stop the application
echo.

REM Set environment variables for LM Studio
set AI_BACKEND=lmstudio
set DEFAULT_MODEL=llama-3.1-8b-instruct

REM Start Streamlit
streamlit run app.py --server.port 8501 --server.address localhost

echo.
echo 🛑 Application stopped
pause
