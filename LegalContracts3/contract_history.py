"""
Contract Analysis History Management System
Developed by MAXBIT LLC © 2025
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st

class ContractHistoryManager:
    """Manage contract analysis history and versioning"""
    
    def __init__(self, db_path: str = "contracts.db"):
        self.db_path = db_path
    
    def save_analysis_to_history(self, contract_id: str, analysis_data: Dict[str, Any], 
                                user_id: str, action_type: str = "analysis", 
                                action_description: str = "Contract analyzed") -> str:
        """Save contract analysis to history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get current contract data
            cursor.execute("SELECT * FROM contracts WHERE id = ?", (contract_id,))
            contract = cursor.fetchone()
            
            if not contract:
                return None
            
            # Create history entry
            history_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO contract_history (
                    id, contract_id, version, filename, original_text,
                    analysis_result, contract_type, status, risk_score,
                    user_id, created_at, action_type, action_description
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                history_id,
                contract_id,
                contract[13],  # version
                contract[1],   # filename
                contract[2],   # original_text
                json.dumps(analysis_data),
                contract[4],   # contract_type
                contract[5],   # status
                analysis_data.get('risk_score', 0),
                user_id,
                current_time,
                action_type,
                action_description
            ))
            
            # Update contract version
            new_version = contract[13] + 1
            cursor.execute("""
                UPDATE contracts 
                SET version = ?, analysis_result = ?, updated_at = ?, risk_score = ?
                WHERE id = ?
            """, (
                new_version,
                json.dumps(analysis_data),
                current_time,
                analysis_data.get('risk_score', 0),
                contract_id
            ))
            
            conn.commit()
            conn.close()
            
            return history_id
            
        except Exception as e:
            print(f"Error saving to history: {e}")
            return None
    
    def get_contract_history(self, contract_id: str) -> List[Dict[str, Any]]:
        """Get complete history for a contract"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT h.*, u.full_name as user_name
                FROM contract_history h
                LEFT JOIN users u ON h.user_id = u.id
                WHERE h.contract_id = ?
                ORDER BY h.created_at DESC
            """, (contract_id,))
            
            rows = cursor.fetchall()
            history = []
            
            for row in rows:
                entry = dict(row)
                # Parse analysis result
                if entry.get('analysis_result'):
                    try:
                        entry['analysis_result'] = json.loads(entry['analysis_result'])
                    except:
                        entry['analysis_result'] = {}
                history.append(entry)
            
            conn.close()
            return history
            
        except Exception as e:
            print(f"Error getting contract history: {e}")
            return []
    
    def get_version_comparison(self, contract_id: str, version1: int, version2: int) -> Dict[str, Any]:
        """Compare two versions of a contract"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Get both versions
            cursor.execute("""
                SELECT * FROM contract_history 
                WHERE contract_id = ? AND version IN (?, ?)
                ORDER BY version
            """, (contract_id, version1, version2))
            
            versions = cursor.fetchall()
            conn.close()
            
            if len(versions) != 2:
                return {"error": "Could not find both versions"}
            
            v1, v2 = [dict(v) for v in versions]
            
            # Parse analysis results
            for v in [v1, v2]:
                if v.get('analysis_result'):
                    try:
                        v['analysis_result'] = json.loads(v['analysis_result'])
                    except:
                        v['analysis_result'] = {}
            
            return {
                "version1": v1,
                "version2": v2,
                "changes": self._detect_changes(v1, v2)
            }
            
        except Exception as e:
            print(f"Error comparing versions: {e}")
            return {"error": str(e)}
    
    def _detect_changes(self, v1: Dict, v2: Dict) -> Dict[str, Any]:
        """Detect changes between two versions"""
        changes = {
            "risk_score_change": v2.get('risk_score', 0) - v1.get('risk_score', 0),
            "status_change": v1.get('status') != v2.get('status'),
            "analysis_changes": []
        }
        
        # Compare analysis results
        a1 = v1.get('analysis_result', {})
        a2 = v2.get('analysis_result', {})
        
        # Check for new legal points
        legal1 = a1.get('legal_points', [])
        legal2 = a2.get('legal_points', [])
        
        if len(legal2) > len(legal1):
            changes["analysis_changes"].append(f"Added {len(legal2) - len(legal1)} new legal points")
        
        # Check for new recommendations
        rec1 = a1.get('recommendations', [])
        rec2 = a2.get('recommendations', [])
        
        if len(rec2) > len(rec1):
            changes["analysis_changes"].append(f"Added {len(rec2) - len(rec1)} new recommendations")
        
        return changes
    
    def get_user_analysis_history(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get analysis history for a specific user"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT h.*, c.filename as contract_filename
                FROM contract_history h
                LEFT JOIN contracts c ON h.contract_id = c.id
                WHERE h.user_id = ?
                ORDER BY h.created_at DESC
                LIMIT ?
            """, (user_id, limit))
            
            rows = cursor.fetchall()
            history = []
            
            for row in rows:
                entry = dict(row)
                if entry.get('analysis_result'):
                    try:
                        entry['analysis_result'] = json.loads(entry['analysis_result'])
                    except:
                        entry['analysis_result'] = {}
                history.append(entry)
            
            conn.close()
            return history
            
        except Exception as e:
            print(f"Error getting user history: {e}")
            return []
    
    def get_analysis_statistics(self, user_id: str = None) -> Dict[str, Any]:
        """Get analysis statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            base_query = "SELECT COUNT(*) FROM contract_history"
            params = []
            
            if user_id:
                base_query += " WHERE user_id = ?"
                params.append(user_id)
            
            cursor.execute(base_query, params)
            total_analyses = cursor.fetchone()[0]
            
            # Get analyses by action type
            action_query = base_query.replace("COUNT(*)", "action_type, COUNT(*)")
            action_query += " GROUP BY action_type" if not user_id else " AND action_type IS NOT NULL GROUP BY action_type"
            
            cursor.execute(action_query, params)
            action_stats = dict(cursor.fetchall())
            
            # Get recent activity (last 30 days)
            recent_query = base_query + (" AND" if user_id else " WHERE") + " created_at >= datetime('now', '-30 days')"
            cursor.execute(recent_query, params)
            recent_analyses = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                "total_analyses": total_analyses,
                "action_statistics": action_stats,
                "recent_analyses": recent_analyses
            }
            
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {
                "total_analyses": 0,
                "action_statistics": {},
                "recent_analyses": 0
            }

class ContractHistoryUI:
    """UI components for contract history"""
    
    def __init__(self):
        self.history_manager = ContractHistoryManager()
    
    def display_contract_history(self, contract_id: str):
        """Display contract analysis history"""
        st.markdown("### 📜 تاريخ تحليل العقد")
        
        history = self.history_manager.get_contract_history(contract_id)
        
        if not history:
            st.info("لا يوجد تاريخ تحليل لهذا العقد")
            return
        
        for i, entry in enumerate(history):
            with st.expander(f"الإصدار {entry['version']} - {entry['action_type']} ({entry['created_at'][:10]})"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown(f"**المحلل:** {entry.get('user_name', 'غير معروف')}")
                    st.markdown(f"**نوع الإجراء:** {entry['action_type']}")
                    st.markdown(f"**الوصف:** {entry['action_description']}")
                
                with col2:
                    st.markdown(f"**درجة المخاطر:** {entry.get('risk_score', 0):.1f}")
                    st.markdown(f"**الحالة:** {entry.get('status', 'غير محدد')}")
                    st.markdown(f"**التاريخ:** {entry['created_at']}")
                
                # Show analysis summary
                analysis = entry.get('analysis_result', {})
                if analysis:
                    st.markdown("**ملخص التحليل:**")
                    
                    legal_points = analysis.get('legal_points', [])
                    if legal_points:
                        st.markdown(f"- النقاط القانونية: {len(legal_points)}")
                    
                    recommendations = analysis.get('recommendations', [])
                    if recommendations:
                        st.markdown(f"- التوصيات: {len(recommendations)}")
                
                # Compare with previous version
                if i < len(history) - 1:
                    if st.button(f"مقارنة مع الإصدار السابق", key=f"compare_{entry['id']}"):
                        self._show_version_comparison(contract_id, entry['version'], history[i+1]['version'])
    
    def _show_version_comparison(self, contract_id: str, version1: int, version2: int):
        """Show comparison between two versions"""
        comparison = self.history_manager.get_version_comparison(contract_id, version1, version2)
        
        if "error" in comparison:
            st.error(f"خطأ في المقارنة: {comparison['error']}")
            return
        
        st.markdown("#### 🔍 مقارنة الإصدارات")
        
        changes = comparison['changes']
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"**الإصدار {version2}**")
            v2 = comparison['version2']
            st.markdown(f"- درجة المخاطر: {v2.get('risk_score', 0):.1f}")
            st.markdown(f"- الحالة: {v2.get('status', 'غير محدد')}")
        
        with col2:
            st.markdown(f"**الإصدار {version1}**")
            v1 = comparison['version1']
            st.markdown(f"- درجة المخاطر: {v1.get('risk_score', 0):.1f}")
            st.markdown(f"- الحالة: {v1.get('status', 'غير محدد')}")
        
        # Show changes
        st.markdown("**التغييرات:**")
        
        risk_change = changes['risk_score_change']
        if risk_change != 0:
            direction = "ارتفعت" if risk_change > 0 else "انخفضت"
            st.markdown(f"- درجة المخاطر {direction} بمقدار {abs(risk_change):.1f}")
        
        if changes['status_change']:
            st.markdown(f"- تغيرت الحالة من {v1.get('status')} إلى {v2.get('status')}")
        
        for change in changes['analysis_changes']:
            st.markdown(f"- {change}")
    
    def display_user_history_dashboard(self, user_id: str):
        """Display user's analysis history dashboard"""
        st.markdown("### 📊 لوحة تاريخ التحليل")
        
        # Get statistics
        stats = self.history_manager.get_analysis_statistics(user_id)
        
        # Display metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("إجمالي التحليلات", stats['total_analyses'])
        
        with col2:
            st.metric("التحليلات الأخيرة (30 يوم)", stats['recent_analyses'])
        
        with col3:
            action_stats = stats['action_statistics']
            main_action = max(action_stats.items(), key=lambda x: x[1])[0] if action_stats else "تحليل"
            st.metric("النشاط الأكثر", main_action)
        
        # Get recent history
        recent_history = self.history_manager.get_user_analysis_history(user_id, 20)
        
        if recent_history:
            st.markdown("#### 📋 النشاط الأخير")
            
            for entry in recent_history[:10]:  # Show last 10
                with st.container():
                    col1, col2, col3 = st.columns([3, 2, 2])
                    
                    with col1:
                        st.markdown(f"**{entry.get('contract_filename', 'عقد غير معروف')}**")
                        st.markdown(f"*{entry['action_description']}*")
                    
                    with col2:
                        st.markdown(f"درجة المخاطر: {entry.get('risk_score', 0):.1f}")
                        st.markdown(f"الإصدار: {entry['version']}")
                    
                    with col3:
                        st.markdown(f"{entry['created_at'][:10]}")
                        st.markdown(f"*{entry['action_type']}*")
                
                st.divider()
