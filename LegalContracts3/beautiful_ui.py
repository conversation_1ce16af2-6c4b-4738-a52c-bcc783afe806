#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Beautiful UI Components
Modern, beautiful, and well-organized interface components
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from theme_manager import get_theme_manager, ThemeType
from enhanced_i18n import get_i18n, t
from legal_frameworks import get_legal_framework_manager, LegalSystem

class BeautifulUI:
    """Beautiful UI component manager"""
    
    def __init__(self):
        self.theme_manager = get_theme_manager()
        self.i18n = get_i18n()
        self.legal_manager = get_legal_framework_manager()
    
    def render_header(self):
        """Render beautiful application header"""
        # Apply enhanced CSS with card styling
        self._apply_enhanced_css()

        # Language and theme controls in top bar
        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

        with col1:
            st.markdown(f"""
            <div class="header-card">
                <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0; color: #1e3a8a;">
                    ⚖️ {t('app.title')}
                </h1>
                <p style="font-size: 1.2rem; margin: 0.5rem 0 0 0; color: #64748b; font-weight: 500;">
                    {t('app.subtitle')}
                </p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            self._render_language_selector()

        with col3:
            self._render_theme_selector()

        with col4:
            self._render_legal_system_selector()

        # Enhanced divider
        st.markdown('<div class="enhanced-divider"></div>', unsafe_allow_html=True)

    def _apply_enhanced_css(self):
        """Apply enhanced CSS with beautiful card styling and fonts"""
        st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap');

        /* Global Styles */
        .stApp {
            font-family: 'Inter', 'Cairo', sans-serif;
        }

        /* Header Card */
        .header-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            margin-bottom: 2rem;
        }

        /* Enhanced Divider */
        .enhanced-divider {
            height: 3px;
            background: linear-gradient(90deg, #1e3a8a, #3b82f6, #06b6d4);
            border-radius: 2px;
            margin: 2rem 0;
            box-shadow: 0 2px 10px rgba(30, 58, 138, 0.3);
        }

        /* Metric Card Enhanced */
        .metric-card-enhanced {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card-enhanced:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .metric-card-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3a8a, #3b82f6);
        }

        .metric-icon {
            font-size: 4rem;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
        }

        .metric-content {
            flex: 1;
        }

        .metric-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #475569;
            margin: 0 0 0.5rem 0;
            font-family: 'Cairo', sans-serif;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 800;
            margin: 0;
            line-height: 1;
            font-family: 'Inter', sans-serif;
        }

        .metric-subtitle {
            font-size: 0.9rem;
            color: #64748b;
            margin: 0.5rem 0 0 0;
            font-weight: 500;
        }

        /* Analysis Card Enhanced */
        .analysis-card-enhanced {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            margin: 1rem 0;
        }

        /* Welcome Card */
        .welcome-card {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 3rem;
            border-radius: 25px;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 15px 35px rgba(30, 58, 138, 0.3);
        }

        .welcome-card h1 {
            font-size: 3rem;
            font-weight: 800;
            margin: 0 0 1rem 0;
            font-family: 'Cairo', sans-serif;
        }

        .welcome-card p {
            font-size: 1.3rem;
            font-weight: 500;
            opacity: 0.9;
            margin: 0;
        }

        /* Status Cards */
        .status-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            margin: 0.5rem 0;
            text-align: center;
        }

        /* Enhanced Buttons */
        .stButton > button {
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s ease;
        }

        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Enhanced Selectbox */
        .stSelectbox > div > div {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            font-weight: 500;
        }

        /* Enhanced Text Input */
        .stTextInput > div > div > input {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            font-weight: 500;
            padding: 1rem;
        }

        /* Enhanced File Uploader */
        .stFileUploader > div {
            border-radius: 15px;
            border: 2px dashed #cbd5e1;
            padding: 2rem;
            text-align: center;
        }

        /* Arabic Text Enhancement */
        .arabic-text {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            direction: rtl;
            text-align: right;
        }

        /* Progress Enhancement */
        .stProgress > div > div {
            border-radius: 10px;
            height: 12px;
        }

        /* Metric Enhancement */
        .stMetric {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def _render_language_selector(self):
        """Render language selector"""
        available_languages = self.i18n.get_available_languages()
        current_lang = self.i18n.get_current_language().value
        
        selected_lang = st.selectbox(
            "🌐",
            options=list(available_languages.keys()),
            format_func=lambda x: available_languages[x],
            index=list(available_languages.keys()).index(current_lang),
            key="language_selector",
            help=t('settings.language_settings')
        )
        
        if selected_lang != current_lang:
            from enhanced_i18n import Language
            self.i18n.set_language(Language(selected_lang))
            st.rerun()
    
    def _render_theme_selector(self):
        """Render theme selector"""
        theme_names = self.theme_manager.get_theme_names()
        current_theme = st.session_state.get('theme', ThemeType.LIGHT.value)
        
        selected_theme = st.selectbox(
            "🎨",
            options=list(theme_names.keys()),
            format_func=lambda x: theme_names[x],
            index=list(theme_names.keys()).index(current_theme) if current_theme in theme_names else 0,
            key="theme_selector",
            help=t('settings.theme')
        )
        
        if selected_theme != current_theme:
            self.theme_manager.set_theme(ThemeType(selected_theme))
            st.session_state.theme = selected_theme
            st.rerun()
    
    def _render_legal_system_selector(self):
        """Render legal system selector"""
        available_systems = self.legal_manager.get_available_systems()
        current_system = self.legal_manager.current_system.value

        selected_system = st.selectbox(
            "⚖️",
            options=list(available_systems.keys()),
            format_func=lambda x: available_systems[x],
            index=list(available_systems.keys()).index(current_system),
            key="legal_system_selector",
            help=t('forms.legal_system')
        )

        if selected_system != current_system:
            self.legal_manager.set_legal_system(LegalSystem(selected_system))
            st.success(f"{t('messages.updated')}: {available_systems[selected_system]}")
            st.rerun()

    def _render_sidebar_header(self):
        """Render enhanced sidebar header with logo and branding"""
        import os
        import base64

        # Logo and branding section
        st.markdown("""
        <style>
        .sidebar-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 1.5rem 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
        }

        .sidebar-logo {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-bottom: 0.8rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0.5rem 0 0.3rem 0;
            line-height: 1.3;
            font-family: 'Cairo', sans-serif;
        }

        .sidebar-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        .sidebar-divider {
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            border-radius: 1px;
            margin: 1rem 0;
        }
        </style>
        """, unsafe_allow_html=True)

        # Try to load and display logo
        logo_path = "Legal Contracts /logo/legal-logo.png"
        logo_html = ""

        try:
            if os.path.exists(logo_path):
                with open(logo_path, "rb") as f:
                    logo_data = base64.b64encode(f.read()).decode()
                logo_html = f'<img src="data:image/png;base64,{logo_data}" class="sidebar-logo" alt="Legal Logo">'
            else:
                # Fallback to emoji if logo not found
                logo_html = '<div style="font-size: 2.5rem; margin-bottom: 0.5rem;">⚖️</div>'
        except Exception:
            # Fallback to emoji if any error
            logo_html = '<div style="font-size: 2.5rem; margin-bottom: 0.5rem;">⚖️</div>'

        # Render header
        st.markdown(f"""
        <div class="sidebar-header">
            {logo_html}
            <div class="sidebar-title">محلل العقود القانونية</div>
            <div class="sidebar-subtitle">MAXBIT LLC - نظام متقدم</div>
        </div>
        """, unsafe_allow_html=True)

        # Add separator
        st.markdown('<div class="sidebar-divider"></div>', unsafe_allow_html=True)
    
    def render_navigation_sidebar(self):
        """Render beautiful navigation sidebar with dynamic menu management"""
        with st.sidebar:
            # Enhanced sidebar header with logo and branding
            self._render_sidebar_header()

            # Get menu preferences from database
            from database_manager import DatabaseManager
            db = DatabaseManager()

            # Get current user ID (if logged in)
            current_user_id = st.session_state.get('user_id')

            # Get menu preferences (user-specific or global)
            menu_preferences = db.get_menu_preferences(current_user_id)

            # Organized navigation menu by categories
            menu_categories = {
                "🏠 الصفحة الرئيسية": [
                    ("🏠 الرئيسية", "home", "الصفحة الرئيسية للتطبيق"),
                ],
                "📊 تحليل العقود": [
                    ("🔍 تحليل العقود", "analysis", "تحليل وفحص العقود القانونية"),
                    ("📈 تقييم المخاطر", "risk", "تقييم مخاطر العقود"),
                    ("📋 التقارير", "reports", "إنشاء وعرض التقارير"),
                    ("💡 الرؤى والتحليلات", "insights", "رؤى ذكية حول العقود"),
                ],
                "🤖 الذكاء الاصطناعي": [
                    ("🤖 مولد العقود الذكي", "contract_generator", "إنشاء عقود بالذكاء الاصطناعي"),
                    ("✅ فاحص الامتثال", "compliance_checker", "فحص الامتثال القانوني"),
                    ("🎯 القوالب المخصصة", "custom_prompts", "قوالب تحليل مخصصة"),
                    ("📋 مكتبة البنود القانونية", "clause_library", "مكتبة البنود والأحكام"),
                ],
                "📚 إدارة البيانات": [
                    ("🗄️ قاعدة البيانات", "database", "إدارة قاعدة بيانات العقود"),
                    ("📄 القوالب", "templates", "قوالب العقود"),
                    ("📜 السجلات", "history", "سجل التحليلات السابقة"),
                    ("📊 الإحصائيات", "statistics", "إحصائيات وتحليلات"),
                ],
                "🔗 المشاركة والتعاون": [
                    ("🔗 المشاركة والتصدير", "sharing", "مشاركة وتصدير التحليلات"),
                    ("👥 التعاون", "collaboration", "العمل التعاوني"),
                    ("📚 المبادئ التوجيهية", "guidelines", "دليل الاستخدام"),
                ],
                "⚙️ الإدارة والإعدادات": [
                    ("👥 إدارة المستخدمين", "users", "إدارة حسابات المستخدمين"),
                    ("⚙️ الإعدادات", "settings", "إعدادات التطبيق"),
                    ("🔧 المراقبة", "monitoring", "مراقبة النظام"),
                    ("🚀 المتقدم", "advanced", "الميزات المتقدمة"),
                ]
            }

            selected_page = st.session_state.get('selected_page', 'home')

            # Render categorized menu with enhanced styling and collapsible sections
            for category_name, items in menu_categories.items():
                # Use expander for collapsible categories
                with st.expander(category_name, expanded=True):
                    # Add category description
                    category_descriptions = {
                        "🏠 الصفحة الرئيسية": "الصفحة الرئيسية ولوحة التحكم",
                        "📊 تحليل العقود": "أدوات تحليل وفحص العقود القانونية",
                        "🤖 الذكاء الاصطناعي": "الميزات المدعومة بالذكاء الاصطناعي",
                        "📚 إدارة البيانات": "إدارة وتنظيم بيانات العقود",
                        "🔗 المشاركة والتعاون": "أدوات المشاركة والعمل التعاوني",
                        "⚙️ الإدارة والإعدادات": "إعدادات النظام والإدارة"
                    }

                    st.caption(category_descriptions.get(category_name, ""))

                    # Category items with improved layout
                    for label, page_key, description in items:
                        # Check if this menu item should be visible
                        if not menu_preferences.get(page_key, True):
                            continue

                        is_selected = selected_page == page_key

                        # Enhanced styling for menu items
                        if is_selected:
                            # Selected item styling with modern design
                            st.markdown(f"""
                            <div style="
                                background: linear-gradient(135deg, #28a745, #20c997);
                                color: white;
                                padding: 12px;
                                border-radius: 8px;
                                margin: 6px 0;
                                border-left: 4px solid #155724;
                                box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
                                transform: translateX(2px);
                            ">
                                <strong>✨ {label}</strong><br>
                                <small style="opacity: 0.95; font-style: italic;">{description}</small>
                            </div>
                            """, unsafe_allow_html=True)
                        else:
                            # Regular item with modern button styling
                            if st.button(
                                f"{label}",
                                key=f"nav_{page_key}",
                                use_container_width=True,
                                help=description,
                                type="secondary"
                            ):
                                st.session_state.selected_page = page_key
                                st.rerun()

                            # Subtle description
                            st.markdown(f"""
                            <div style="
                                color: #6c757d;
                                font-size: 0.85em;
                                margin: -8px 0 8px 12px;
                                font-style: italic;
                            ">
                                {description}
                            </div>
                            """, unsafe_allow_html=True)
            
            # System status indicator
            st.markdown("---")
            self._render_system_status()
    
    def _render_system_status(self):
        """Render system status in sidebar"""
        st.subheader(t('nav.monitoring'))

        # System status indicators
        st.success("🟢 AI Backend")
        st.success("🟢 Database")
        st.info(f"🔵 {self.legal_manager.get_system_name()}")
    
    def render_metric_card(self, title: str, value: str, icon: str = "",
                          color: str = "primary", change: Optional[str] = None):
        """Render clean metric card without icons"""
        # Clean metric without icons
        st.metric(
            label=title,
            value=value,
            delta=change if change else None
        )
    
    def render_analysis_card(self, contract_data: Dict[str, Any]):
        """Render compact contract analysis card"""
        risk_score = contract_data.get('risk_score', 0)

        # Determine risk color without icons
        if risk_score >= 70:
            risk_color = "🔴"
        elif risk_score >= 40:
            risk_color = "🟡"
        else:
            risk_color = "🟢"

        # Compact card design
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

            with col1:
                st.markdown(f"**{contract_data.get('title', 'عقد غير محدد')}**")
                st.caption(contract_data.get('date', 'تاريخ غير محدد'))

            with col2:
                st.caption("المخاطر")
                st.markdown(f"{risk_color} **{risk_score}%**")

            with col3:
                st.caption("البنود")
                st.markdown(f"**{contract_data.get('clauses', 15)}**")

            with col4:
                st.caption("المشاكل")
                st.markdown(f"**{contract_data.get('issues', 2)}**")

        st.markdown("")  # Small spacing instead of separator
    
    def _get_risk_color(self, risk_score: int) -> str:
        """Get color based on risk score"""
        if risk_score >= 70:
            return "var(--error-color)"
        elif risk_score >= 40:
            return "var(--warning-color)"
        else:
            return "var(--success-color)"
    
    def render_progress_indicator(self, progress: int, title: str, subtitle: str = ""):
        """Render beautiful progress indicator using Streamlit components"""
        with st.container():
            st.subheader(title)

            # Create a progress bar
            st.progress(progress / 100)

            # Display percentage
            col1, col2, col3 = st.columns([1, 1, 1])
            with col2:
                st.metric("", f"{progress}%")

            if subtitle:
                st.caption(subtitle)
    
    def render_action_buttons(self, buttons: List[Dict[str, Any]]):
        """Render beautiful action buttons"""
        cols = st.columns(len(buttons))
        
        for i, button in enumerate(buttons):
            with cols[i]:
                if st.button(
                    button['label'],
                    key=button.get('key', f"btn_{i}"),
                    type=button.get('type', 'secondary'),
                    use_container_width=True,
                    help=button.get('help', '')
                ):
                    if 'callback' in button:
                        button['callback']()
    
    def render_footer(self):
        """Render beautiful footer"""
        st.divider()

        # Center the footer content
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown(f"**© 2025 {t('app.company')} - {t('app.title')}**")
            st.caption("<EMAIL> | +1 626 509 0918 | maxbit.net")

# Global instance
beautiful_ui = BeautifulUI()

def get_beautiful_ui() -> BeautifulUI:
    """Get global beautiful UI instance"""
    return beautiful_ui
