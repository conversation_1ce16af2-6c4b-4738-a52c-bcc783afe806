@echo off
REM Kuwaiti Legal Contract Analysis Application Runner
REM محلل العقود القانونية الكويتية

echo ========================================================
echo 🏛️  محلل العقود القانونية الكويتية
echo    Kuwaiti Legal Contract Analysis Application
echo ========================================================
echo.

REM Check if Python is installed
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found
    echo    Please install Python 3.8+ from: https://python.org/downloads
    pause
    exit /b 1
)
echo ✅ Python is installed

REM Check if Ollama is running
echo 🔍 Checking Ollama status...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Ollama is not running
    
    REM Check if Ollama is installed
    ollama --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Ollama not found
        echo    Please install Ollama from: https://ollama.ai/download
        pause
        exit /b 1
    )
    
    echo 🚀 Starting Ollama...
    start /b ollama serve
    
    echo ⏳ Waiting for Ollama to start...
    timeout /t 10 /nobreak >nul
    
    REM Check if Ollama started successfully
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Failed to start Ollama
        echo    Please check Ollama installation
        pause
        exit /b 1
    )
    echo ✅ Ollama started successfully
) else (
    echo ✅ Ollama is running
)

REM Check if required model is available
echo 🤖 Checking for Llama 3.1 model...
curl -s http://localhost:11434/api/tags | findstr "llama3.1:8b" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Llama 3.1 8B model not found
    set /p download="🔄 Would you like to download it now? (y/n): "
    if /i "%download%"=="y" (
        echo 📥 Downloading Llama 3.1 8B model...
        ollama pull llama3.1:8b
        if %errorlevel% equ 0 (
            echo ✅ Model downloaded successfully
        ) else (
            echo ❌ Failed to download model
            echo    You can download it later with: ollama pull llama3.1:8b
        )
    ) else (
        echo ⚠️  You can download the model later with: ollama pull llama3.1:8b
    )
) else (
    echo ✅ Llama 3.1 8B model is available
)

REM Check if Python dependencies are installed
echo 📦 Checking Python dependencies...
python -c "import streamlit" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Streamlit not found
    echo 📦 Installing dependencies...
    pip install -r requirements.txt
    if %errorlevel% equ 0 (
        echo ✅ Dependencies installed successfully
    ) else (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Streamlit is installed
)

REM Start the Streamlit application
echo.
echo 🚀 Starting Streamlit application...
echo 🌐 The application will open in your browser at: http://localhost:8501
echo ⏹️  Press Ctrl+C to stop the application
echo.

REM Start Streamlit
streamlit run app.py

echo.
echo ✅ Application stopped
pause
