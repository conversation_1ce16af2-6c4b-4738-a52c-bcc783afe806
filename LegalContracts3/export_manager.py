#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Export Manager for Contract Analysis Results
Handles PDF and Word export with professional formatting
"""

import io
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExportManager:
    """Handles export of analysis results to PDF and Word formats"""
    
    def __init__(self):
        """Initialize export manager"""
        self.supported_formats = ['pdf', 'docx']
        self.company_name = "MAXBIT LLC"
        self.app_name = "محلل العقود القانونية المتقدم"
    
    def export_analysis(self, analysis_data: Dict[str, Any], format_type: str = 'pdf') -> bytes:
        """Export analysis results to specified format"""
        try:
            if format_type.lower() == 'pdf':
                return self._export_to_pdf(analysis_data)
            elif format_type.lower() == 'docx':
                return self._export_to_docx(analysis_data)
            else:
                raise ValueError(f"Unsupported export format: {format_type}")
                
        except Exception as e:
            logger.error(f"Export error: {e}")
            raise
    
    def _export_to_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Export analysis to PDF format"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # Create PDF buffer
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Build story
            story = []
            styles = getSampleStyleSheet()
            
            # Custom styles for Arabic text
            arabic_style = ParagraphStyle(
                'Arabic',
                parent=styles['Normal'],
                fontName='Helvetica',
                fontSize=12,
                alignment=2,  # Right alignment for Arabic
                spaceAfter=12
            )
            
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Title'],
                fontName='Helvetica-Bold',
                fontSize=18,
                alignment=1,  # Center alignment
                spaceAfter=20
            )
            
            # Title
            story.append(Paragraph(self.app_name, title_style))
            story.append(Paragraph(f"تقرير تحليل العقد - {datetime.now().strftime('%Y-%m-%d')}", arabic_style))
            story.append(Spacer(1, 20))
            
            # Executive Summary
            if analysis_data.get('summary'):
                summary = analysis_data['summary']
                story.append(Paragraph("الملخص التنفيذي", title_style))
                story.append(Paragraph(f"نوع العقد: {summary.get('contract_type', 'غير محدد')}", arabic_style))
                story.append(Paragraph(f"الملخص: {summary.get('executive_summary', 'غير متوفر')}", arabic_style))
                story.append(Spacer(1, 15))
            
            # Risk Assessment
            risk_score = analysis_data.get('risk_score', 0)
            risk_level = "منخفض" if risk_score < 30 else "متوسط" if risk_score < 70 else "عالي"
            
            story.append(Paragraph("تقييم المخاطر", title_style))
            
            risk_data = [
                ['المؤشر', 'القيمة'],
                ['درجة المخاطر الإجمالية', f"{risk_score}%"],
                ['مستوى المخاطر', risk_level],
                ['عدد البنود', str(analysis_data.get('clauses', 0))],
                ['عدد المشاكل المحددة', str(analysis_data.get('issues', 0))],
                ['عدد الصفحات', str(analysis_data.get('pages', 1))]
            ]
            
            risk_table = Table(risk_data, colWidths=[2*inch, 2*inch])
            risk_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(risk_table)
            story.append(Spacer(1, 20))
            
            # Legal Points
            if analysis_data.get('legal_points'):
                story.append(Paragraph("النقاط القانونية المحددة", title_style))
                
                for i, point in enumerate(analysis_data['legal_points'][:10], 1):  # Limit to 10 points
                    if isinstance(point, dict):
                        point_text = f"{i}. {point.get('title', point.get('point', 'نقطة قانونية'))}"
                        if point.get('description'):
                            point_text += f" - {point.get('description')}"
                    else:
                        point_text = f"{i}. {point}"
                    story.append(Paragraph(point_text, arabic_style))
                
                story.append(Spacer(1, 15))
            
            # Recommendations
            if analysis_data.get('recommendations'):
                story.append(Paragraph("التوصيات", title_style))
                
                for i, rec in enumerate(analysis_data['recommendations'][:10], 1):  # Limit to 10 recommendations
                    if isinstance(rec, dict):
                        rec_text = f"{i}. {rec.get('title', 'توصية غير محددة')}"
                        priority = rec.get('priority', 'غير محدد')
                        rec_text += f" (الأولوية: {priority})"
                    else:
                        rec_text = f"{i}. {rec}"
                    
                    story.append(Paragraph(rec_text, arabic_style))
                
                story.append(Spacer(1, 15))
            
            # Footer
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontName='Helvetica',
                fontSize=10,
                alignment=1,
                textColor=colors.grey
            )
            
            story.append(Paragraph(f"تم إنشاء هذا التقرير بواسطة {self.company_name}", footer_style))
            story.append(Paragraph(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", footer_style))
            
            # Build PDF
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
            
        except ImportError:
            raise ImportError("مكتبة ReportLab غير متوفرة. يرجى تثبيت reportlab")
    
    def _export_to_docx(self, analysis_data: Dict[str, Any]) -> bytes:
        """Export analysis to Word DOCX format"""
        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.shared import RGBColor
            
            # Create document
            doc = Document()
            
            # Title
            title = doc.add_heading(self.app_name, 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            subtitle = doc.add_heading(f"تقرير تحليل العقد - {datetime.now().strftime('%Y-%m-%d')}", level=1)
            subtitle.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # Executive Summary
            if analysis_data.get('summary'):
                summary = analysis_data['summary']
                doc.add_heading('الملخص التنفيذي', level=2).alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                p = doc.add_paragraph()
                p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                p.add_run(f"نوع العقد: {summary.get('contract_type', 'غير محدد')}\n")
                p.add_run(f"الملخص: {summary.get('executive_summary', 'غير متوفر')}")
            
            # Risk Assessment
            doc.add_heading('تقييم المخاطر', level=2).alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            risk_score = analysis_data.get('risk_score', 0)
            risk_level = "منخفض" if risk_score < 30 else "متوسط" if risk_score < 70 else "عالي"
            
            # Create risk table
            table = doc.add_table(rows=6, cols=2)
            table.style = 'Table Grid'
            
            # Table headers
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = 'المؤشر'
            hdr_cells[1].text = 'القيمة'
            
            # Table data
            risk_data = [
                ['درجة المخاطر الإجمالية', f"{risk_score}%"],
                ['مستوى المخاطر', risk_level],
                ['عدد البنود', str(analysis_data.get('clauses', 0))],
                ['عدد المشاكل المحددة', str(analysis_data.get('issues', 0))],
                ['عدد الصفحات', str(analysis_data.get('pages', 1))]
            ]
            
            for i, (key, value) in enumerate(risk_data, 1):
                row_cells = table.rows[i].cells
                row_cells[0].text = key
                row_cells[1].text = value
            
            # Legal Points
            if analysis_data.get('legal_points'):
                doc.add_heading('النقاط القانونية المحددة', level=2).alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                for i, point in enumerate(analysis_data['legal_points'][:10], 1):
                    p = doc.add_paragraph(style='List Number')
                    p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    if isinstance(point, dict):
                        point_text = point.get('title', point.get('point', 'نقطة قانونية'))
                        if point.get('description'):
                            point_text += f" - {point.get('description')}"
                    else:
                        point_text = str(point)
                    p.add_run(point_text)
            
            # Recommendations
            if analysis_data.get('recommendations'):
                doc.add_heading('التوصيات', level=2).alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                for i, rec in enumerate(analysis_data['recommendations'][:10], 1):
                    p = doc.add_paragraph(style='List Number')
                    p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    
                    if isinstance(rec, dict):
                        rec_text = rec.get('title', 'توصية غير محددة')
                        priority = rec.get('priority', 'غير محدد')
                        p.add_run(f"{rec_text} (الأولوية: {priority})")
                    else:
                        p.add_run(str(rec))
            
            # Footer
            doc.add_page_break()
            footer_para = doc.add_paragraph()
            footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            footer_run = footer_para.add_run(f"تم إنشاء هذا التقرير بواسطة {self.company_name}\n")
            footer_run.font.color.rgb = RGBColor(128, 128, 128)
            footer_run = footer_para.add_run(f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            footer_run.font.color.rgb = RGBColor(128, 128, 128)
            
            # Save to buffer
            buffer = io.BytesIO()
            doc.save(buffer)
            buffer.seek(0)
            return buffer.getvalue()
            
        except ImportError:
            raise ImportError("مكتبة python-docx غير متوفرة. يرجى تثبيت python-docx")
    
    def render_export_options(self, analysis_data: Dict[str, Any]):
        """Render export options in Streamlit"""
        if not analysis_data:
            st.warning("لا توجد نتائج تحليل للتصدير")
            return
        
        st.markdown("---")
        st.markdown("### 📥 تصدير النتائج")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📄 تصدير PDF", type="secondary", use_container_width=True):
                try:
                    with st.spinner("إنشاء ملف PDF..."):
                        pdf_data = self.export_analysis(analysis_data, 'pdf')
                        
                        filename = f"contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
                        
                        st.download_button(
                            label="📥 تحميل PDF",
                            data=pdf_data,
                            file_name=filename,
                            mime="application/pdf",
                            use_container_width=True
                        )
                        
                        st.success("✅ تم إنشاء ملف PDF بنجاح!")
                        
                except Exception as e:
                    st.error(f"❌ خطأ في إنشاء PDF: {str(e)}")
        
        with col2:
            if st.button("📝 تصدير Word", type="secondary", use_container_width=True):
                try:
                    with st.spinner("إنشاء ملف Word..."):
                        docx_data = self.export_analysis(analysis_data, 'docx')
                        
                        filename = f"contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
                        
                        st.download_button(
                            label="📥 تحميل Word",
                            data=docx_data,
                            file_name=filename,
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            use_container_width=True
                        )
                        
                        st.success("✅ تم إنشاء ملف Word بنجاح!")
                        
                except Exception as e:
                    st.error(f"❌ خطأ في إنشاء Word: {str(e)}")
