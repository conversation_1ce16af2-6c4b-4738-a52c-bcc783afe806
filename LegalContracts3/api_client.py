"""
API Client for Kuwaiti Legal Contract Analysis
Developed by MAXBIT LLC © 2025
"""

import requests
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import os

class ContractAnalysisAPIClient:
    """Client for interacting with the Contract Analysis API"""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_version: str = "v1"):
        self.base_url = base_url.rstrip('/')
        self.api_version = api_version
        self.api_base = f"{self.base_url}/api/{self.api_version}"
        self.access_token = None
        self.session = requests.Session()
        
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers with authentication"""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response"""
        try:
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            error_detail = "Unknown error"
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", str(e))
            except:
                error_detail = str(e)
            raise Exception(f"API Error: {error_detail}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request Error: {str(e)}")
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate with the API"""
        url = f"{self.api_base}/auth/login"
        data = {"username": username, "password": password}
        
        response = self.session.post(url, json=data)
        result = self._handle_response(response)
        
        self.access_token = result["access_token"]
        return result
    
    def register_user(self, username: str, password: str, email: str, 
                     full_name: str, role: str = "client") -> Dict[str, Any]:
        """Register new user (admin only)"""
        url = f"{self.api_base}/auth/register"
        data = {
            "username": username,
            "password": password,
            "email": email,
            "full_name": full_name,
            "role": role
        }
        
        response = self.session.post(url, json=data, headers=self._get_headers())
        return self._handle_response(response)
    
    def analyze_contract_text(self, contract_text: str, title: str, 
                            contract_type: str = "other") -> Dict[str, Any]:
        """Analyze contract text"""
        url = f"{self.api_base}/contracts/analyze"
        data = {
            "contract_text": contract_text,
            "title": title,
            "contract_type": contract_type
        }
        
        response = self.session.post(url, json=data, headers=self._get_headers())
        return self._handle_response(response)
    
    def upload_contract_file(self, file_path: str, title: str, 
                           contract_type: str = "other") -> Dict[str, Any]:
        """Upload and analyze contract file"""
        url = f"{self.api_base}/contracts/upload"
        
        with open(file_path, 'rb') as file:
            files = {'file': (os.path.basename(file_path), file)}
            data = {'title': title, 'contract_type': contract_type}
            
            # Remove Content-Type header for file upload
            headers = self._get_headers()
            headers.pop('Content-Type', None)
            
            response = self.session.post(url, files=files, data=data, headers=headers)
            return self._handle_response(response)
    
    def list_contracts(self, limit: int = 50, offset: int = 0, 
                      contract_type: Optional[str] = None, 
                      status: Optional[str] = None) -> List[Dict[str, Any]]:
        """List contracts"""
        url = f"{self.api_base}/contracts"
        params = {"limit": limit, "offset": offset}
        
        if contract_type:
            params["contract_type"] = contract_type
        if status:
            params["status"] = status
        
        response = self.session.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)
    
    def get_contract(self, contract_id: str) -> Dict[str, Any]:
        """Get specific contract"""
        url = f"{self.api_base}/contracts/{contract_id}"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)
    
    def add_comment(self, contract_id: str, content: str, 
                   comment_type: str = "general", 
                   section: Optional[str] = None) -> Dict[str, Any]:
        """Add comment to contract"""
        url = f"{self.api_base}/contracts/{contract_id}/comments"
        data = {
            "contract_id": contract_id,
            "content": content,
            "comment_type": comment_type,
            "section": section
        }
        
        response = self.session.post(url, json=data, headers=self._get_headers())
        return self._handle_response(response)
    
    def get_comments(self, contract_id: str) -> Dict[str, Any]:
        """Get contract comments"""
        url = f"{self.api_base}/contracts/{contract_id}/comments"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)
    
    def list_templates(self, category: Optional[str] = None, 
                      complexity: Optional[str] = None) -> Dict[str, Any]:
        """List available templates"""
        url = f"{self.api_base}/templates"
        params = {}
        
        if category:
            params["category"] = category
        if complexity:
            params["complexity"] = complexity
        
        response = self.session.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)
    
    def generate_from_template(self, template_id: str, 
                             variables: Dict[str, str]) -> Dict[str, Any]:
        """Generate contract from template"""
        url = f"{self.api_base}/templates/{template_id}/generate"
        data = {
            "template_id": template_id,
            "variables": variables
        }
        
        response = self.session.post(url, json=data, headers=self._get_headers())
        return self._handle_response(response)
    
    def get_executive_report(self, days: int = 30) -> Dict[str, Any]:
        """Get executive summary report"""
        url = f"{self.api_base}/reports/executive"
        params = {"days": days}
        
        response = self.session.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)
    
    def get_risk_report(self) -> Dict[str, Any]:
        """Get risk analysis report"""
        url = f"{self.api_base}/reports/risk"
        response = self.session.get(url, headers=self._get_headers())
        return self._handle_response(response)
    
    def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        url = f"{self.api_base}/health"
        response = self.session.get(url)
        return self._handle_response(response)

class ContractAnalysisSDK:
    """High-level SDK for Contract Analysis API"""
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 username: str = None, password: str = None):
        self.client = ContractAnalysisAPIClient(base_url)
        if username and password:
            self.login(username, password)
    
    def login(self, username: str, password: str) -> bool:
        """Login to the API"""
        try:
            result = self.client.login(username, password)
            return True
        except Exception as e:
            print(f"Login failed: {e}")
            return False
    
    def analyze_contract(self, contract_input: Union[str, bytes], 
                        title: str, contract_type: str = "other") -> Optional[Dict[str, Any]]:
        """Analyze contract (text or file path)"""
        try:
            if isinstance(contract_input, str):
                if os.path.isfile(contract_input):
                    # File path
                    return self.client.upload_contract_file(contract_input, title, contract_type)
                else:
                    # Text content
                    return self.client.analyze_contract_text(contract_input, title, contract_type)
            else:
                raise ValueError("Invalid contract input type")
        except Exception as e:
            print(f"Analysis failed: {e}")
            return None
    
    def get_my_contracts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get user's contracts"""
        try:
            return self.client.list_contracts(limit=limit)
        except Exception as e:
            print(f"Failed to get contracts: {e}")
            return []
    
    def collaborate_on_contract(self, contract_id: str, comment: str, 
                              comment_type: str = "general") -> bool:
        """Add comment to contract"""
        try:
            self.client.add_comment(contract_id, comment, comment_type)
            return True
        except Exception as e:
            print(f"Failed to add comment: {e}")
            return False
    
    def generate_contract_from_template(self, template_id: str, 
                                      **variables) -> Optional[str]:
        """Generate contract from template"""
        try:
            result = self.client.generate_from_template(template_id, variables)
            return result["data"]["content"]
        except Exception as e:
            print(f"Failed to generate contract: {e}")
            return None
    
    def get_dashboard_data(self, days: int = 30) -> Dict[str, Any]:
        """Get dashboard data"""
        try:
            executive_report = self.client.get_executive_report(days)
            risk_report = self.client.get_risk_report()
            
            return {
                "executive": executive_report["data"],
                "risk": risk_report["data"],
                "period": f"{days} days"
            }
        except Exception as e:
            print(f"Failed to get dashboard data: {e}")
            return {}

# Example usage functions
def example_basic_usage():
    """Example of basic API usage"""
    # Initialize SDK
    sdk = ContractAnalysisSDK()
    
    # Login
    if sdk.login("admin", "admin123"):
        print("✅ Logged in successfully")
        
        # Analyze contract text
        contract_text = """
        This Employment Agreement is entered into between ABC Company and John Doe.
        The employee shall work as a Software Developer for a salary of 1000 KWD per month.
        """
        
        result = sdk.analyze_contract(
            contract_text, 
            "Sample Employment Contract", 
            "employment"
        )
        
        if result:
            print("✅ Contract analyzed successfully")
            print(f"Risk Score: {result['data']['risk_score']}")
            
            # Add comment
            contract_id = result['data']['contract_id']
            sdk.collaborate_on_contract(
                contract_id, 
                "This contract looks good but needs salary review",
                "suggestion"
            )
            print("✅ Comment added successfully")
        
        # Get dashboard data
        dashboard = sdk.get_dashboard_data(30)
        if dashboard:
            print("✅ Dashboard data retrieved")
            print(f"Total contracts: {dashboard['executive']['total_contracts']}")
    
    else:
        print("❌ Login failed")

def example_template_usage():
    """Example of template usage"""
    sdk = ContractAnalysisSDK()
    
    if sdk.login("admin", "admin123"):
        # List templates
        templates = sdk.client.list_templates()
        print(f"Available templates: {len(templates['data'])}")
        
        # Use first template if available
        if templates['data']:
            template = templates['data'][0]
            template_id = template['id']
            
            # Generate contract
            contract_content = sdk.generate_contract_from_template(
                template_id,
                employer_name="MAXBIT LLC",
                employee_name="Ahmed Al-Kuwaiti",
                basic_salary="1200",
                start_date="2025-01-01"
            )
            
            if contract_content:
                print("✅ Contract generated from template")
                print(contract_content[:200] + "...")

if __name__ == "__main__":
    print("🚀 Contract Analysis API Client Examples")
    print("=" * 50)
    
    print("\n1. Basic Usage Example:")
    example_basic_usage()
    
    print("\n2. Template Usage Example:")
    example_template_usage()
