"""
Collaboration Tools - Comments & Annotations System
Developed by MAXBIT LLC © 2025
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st

class CommentType:
    """Comment type definitions"""
    GENERAL = "general"
    LEGAL_ISSUE = "legal_issue"
    SUGGESTION = "suggestion"
    QUESTION = "question"
    APPROVAL = "approval"
    REJECTION = "rejection"

class CommentStatus:
    """Comment status definitions"""
    OPEN = "open"
    RESOLVED = "resolved"
    IN_PROGRESS = "in_progress"

class CollaborationManager:
    """Manage comments and annotations for contracts"""
    
    def __init__(self, comments_file: str = "comments.json"):
        self.comments_file = comments_file
        self.comments = self._load_comments()
    
    def _load_comments(self) -> Dict[str, Any]:
        """Load comments from file"""
        if os.path.exists(self.comments_file):
            try:
                with open(self.comments_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_comments(self):
        """Save comments to file"""
        with open(self.comments_file, 'w', encoding='utf-8') as f:
            json.dump(self.comments, f, ensure_ascii=False, indent=2)
    
    def add_comment(self, contract_id: str, user_id: str, user_name: str, 
                   content: str, comment_type: str = CommentType.GENERAL,
                   section: str = None, line_number: int = None) -> str:
        """Add a new comment to a contract"""
        comment_id = str(uuid.uuid4())
        
        if contract_id not in self.comments:
            self.comments[contract_id] = []
        
        comment = {
            "id": comment_id,
            "user_id": user_id,
            "user_name": user_name,
            "content": content,
            "type": comment_type,
            "status": CommentStatus.OPEN,
            "section": section,
            "line_number": line_number,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "replies": []
        }
        
        self.comments[contract_id].append(comment)
        self._save_comments()
        return comment_id
    
    def add_reply(self, contract_id: str, comment_id: str, user_id: str, 
                 user_name: str, content: str) -> str:
        """Add a reply to an existing comment"""
        reply_id = str(uuid.uuid4())
        
        if contract_id in self.comments:
            for comment in self.comments[contract_id]:
                if comment["id"] == comment_id:
                    reply = {
                        "id": reply_id,
                        "user_id": user_id,
                        "user_name": user_name,
                        "content": content,
                        "created_at": datetime.now().isoformat()
                    }
                    comment["replies"].append(reply)
                    comment["updated_at"] = datetime.now().isoformat()
                    self._save_comments()
                    return reply_id
        
        return None
    
    def update_comment_status(self, contract_id: str, comment_id: str, 
                            status: str, user_id: str) -> bool:
        """Update comment status"""
        if contract_id in self.comments:
            for comment in self.comments[contract_id]:
                if comment["id"] == comment_id:
                    comment["status"] = status
                    comment["updated_at"] = datetime.now().isoformat()
                    comment["resolved_by"] = user_id if status == CommentStatus.RESOLVED else None
                    self._save_comments()
                    return True
        return False
    
    def get_comments(self, contract_id: str) -> List[Dict[str, Any]]:
        """Get all comments for a contract"""
        return self.comments.get(contract_id, [])
    
    def get_comment_statistics(self, contract_id: str = None) -> Dict[str, Any]:
        """Get comment statistics"""
        if contract_id:
            comments = self.comments.get(contract_id, [])
        else:
            comments = []
            for contract_comments in self.comments.values():
                comments.extend(contract_comments)
        
        total_comments = len(comments)
        open_comments = len([c for c in comments if c["status"] == CommentStatus.OPEN])
        resolved_comments = len([c for c in comments if c["status"] == CommentStatus.RESOLVED])
        
        # Count by type
        type_counts = {}
        for comment_type in [CommentType.GENERAL, CommentType.LEGAL_ISSUE, 
                           CommentType.SUGGESTION, CommentType.QUESTION, 
                           CommentType.APPROVAL, CommentType.REJECTION]:
            type_counts[comment_type] = len([c for c in comments if c["type"] == comment_type])
        
        return {
            "total_comments": total_comments,
            "open_comments": open_comments,
            "resolved_comments": resolved_comments,
            "in_progress_comments": len([c for c in comments if c["status"] == CommentStatus.IN_PROGRESS]),
            "type_counts": type_counts,
            "total_replies": sum(len(c["replies"]) for c in comments)
        }

class AnnotationManager:
    """Manage text annotations for contracts"""
    
    def __init__(self, annotations_file: str = "annotations.json"):
        self.annotations_file = annotations_file
        self.annotations = self._load_annotations()
    
    def _load_annotations(self) -> Dict[str, Any]:
        """Load annotations from file"""
        if os.path.exists(self.annotations_file):
            try:
                with open(self.annotations_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_annotations(self):
        """Save annotations to file"""
        with open(self.annotations_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotations, f, ensure_ascii=False, indent=2)
    
    def add_annotation(self, contract_id: str, user_id: str, user_name: str,
                      text_selection: str, start_pos: int, end_pos: int,
                      annotation_text: str, annotation_type: str = "highlight") -> str:
        """Add a text annotation"""
        annotation_id = str(uuid.uuid4())
        
        if contract_id not in self.annotations:
            self.annotations[contract_id] = []
        
        annotation = {
            "id": annotation_id,
            "user_id": user_id,
            "user_name": user_name,
            "text_selection": text_selection,
            "start_pos": start_pos,
            "end_pos": end_pos,
            "annotation_text": annotation_text,
            "type": annotation_type,
            "created_at": datetime.now().isoformat(),
            "color": self._get_annotation_color(annotation_type)
        }
        
        self.annotations[contract_id].append(annotation)
        self._save_annotations()
        return annotation_id
    
    def _get_annotation_color(self, annotation_type: str) -> str:
        """Get color for annotation type"""
        colors = {
            "highlight": "#ffeb3b",
            "important": "#f44336",
            "question": "#2196f3",
            "suggestion": "#4caf50",
            "warning": "#ff9800"
        }
        return colors.get(annotation_type, "#ffeb3b")
    
    def get_annotations(self, contract_id: str) -> List[Dict[str, Any]]:
        """Get all annotations for a contract"""
        return self.annotations.get(contract_id, [])
    
    def delete_annotation(self, contract_id: str, annotation_id: str, user_id: str) -> bool:
        """Delete an annotation"""
        if contract_id in self.annotations:
            for i, annotation in enumerate(self.annotations[contract_id]):
                if annotation["id"] == annotation_id and annotation["user_id"] == user_id:
                    del self.annotations[contract_id][i]
                    self._save_annotations()
                    return True
        return False

class CollaborationUI:
    """UI components for collaboration features"""
    
    def __init__(self):
        self.collaboration_manager = CollaborationManager()
        self.annotation_manager = AnnotationManager()
    
    def display_comments_section(self, contract_id: str, current_user: Dict[str, Any]):
        """Display comments section for a contract"""
        st.markdown("### 💬 التعليقات والمناقشات")
        
        # Add new comment
        with st.expander("➕ إضافة تعليق جديد"):
            with st.form(f"add_comment_{contract_id}"):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    comment_content = st.text_area("التعليق", placeholder="اكتب تعليقك هنا...")
                
                with col2:
                    comment_type = st.selectbox(
                        "نوع التعليق",
                        options=[CommentType.GENERAL, CommentType.LEGAL_ISSUE, 
                                CommentType.SUGGESTION, CommentType.QUESTION],
                        format_func=self._get_comment_type_name
                    )
                
                section = st.text_input("القسم (اختياري)", placeholder="مثل: البند الثالث")
                
                if st.form_submit_button("إضافة التعليق"):
                    if comment_content:
                        comment_id = self.collaboration_manager.add_comment(
                            contract_id, current_user["id"], current_user["full_name"],
                            comment_content, comment_type, section
                        )
                        st.success("✅ تم إضافة التعليق بنجاح")
                        st.rerun()
                    else:
                        st.error("يرجى كتابة التعليق")
        
        # Display existing comments
        comments = self.collaboration_manager.get_comments(contract_id)
        
        if comments:
            st.markdown("#### 📋 التعليقات الموجودة")
            
            # Filter options
            col1, col2 = st.columns(2)
            with col1:
                status_filter = st.selectbox(
                    "تصفية حسب الحالة",
                    options=["الكل", CommentStatus.OPEN, CommentStatus.IN_PROGRESS, CommentStatus.RESOLVED],
                    format_func=self._get_status_name
                )
            
            with col2:
                type_filter = st.selectbox(
                    "تصفية حسب النوع",
                    options=["الكل"] + [CommentType.GENERAL, CommentType.LEGAL_ISSUE, 
                            CommentType.SUGGESTION, CommentType.QUESTION],
                    format_func=self._get_comment_type_name
                )
            
            # Filter comments
            filtered_comments = comments
            if status_filter != "الكل":
                filtered_comments = [c for c in filtered_comments if c["status"] == status_filter]
            if type_filter != "الكل":
                filtered_comments = [c for c in filtered_comments if c["type"] == type_filter]
            
            # Display comments
            for comment in filtered_comments:
                self._display_comment_card(comment, contract_id, current_user)
        else:
            st.info("لا توجد تعليقات على هذا العقد بعد")
    
    def _display_comment_card(self, comment: Dict[str, Any], contract_id: str, current_user: Dict[str, Any]):
        """Display individual comment card"""
        # Status color
        status_color = {
            CommentStatus.OPEN: "#e74c3c",
            CommentStatus.IN_PROGRESS: "#f39c12",
            CommentStatus.RESOLVED: "#27ae60"
        }.get(comment["status"], "#95a5a6")
        
        # Type icon
        type_icon = {
            CommentType.GENERAL: "💬",
            CommentType.LEGAL_ISSUE: "⚖️",
            CommentType.SUGGESTION: "💡",
            CommentType.QUESTION: "❓",
            CommentType.APPROVAL: "✅",
            CommentType.REJECTION: "❌"
        }.get(comment["type"], "💬")
        
        with st.container():
            st.markdown(f"""
            <div style="border-left: 4px solid {status_color}; padding: 15px; margin: 10px 0; 
                        background: #f8f9fa; border-radius: 5px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <div>
                        <strong>{type_icon} {comment['user_name']}</strong>
                        <span style="color: #666; font-size: 0.9em; margin-left: 10px;">
                            {self._format_date(comment['created_at'])}
                        </span>
                    </div>
                    <span style="background: {status_color}; color: white; padding: 2px 8px; 
                                border-radius: 12px; font-size: 0.8em;">
                        {self._get_status_name(comment['status'])}
                    </span>
                </div>
                <div style="margin-bottom: 10px;">
                    {comment['content']}
                </div>
                {f"<div style='color: #666; font-size: 0.9em;'><strong>القسم:</strong> {comment['section']}</div>" if comment.get('section') else ""}
            </div>
            """, unsafe_allow_html=True)
            
            # Action buttons
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                if st.button(f"رد", key=f"reply_{comment['id']}"):
                    st.session_state[f"show_reply_{comment['id']}"] = True
            
            with col2:
                if comment["status"] != CommentStatus.RESOLVED:
                    if st.button(f"حل", key=f"resolve_{comment['id']}"):
                        self.collaboration_manager.update_comment_status(
                            contract_id, comment['id'], CommentStatus.RESOLVED, current_user["id"]
                        )
                        st.rerun()
            
            with col3:
                if comment["status"] == CommentStatus.OPEN:
                    if st.button(f"قيد التنفيذ", key=f"progress_{comment['id']}"):
                        self.collaboration_manager.update_comment_status(
                            contract_id, comment['id'], CommentStatus.IN_PROGRESS, current_user["id"]
                        )
                        st.rerun()
            
            # Reply form
            if st.session_state.get(f"show_reply_{comment['id']}", False):
                with st.form(f"reply_form_{comment['id']}"):
                    reply_content = st.text_area("الرد", key=f"reply_content_{comment['id']}")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("إرسال الرد"):
                            if reply_content:
                                self.collaboration_manager.add_reply(
                                    contract_id, comment['id'], current_user["id"], 
                                    current_user["full_name"], reply_content
                                )
                                st.session_state[f"show_reply_{comment['id']}"] = False
                                st.rerun()
                    
                    with col2:
                        if st.form_submit_button("إلغاء"):
                            st.session_state[f"show_reply_{comment['id']}"] = False
                            st.rerun()
            
            # Display replies
            if comment["replies"]:
                st.markdown("**الردود:**")
                for reply in comment["replies"]:
                    st.markdown(f"""
                    <div style="margin-left: 20px; padding: 10px; background: #e9ecef; 
                                border-radius: 5px; margin: 5px 0;">
                        <div style="font-weight: bold; color: #495057;">
                            {reply['user_name']} 
                            <span style="font-weight: normal; color: #6c757d; font-size: 0.9em;">
                                - {self._format_date(reply['created_at'])}
                            </span>
                        </div>
                        <div style="margin-top: 5px;">{reply['content']}</div>
                    </div>
                    """, unsafe_allow_html=True)
    
    def display_collaboration_statistics(self, contract_id: str = None):
        """Display collaboration statistics"""
        stats = self.collaboration_manager.get_comment_statistics(contract_id)
        
        st.markdown("### 📊 إحصائيات التعاون")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("إجمالي التعليقات", stats["total_comments"])
        
        with col2:
            st.metric("تعليقات مفتوحة", stats["open_comments"])
        
        with col3:
            st.metric("تعليقات محلولة", stats["resolved_comments"])
        
        with col4:
            st.metric("إجمالي الردود", stats["total_replies"])
    
    def _get_comment_type_name(self, comment_type: str) -> str:
        """Get Arabic comment type name"""
        names = {
            "الكل": "الكل",
            CommentType.GENERAL: "عام",
            CommentType.LEGAL_ISSUE: "مسألة قانونية",
            CommentType.SUGGESTION: "اقتراح",
            CommentType.QUESTION: "سؤال",
            CommentType.APPROVAL: "موافقة",
            CommentType.REJECTION: "رفض"
        }
        return names.get(comment_type, comment_type)
    
    def _get_status_name(self, status: str) -> str:
        """Get Arabic status name"""
        names = {
            "الكل": "الكل",
            CommentStatus.OPEN: "مفتوح",
            CommentStatus.IN_PROGRESS: "قيد التنفيذ",
            CommentStatus.RESOLVED: "محلول"
        }
        return names.get(status, status)
    
    def _format_date(self, date_str: str) -> str:
        """Format date for display"""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            return date_str
