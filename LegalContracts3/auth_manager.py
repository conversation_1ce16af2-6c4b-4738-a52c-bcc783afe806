#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Authentication Manager for Legal Contract Analysis
Handles user authentication, session management, and security
"""

import streamlit as st
import hashlib
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuthManager:
    """Manages user authentication and session security"""
    
    def __init__(self, db_manager):
        """Initialize authentication manager"""
        self.db_manager = db_manager
        self.session_timeout = timedelta(hours=8)  # 8 hour session timeout
    
    def render_login_page(self) -> bool:
        """Render login page and handle authentication"""
        # Header using native Streamlit components
        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            st.markdown("# ⚖️")
            st.markdown("## محلل العقود القانونية المتقدم")
            st.markdown("**نظام تحليل العقود بالذكاء الاصطناعي**")
            st.markdown("---")
        
        # Login form
        with st.container():
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col2:
                st.markdown("""
                <div class="analysis-card-enhanced" style="padding: 2rem;">
                    <h3 style="text-align: center; color: #1e3a8a; margin-bottom: 1.5rem;">
                        🔐 تسجيل الدخول
                    </h3>
                </div>
                """, unsafe_allow_html=True)
                
                with st.form("login_form"):
                    username = st.text_input(
                        "اسم المستخدم",
                        placeholder="أدخل اسم المستخدم",
                        help="استخدم: admin"
                    )
                    
                    password = st.text_input(
                        "كلمة المرور",
                        type="password",
                        placeholder="أدخل كلمة المرور",
                        help="استخدم: admin123"
                    )
                    
                    remember_me = st.checkbox("تذكرني")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        login_button = st.form_submit_button(
                            "🔑 تسجيل الدخول",
                            type="primary",
                            use_container_width=True
                        )
                    
                    with col2:
                        guest_button = st.form_submit_button(
                            "👤 دخول كضيف",
                            use_container_width=True
                        )
                
                # Handle login
                if login_button and username and password:
                    if self.authenticate(username, password, remember_me):
                        st.success("✅ تم تسجيل الدخول بنجاح!")
                        st.rerun()
                    else:
                        st.error("❌ اسم المستخدم أو كلمة المرور غير صحيحة")
                
                elif guest_button:
                    if self.login_as_guest():
                        st.success("✅ تم الدخول كضيف!")
                        st.rerun()
                
                # Login hints
                st.markdown("---")
                st.info("""
                **بيانات تسجيل الدخول التجريبية:**
                - اسم المستخدم: `admin`
                - كلمة المرور: `admin123`
                
                أو يمكنك الدخول كضيف للتجربة
                """)
        
        return False
    
    def authenticate(self, username: str, password: str, remember_me: bool = False) -> bool:
        """Authenticate user credentials"""
        try:
            user = self.db_manager.authenticate_user(username, password)
            
            if user:
                # Create session
                session_id = str(uuid.uuid4())
                session_data = {
                    'session_id': session_id,
                    'user_id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'email': user['email'],
                    'role': user['role'],
                    'preferences': user['preferences'],
                    'login_time': datetime.now().isoformat(),
                    'remember_me': remember_me
                }
                
                # Store in session state
                st.session_state.authenticated = True
                st.session_state.user_session = session_data
                st.session_state.current_user = user
                
                logger.info(f"User {username} authenticated successfully")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def login_as_guest(self) -> bool:
        """Login as guest user"""
        try:
            guest_user = {
                'id': 'guest',
                'username': 'guest',
                'full_name': 'مستخدم ضيف',
                'email': '<EMAIL>',
                'role': 'guest',
                'preferences': {}
            }
            
            session_data = {
                'session_id': str(uuid.uuid4()),
                'user_id': 'guest',
                'username': 'guest',
                'full_name': 'مستخدم ضيف',
                'email': '<EMAIL>',
                'role': 'guest',
                'preferences': {},
                'login_time': datetime.now().isoformat(),
                'remember_me': False
            }
            
            st.session_state.authenticated = True
            st.session_state.user_session = session_data
            st.session_state.current_user = guest_user
            
            logger.info("Guest user logged in")
            return True
            
        except Exception as e:
            logger.error(f"Guest login error: {e}")
            return False
    
    def logout(self):
        """Logout current user"""
        try:
            if 'user_session' in st.session_state:
                username = st.session_state.user_session.get('username', 'unknown')
                logger.info(f"User {username} logged out")
            
            # Clear session state
            for key in ['authenticated', 'user_session', 'current_user']:
                if key in st.session_state:
                    del st.session_state[key]
            
            # Clear other session data
            keys_to_clear = [
                'current_analysis', 'file_processor', 'export_manager',
                'selected_legal_system', 'theme', 'language'
            ]
            
            for key in keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]
            
            st.rerun()
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        if not st.session_state.get('authenticated', False):
            return False
        
        # Check session timeout
        if self._is_session_expired():
            self.logout()
            return False
        
        return True
    
    def _is_session_expired(self) -> bool:
        """Check if session has expired"""
        try:
            session = st.session_state.get('user_session')
            if not session:
                return True
            
            login_time = datetime.fromisoformat(session['login_time'])
            
            # Skip timeout for remembered sessions
            if session.get('remember_me', False):
                return False
            
            return datetime.now() - login_time > self.session_timeout
            
        except Exception as e:
            logger.error(f"Session check error: {e}")
            return True
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current authenticated user"""
        if self.is_authenticated():
            return st.session_state.get('current_user')
        return None
    
    def require_authentication(self):
        """Decorator to require authentication for pages"""
        if not self.is_authenticated():
            self.render_login_page()
            st.stop()
    
    def require_role(self, required_role: str):
        """Require specific user role"""
        user = self.get_current_user()
        if not user or user.get('role') != required_role:
            st.error("❌ ليس لديك صلاحية للوصول إلى هذه الصفحة")
            st.stop()
    
    def render_user_info(self):
        """Render user information in sidebar"""
        user = self.get_current_user()
        if not user:
            return
        
        with st.sidebar:
            st.markdown("---")
            st.markdown("### 👤 معلومات المستخدم")
            
            # User card
            st.markdown(f"""
            <div class="analysis-card-enhanced" style="padding: 1rem; margin-bottom: 1rem;">
                <div style="text-align: center;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">
                        {'👑' if user['role'] == 'admin' else '👤' if user['role'] == 'user' else '👥'}
                    </div>
                    <h4 style="margin: 0; color: #1e3a8a;">{user['full_name']}</h4>
                    <p style="margin: 0.25rem 0; color: #64748b; font-size: 0.9rem;">
                        @{user['username']}
                    </p>
                    <p style="margin: 0; color: #64748b; font-size: 0.8rem;">
                        {user['role'].title()}
                    </p>
                </div>
            </div>
            """, unsafe_allow_html=True)
            
            # Session info
            session = st.session_state.get('user_session', {})
            login_time = session.get('login_time', '')
            if login_time:
                try:
                    login_dt = datetime.fromisoformat(login_time)
                    st.caption(f"تسجيل الدخول: {login_dt.strftime('%H:%M')}")
                except:
                    pass
            
            # Logout button
            if st.button("🚪 تسجيل الخروج", use_container_width=True):
                self.logout()
    
    def render_session_warning(self):
        """Render session timeout warning"""
        if not self.is_authenticated():
            return
        
        session = st.session_state.get('user_session', {})
        if session.get('remember_me', False):
            return
        
        try:
            login_time = datetime.fromisoformat(session['login_time'])
            time_left = self.session_timeout - (datetime.now() - login_time)
            
            # Show warning if less than 30 minutes left
            if time_left < timedelta(minutes=30) and time_left > timedelta(0):
                minutes_left = int(time_left.total_seconds() / 60)
                st.warning(f"⏰ ستنتهي جلستك خلال {minutes_left} دقيقة")
                
                if st.button("🔄 تجديد الجلسة"):
                    session['login_time'] = datetime.now().isoformat()
                    st.session_state.user_session = session
                    st.success("✅ تم تجديد الجلسة")
                    st.rerun()
                    
        except Exception as e:
            logger.error(f"Session warning error: {e}")
    
    def get_user_permissions(self) -> Dict[str, bool]:
        """Get user permissions based on role"""
        user = self.get_current_user()
        if not user:
            return {}
        
        role = user.get('role', 'guest')
        
        permissions = {
            'admin': {
                'can_view_analytics': True,
                'can_manage_users': True,
                'can_manage_templates': True,
                'can_export': True,
                'can_analyze': True,
                'can_save_analysis': True,
                'can_view_history': True
            },
            'user': {
                'can_view_analytics': False,
                'can_manage_users': False,
                'can_manage_templates': False,
                'can_export': True,
                'can_analyze': True,
                'can_save_analysis': True,
                'can_view_history': True
            },
            'guest': {
                'can_view_analytics': False,
                'can_manage_users': False,
                'can_manage_templates': False,
                'can_export': False,
                'can_analyze': True,
                'can_save_analysis': False,
                'can_view_history': False
            }
        }
        
        return permissions.get(role, permissions['guest'])
