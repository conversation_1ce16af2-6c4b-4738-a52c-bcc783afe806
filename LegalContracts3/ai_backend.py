#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Backend Module for Contract Analysis
Handles integration with Ollama and LM Studio for local AI processing
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any
import streamlit as st
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ContractAnalyzer:
    """Main class for contract analysis using local AI models with multi-legal system support"""

    def __init__(self, backend: str = "ollama", model: str = "llama3.1:8b", legal_system: str = "kuwait", host: str = "localhost", port: int = None):
        """
        Initialize the contract analyzer

        Args:
            backend: Either 'ollama' or 'lmstudio'
            model: Model name to use for analysis
            legal_system: Legal system to use ('kuwait' or 'saudi_arabia')
            host: Host address for the AI backend
            port: Port number for the AI backend
        """
        self.backend = backend
        self.model = model
        self.legal_system = legal_system
        self.host = host
        self.port = port if port else (11434 if backend == "ollama" else 1234)
        self.base_url = self._get_base_url()

        # Import legal framework manager
        try:
            from legal_frameworks import get_legal_framework_manager, LegalSystem
            self.legal_manager = get_legal_framework_manager()
            if legal_system == "saudi_arabia":
                self.legal_manager.set_legal_system(LegalSystem.SAUDI_ARABIA)
            else:
                self.legal_manager.set_legal_system(LegalSystem.KUWAIT)
        except ImportError:
            self.legal_manager = None

        # Import legal guidelines manager
        try:
            from legal_guidelines_manager import LegalGuidelinesManager
            self.guidelines_manager = LegalGuidelinesManager()
        except ImportError:
            self.guidelines_manager = None
        
    def _get_base_url(self) -> str:
        """Get the base URL for the AI backend"""
        return f"http://{self.host}:{self.port}"
    
    def _make_request(self, prompt: str, system_prompt: str = "") -> str:
        """Make a request to the AI backend"""
        try:
            if self.backend == "ollama":
                response = self._ollama_request(prompt, system_prompt)
            elif self.backend == "lmstudio":
                response = self._lmstudio_request(prompt, system_prompt)
            else:
                response = ""

            # Debug output
            if not response or response.strip() == "":
                print(f"DEBUG: Empty response from {self.backend} backend")
                return "No response from AI service"

            return response

        except Exception as e:
            error_msg = f"خطأ في الاتصال بالذكاء الاصطناعي: {str(e)}"
            print(f"DEBUG: AI Backend Error: {str(e)}")
            if hasattr(st, 'error'):
                st.error(error_msg)
            return f"AI service error: {str(e)}"
    
    def _ollama_request(self, prompt: str, system_prompt: str = "") -> str:
        """Make request to Ollama API"""
        url = f"{self.base_url}/api/generate"
        
        data = {
            "model": self.model,
            "prompt": prompt,
            "system": system_prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,
                "top_p": 0.9,
                "max_tokens": 4000
            }
        }
        
        response = requests.post(url, json=data, timeout=300)
        response.raise_for_status()
        
        result = response.json()
        return result.get("response", "")
    
    def _lmstudio_request(self, prompt: str, system_prompt: str = "") -> str:
        """Make request to LM Studio API with retry logic"""
        url = f"{self.base_url}/v1/chat/completions"

        # Get available models and use the first available one if current model doesn't exist
        available_models = self.get_available_models()
        model_to_use = self.model

        if available_models and self.model not in available_models:
            # Prefer instruction models for better translation quality
            preferred_models = []

            # First priority: Instruction models
            for model in available_models:
                if any(keyword in model.lower() for keyword in ['instruct', 'chat']):
                    preferred_models.append(model)

            # Second priority: General purpose models
            if not preferred_models:
                for model in available_models:
                    if any(keyword in model.lower() for keyword in ['llama', 'qwen']):
                        # Avoid specialized models that might cause mixed language issues
                        if not any(avoid in model.lower() for avoid in ['arabic-law', 'finetune', 'abliterated']):
                            preferred_models.append(model)

            # Use preferred model or fallback to first available
            model_to_use = preferred_models[0] if preferred_models else available_models[0]

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": model_to_use,
            "messages": messages,
            "temperature": 0.3,
            "max_tokens": 2000,  # Reduced for faster processing
            "stream": False,
            "top_p": 0.9
        }

        headers = {
            "Content-Type": "application/json"
        }

        # Setup retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        session = requests.Session()
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        try:
            # Validate request data before sending
            if not messages or len(messages) == 0:
                raise Exception("No messages provided for API request")

            # Check if prompt is too long
            total_content_length = sum(len(msg.get("content", "")) for msg in messages)
            if total_content_length > 6000:  # More conservative limit for better reliability
                raise Exception("Request content too long. Try with shorter text.")

            response = session.post(url, json=data, headers=headers, timeout=300)

            # Handle specific HTTP errors
            if response.status_code == 400:
                try:
                    error_detail = response.json()
                    error_msg = error_detail.get('error', {}).get('message', 'Bad Request')
                    raise Exception(f"Bad Request (400): {error_msg}. Check if the model '{model_to_use}' is loaded in LM Studio.")
                except:
                    raise Exception(f"Bad Request (400): Invalid request format or model '{model_to_use}' not loaded in LM Studio.")
            elif response.status_code == 404:
                raise Exception(f"Model '{model_to_use}' not found. Please load a model in LM Studio.")
            elif response.status_code == 500:
                raise Exception("LM Studio server error. Try restarting LM Studio or loading a different model.")

            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                if content and content.strip():
                    return content.strip()
                else:
                    raise Exception("Empty response from model")
            else:
                raise Exception(f"Unexpected response format: {result}")
        except requests.exceptions.Timeout:
            raise Exception("Request timed out. The model may be processing a large document. Try with a shorter text or check if LM Studio is responding.")
        except requests.exceptions.ConnectionError:
            raise Exception("Cannot connect to LM Studio. Please ensure LM Studio is running on localhost:1234.")
        except Exception as e:
            if "LM Studio API error" in str(e):
                raise e
            else:
                raise Exception(f"LM Studio API error: {str(e)}")
    
    def translate_to_arabic(self, contract_text: str) -> str:
        """Translate English contract to Arabic with legal terminology"""

        # Check if text is too long and needs chunking
        max_chunk_size = 1200  # Smaller chunks for faster processing

        if len(contract_text) > max_chunk_size:
            return self._translate_large_document(contract_text)

        system_prompt = """أنت مترجم قانوني متخصص في ترجمة العقود من الإنجليزية إلى العربية.
        يجب أن تحافظ على المصطلحات القانونية الدقيقة وتستخدم المصطلحات القانونية الكويتية المناسبة.
        احرص على الدقة والوضوح في الترجمة مع الحفاظ على المعنى القانوني الأصلي."""

        prompt = f"""قم بترجمة العقد التالي من الإنجليزية إلى العربية مع التركيز على:
        1. استخدام المصطلحات القانونية الكويتية الصحيحة
        2. الحفاظ على المعنى القانوني الدقيق
        3. ترجمة واضحة ومفهومة
        4. تنسيق مناسب للنص المترجم

        النص الإنجليزي:
        {contract_text}

        الترجمة العربية:"""

        return self._make_request(prompt, system_prompt)

    def _translate_large_document(self, contract_text: str) -> str:
        """Translate large documents by breaking them into chunks"""

        # Split text into logical chunks (by paragraphs or sections)
        chunks = self._split_text_intelligently(contract_text, max_size=1200)
        translated_chunks = []

        system_prompt = """أنت مترجم قانوني متخصص في ترجمة العقود من الإنجليزية إلى العربية.
        يجب أن تحافظ على المصطلحات القانونية الدقيقة وتستخدم المصطلحات القانونية الكويتية المناسبة.
        احرص على الدقة والوضوح في الترجمة مع الحفاظ على المعنى القانوني الأصلي.
        هذا جزء من عقد أكبر، لذا حافظ على التناسق في الترجمة."""

        for i, chunk in enumerate(chunks):
            user_prompt = f"""قم بترجمة الجزء التالي من العقد (الجزء {i+1} من {len(chunks)}):

            النص الإنجليزي:
            {chunk}

            الترجمة العربية:"""

            try:
                translated_chunk = self._make_request(user_prompt, system_prompt)
                if translated_chunk:
                    translated_chunks.append(translated_chunk)
                else:
                    translated_chunks.append(f"[خطأ في ترجمة الجزء {i+1}]")
            except Exception as e:
                print(f"Error translating chunk {i+1}: {e}")
                translated_chunks.append(f"[خطأ في ترجمة الجزء {i+1}: {str(e)}]")

        return "\n\n".join(translated_chunks)

    def _split_text_intelligently(self, text: str, max_size: int = 2000) -> List[str]:
        """Split text into chunks while preserving logical structure"""

        # First try to split by double newlines (paragraphs)
        paragraphs = text.split('\n\n')
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            # If adding this paragraph would exceed max_size
            if len(current_chunk) + len(paragraph) + 2 > max_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = paragraph
                else:
                    # Paragraph itself is too long, split by sentences
                    sentences = paragraph.split('. ')
                    for sentence in sentences:
                        if len(current_chunk) + len(sentence) + 2 > max_size:
                            if current_chunk:
                                chunks.append(current_chunk.strip())
                                current_chunk = sentence
                            else:
                                # Even sentence is too long, force split
                                chunks.append(sentence[:max_size])
                                current_chunk = sentence[max_size:]
                        else:
                            current_chunk += sentence + ". " if not sentence.endswith('.') else sentence + " "
            else:
                current_chunk += paragraph + "\n\n"

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _should_chunk_text(self, text: str) -> bool:
        """Determine if text should be chunked based on length"""
        # Conservative limits to avoid API errors
        return len(text) > 3000 or len(text.split()) > 500

    def _chunk_contract_for_analysis(self, text: str, max_chunk_size: int = 2500) -> List[str]:
        """Intelligently chunk contract text for analysis"""
        if len(text) <= max_chunk_size:
            return [text]

        # Try to split by sections first
        sections = []
        current_section = ""

        lines = text.split('\n')
        for line in lines:
            # Check if this looks like a section header
            if (line.strip() and
                (line.strip().startswith(('المادة', 'البند', 'الفقرة', 'Article', 'Section', 'Clause')) or
                 line.strip().endswith(':') or
                 len(line.strip()) < 100 and line.strip().isupper())):

                # Save current section if it exists
                if current_section.strip():
                    sections.append(current_section.strip())
                    current_section = line + '\n'
                else:
                    current_section += line + '\n'
            else:
                current_section += line + '\n'

                # If section gets too long, split it
                if len(current_section) > max_chunk_size:
                    sections.append(current_section.strip())
                    current_section = ""

        # Add the last section
        if current_section.strip():
            sections.append(current_section.strip())

        # If we still have sections that are too long, split them further
        final_chunks = []
        for section in sections:
            if len(section) <= max_chunk_size:
                final_chunks.append(section)
            else:
                # Split by sentences or paragraphs
                sentences = section.split('. ')
                current_chunk = ""
                for sentence in sentences:
                    if len(current_chunk + sentence) <= max_chunk_size:
                        current_chunk += sentence + '. '
                    else:
                        if current_chunk.strip():
                            final_chunks.append(current_chunk.strip())
                        current_chunk = sentence + '. '

                if current_chunk.strip():
                    final_chunks.append(current_chunk.strip())

        return final_chunks if final_chunks else [text[:max_chunk_size]]

    def analyze_contract(self, text: str, legal_system: str = None) -> Dict[str, Any]:
        """Comprehensive contract analysis with AI integration and intelligent chunking"""
        import uuid
        from datetime import datetime

        if legal_system is None:
            legal_system = self.legal_system

        try:
            # Initialize analysis results
            analysis = {
                'success': True,
                'analysis_id': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'legal_system': legal_system,
                'word_count': len(text.split()),
                'character_count': len(text),
                'text': text,
                'is_chunked': self._should_chunk_text(text)
            }

            # Perform comprehensive analysis
            st.info("🔍 بدء التحليل الشامل للعقد...")

            # Check if we need to chunk the text
            if self._should_chunk_text(text):
                st.info("📄 النص طويل - سيتم تقسيمه لضمان دقة التحليل")
                return self._analyze_contract_chunked(text, legal_system, analysis)

            # 1. Contract Translation (Arabic to English)
            with st.spinner("ترجمة العقد إلى الإنجليزية..."):
                translation = self.translate_contract(text)
                analysis['translation'] = translation

            # 2. Legal Points Analysis
            with st.spinner("تحليل النقاط القانونية..."):
                legal_points = self.analyze_legal_points(text)
                analysis['legal_points'] = legal_points

            # 3. Risk Assessment
            with st.spinner("تقييم المخاطر..."):
                risk_assessment = self.assess_risks(text)
                analysis['risk_assessment'] = risk_assessment
                analysis['risk_score'] = risk_assessment.get('overall_score', 0)

            # 4. Compliance Check
            with st.spinner("فحص الامتثال القانوني..."):
                compliance = self.check_compliance(text)
                analysis['compliance'] = compliance

            # 5. Generate Recommendations
            with st.spinner("إنشاء التوصيات..."):
                recommendations = self.generate_recommendations(text, legal_points, risk_assessment)
                analysis['recommendations'] = recommendations

            # 6. Contract Summary
            with st.spinner("إنشاء ملخص العقد..."):
                summary = self.generate_summary(text)
                analysis['summary'] = summary

            # 7. Calculate metrics
            analysis['clauses'] = len([p for p in legal_points if p.get('type') == 'clause'])
            analysis['issues'] = len([p for p in legal_points if p.get('risk_level') in ['high', 'عالي']])
            analysis['pages'] = max(1, len(text) // 2000)  # Estimate pages

            st.success("✅ تم إكمال التحليل بنجاح!")
            return analysis

        except Exception as e:
            st.error(f"❌ خطأ في التحليل: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'analysis_id': str(uuid.uuid4())
            }

    def _analyze_contract_chunked(self, text: str, legal_system: str, base_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze large contracts by chunking them into smaller pieces"""
        import uuid
        from datetime import datetime

        try:
            # Chunk the contract
            chunks = self._chunk_contract_for_analysis(text)
            st.info(f"📊 تم تقسيم العقد إلى {len(chunks)} جزء للتحليل")

            # Initialize combined results
            combined_analysis = base_analysis.copy()
            combined_analysis['chunks_count'] = len(chunks)
            combined_analysis['chunk_results'] = []

            all_legal_points = []
            all_risks = []
            all_compliance_issues = []
            translation_parts = []

            # Analyze each chunk
            for i, chunk in enumerate(chunks):
                st.info(f"🔍 تحليل الجزء {i+1} من {len(chunks)}")

                chunk_analysis = {
                    'chunk_id': i + 1,
                    'chunk_text': chunk[:200] + "..." if len(chunk) > 200 else chunk,
                    'word_count': len(chunk.split())
                }

                try:
                    # 1. Translation for this chunk
                    with st.spinner(f"ترجمة الجزء {i+1}..."):
                        chunk_translation = self.translate_contract(chunk)
                        if chunk_translation and 'english_translation' in chunk_translation:
                            translation_parts.append(chunk_translation['english_translation'])
                        elif chunk_translation and 'arabic_translation' in chunk_translation:
                            translation_parts.append(chunk_translation['arabic_translation'])

                    # 2. Legal points for this chunk
                    with st.spinner(f"تحليل النقاط القانونية للجزء {i+1}..."):
                        chunk_legal_points = self.analyze_legal_points(chunk)
                        if chunk_legal_points:
                            all_legal_points.extend(chunk_legal_points)

                    # 3. Risk assessment for this chunk
                    with st.spinner(f"تقييم مخاطر الجزء {i+1}..."):
                        chunk_risks = self.assess_risks(chunk)
                        if chunk_risks and 'risks' in chunk_risks:
                            all_risks.extend(chunk_risks['risks'])

                    # 4. Compliance check for this chunk
                    with st.spinner(f"فحص امتثال الجزء {i+1}..."):
                        chunk_compliance = self.check_compliance(chunk)
                        if chunk_compliance and 'issues' in chunk_compliance:
                            all_compliance_issues.extend(chunk_compliance['issues'])

                    chunk_analysis['status'] = 'completed'

                except Exception as e:
                    chunk_analysis['status'] = 'failed'
                    chunk_analysis['error'] = str(e)
                    st.warning(f"⚠️ فشل تحليل الجزء {i+1}: {str(e)}")

                combined_analysis['chunk_results'].append(chunk_analysis)

            # Combine all results
            with st.spinner("دمج نتائج التحليل..."):
                # Combined translation
                if translation_parts:
                    combined_analysis['translation'] = {
                        'english_translation': '\n\n'.join(translation_parts),
                        'arabic_translation': text,  # Original text as Arabic
                        'source_language': 'arabic',
                        'target_language': 'english',
                        'translation_notes': [f"تم الترجمة في {len(chunks)} أجزاء", f"Translated in {len(chunks)} parts"],
                        'confidence_level': 'medium',
                        'key_terms': []
                    }
                else:
                    combined_analysis['translation'] = {
                        'english_translation': "الترجمة غير متوفرة",
                        'arabic_translation': text,
                        'source_language': 'arabic',
                        'target_language': 'english',
                        'translation_notes': ["فشل في الترجمة", "Translation failed"],
                        'confidence_level': 'low',
                        'key_terms': []
                    }

                # Combined legal points (remove duplicates and ensure proper structure)
                unique_legal_points = []
                seen_points = set()
                for point in all_legal_points:
                    # Ensure proper structure for UI display
                    if isinstance(point, dict):
                        # Normalize the point structure
                        normalized_point = {
                            'title': point.get('title') or point.get('point') or point.get('category', 'نقطة قانونية'),
                            'description': point.get('description') or point.get('details') or point.get('point', 'لا يوجد وصف'),
                            'priority': point.get('priority', 'medium'),
                            'risk_level': point.get('risk_level', 'medium'),
                            'category': point.get('category', 'general'),
                            'law_reference': point.get('law_reference', 'القانون الكويتي')
                        }

                        point_key = normalized_point['title'] + normalized_point['category']
                        if point_key not in seen_points:
                            unique_legal_points.append(normalized_point)
                            seen_points.add(point_key)

                combined_analysis['legal_points'] = unique_legal_points[:20]  # Limit to top 20

                # Combined risk assessment
                combined_analysis['risk_assessment'] = {
                    'risks': all_risks[:15],  # Limit to top 15 risks
                    'overall_score': min(len(all_risks) * 10, 100),  # Simple scoring
                    'risk_level': 'high' if len(all_risks) > 10 else 'medium' if len(all_risks) > 5 else 'low',
                    'total_risks_found': len(all_risks)
                }
                combined_analysis['risk_score'] = combined_analysis['risk_assessment']['overall_score']

                # Combined compliance
                combined_analysis['compliance'] = {
                    'issues': all_compliance_issues[:10],  # Limit to top 10 issues
                    'compliance_score': max(0, 100 - len(all_compliance_issues) * 5),
                    'total_issues_found': len(all_compliance_issues)
                }

                # Generate overall recommendations
                combined_analysis['recommendations'] = self._generate_chunked_recommendations(
                    unique_legal_points, all_risks, all_compliance_issues
                )

                # Generate overall summary
                combined_analysis['summary'] = self._generate_chunked_summary(
                    text, len(chunks), len(unique_legal_points), len(all_risks)
                )

            st.success("✅ تم إكمال التحليل المجمع بنجاح!")
            return combined_analysis

        except Exception as e:
            error_msg = f"خطأ في التحليل المجمع: {str(e)}"
            st.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'analysis_id': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'is_chunked': True
            }

    def _generate_chunked_recommendations(self, legal_points: List[Dict], risks: List[Dict], compliance_issues: List[Dict]) -> List[Dict[str, Any]]:
        """Generate recommendations based on chunked analysis results"""
        recommendations = []

        # High-priority recommendations based on risks
        if len(risks) > 10:
            recommendations.append({
                'priority': 'high',
                'category': 'risk_management',
                'title': 'مراجعة شاملة للمخاطر',
                'description': f'تم اكتشاف {len(risks)} مخاطر محتملة. يُنصح بمراجعة شاملة للعقد.',
                'action': 'مراجعة قانونية فورية'
            })

        # Compliance recommendations
        if len(compliance_issues) > 5:
            recommendations.append({
                'priority': 'high',
                'category': 'compliance',
                'title': 'مراجعة الامتثال القانوني',
                'description': f'تم اكتشاف {len(compliance_issues)} مشاكل امتثال. يجب معالجتها قبل التوقيع.',
                'action': 'استشارة قانونية متخصصة'
            })

        # Legal points recommendations
        if len(legal_points) > 15:
            recommendations.append({
                'priority': 'medium',
                'category': 'legal_review',
                'title': 'مراجعة النقاط القانونية',
                'description': f'العقد يحتوي على {len(legal_points)} نقطة قانونية مهمة تحتاج مراجعة.',
                'action': 'مراجعة تفصيلية للبنود'
            })

        return recommendations[:5]  # Limit to top 5 recommendations

    def _generate_chunked_summary(self, text: str, chunks_count: int, legal_points_count: int, risks_count: int) -> Dict[str, Any]:
        """Generate summary for chunked analysis"""
        return {
            'contract_type': 'عقد معقد',
            'total_length': len(text),
            'chunks_analyzed': chunks_count,
            'legal_points_found': legal_points_count,
            'risks_identified': risks_count,
            'complexity_level': 'عالي' if chunks_count > 5 else 'متوسط',
            'analysis_method': 'تحليل مجزأ',
            'summary_text': f'تم تحليل عقد معقد مكون من {len(text.split())} كلمة في {chunks_count} أجزاء. تم اكتشاف {legal_points_count} نقطة قانونية و {risks_count} مخاطر محتملة.',
            'key_findings': [
                f'العقد مقسم إلى {chunks_count} أجزاء للتحليل',
                f'تم اكتشاف {legal_points_count} نقطة قانونية مهمة',
                f'تم تحديد {risks_count} مخاطر محتملة',
                'يُنصح بمراجعة قانونية شاملة'
            ]
        }

    def translate_contract(self, contract_text: str) -> Dict[str, Any]:
        """Translate contract text - auto-detects language and translates accordingly"""
        try:
            # Detect source language
            source_lang, target_lang = self._detect_language_and_direction(contract_text)

            # Check if text is too long and needs chunking
            if len(contract_text) > 3000:
                return self._translate_contract_chunked(contract_text, source_lang, target_lang)

            # Create appropriate prompts based on detected language
            if source_lang == "arabic":
                system_prompt = """You are a professional legal translator specializing in Arabic to English translation.
                IMPORTANT: Respond ONLY in English. Do not include any Arabic, Chinese, or other languages in your response.
                Translate Arabic legal documents to clear, professional English while maintaining legal terminology accuracy."""
                prompt = f"""Translate the following Arabic legal text to English.

                REQUIREMENTS:
                - Provide ONLY the English translation
                - Do not include any Arabic text in your response
                - Do not include any Chinese characters or other languages
                - Use professional legal English terminology

                Arabic Text:
                {contract_text}

                English Translation:"""
            else:  # English source
                system_prompt = """You are a professional legal translator specializing in English to Arabic translation.
                IMPORTANT: Respond ONLY in Arabic. Do not include any English, Chinese, or other languages in your response.
                Translate English legal documents to clear, professional Arabic while maintaining legal terminology accuracy."""
                prompt = f"""Translate the following English legal text to Arabic.

                REQUIREMENTS:
                - Provide ONLY the Arabic translation
                - Do not include any English text in your response
                - Do not include any Chinese characters or other languages
                - Use professional legal Arabic terminology

                English Text:
                {contract_text}

                Arabic Translation:"""

            response = self._make_request(prompt, system_prompt)

            # Check if we got a valid response
            if response and len(response.strip()) > 5 and not any(error_word in response.lower() for error_word in ['error', 'service', 'unavailable', 'failed']):
                # Clean up the response and remove mixed language content
                clean_translation = self._clean_mixed_language_response(response.strip(), target_lang)

                # Extract key terms if possible
                key_terms = self._extract_key_terms(contract_text, clean_translation, source_lang, target_lang)

                # Create appropriate response based on target language
                translation_key = "arabic_translation" if target_lang == "arabic" else "english_translation"

                return {
                    translation_key: clean_translation,
                    "source_language": source_lang,
                    "target_language": target_lang,
                    "translation_notes": [f"Professional {source_lang} to {target_lang} translation provided by AI"],
                    "confidence_level": "high" if len(clean_translation) > 50 else "medium",
                    "key_terms": key_terms
                }
            else:
                # Try a more detailed translation request
                return self._try_detailed_translation(contract_text, source_lang, target_lang)

        except Exception as e:
            # Log the error for debugging
            if hasattr(st, 'error'):
                st.error(f"Translation error: {str(e)}")
            return self._create_simple_translation_fallback(contract_text)

    def _detect_language_and_direction(self, text: str) -> tuple:
        """Detect the source language and determine translation direction"""
        # Simple language detection based on character patterns
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        english_chars = sum(1 for char in text if char.isalpha() and ord(char) < 128)

        total_chars = len([char for char in text if char.isalpha()])

        if total_chars == 0:
            return "english", "arabic"  # Default fallback

        arabic_ratio = arabic_chars / total_chars
        english_ratio = english_chars / total_chars

        # Determine source and target languages
        if arabic_ratio > 0.3:  # If more than 30% Arabic characters
            return "arabic", "english"
        elif english_ratio > 0.5:  # If more than 50% English characters
            return "english", "arabic"
        else:
            # Fallback: check for common Arabic words
            arabic_words = ['عقد', 'الطرف', 'شركة', 'التزام', 'شروط', 'مادة', 'بند']
            english_words = ['contract', 'party', 'company', 'agreement', 'terms', 'clause', 'article']

            text_lower = text.lower()
            arabic_word_count = sum(1 for word in arabic_words if word in text)
            english_word_count = sum(1 for word in english_words if word in text_lower)

            if arabic_word_count > english_word_count:
                return "arabic", "english"
            else:
                return "english", "arabic"

    def _create_simple_translation_fallback(self, contract_text: str) -> Dict[str, Any]:
        """Create a simple translation fallback when AI is unavailable"""
        # Basic word-by-word translation for common legal terms
        basic_translations = {
            'عقد': 'contract',
            'الطرف الأول': 'first party',
            'الطرف الثاني': 'second party',
            'توريد': 'supply',
            'معدات': 'equipment',
            'شروط': 'terms',
            'دفع': 'payment',
            'تسليم': 'delivery',
            'مدة': 'duration',
            'التزامات': 'obligations',
            'مسؤوليات': 'responsibilities',
            'ضمانات': 'guarantees',
            'كفالات': 'warranties'
        }

        # Simple word replacement
        simple_translation = contract_text
        for arabic, english in basic_translations.items():
            simple_translation = simple_translation.replace(arabic, f"{english}({arabic})")

        return {
            "english_translation": f"Basic Translation: {simple_translation}",
            "translation_notes": [
                "This is a basic translation provided when the AI translation service is unavailable",
                "For professional translation, please ensure the AI backend is properly configured"
            ],
            "confidence_level": "low",
            "key_terms": [
                {"arabic": k, "english": v, "explanation": "Basic term mapping"}
                for k, v in basic_translations.items() if k in contract_text
            ]
        }

    def _extract_key_terms(self, source_text: str, target_text: str, source_lang: str = "arabic", target_lang: str = "english") -> list:
        """Extract key legal terms from the translation"""
        # Common legal terms mapping
        legal_terms = {
            'عقد': 'contract',
            'الطرف الأول': 'first party',
            'الطرف الثاني': 'second party',
            'توريد': 'supply',
            'معدات': 'equipment',
            'شروط': 'terms',
            'دفع': 'payment',
            'تسليم': 'delivery',
            'مدة': 'duration',
            'التزامات': 'obligations',
            'مسؤوليات': 'responsibilities',
            'ضمانات': 'guarantees',
            'كفالات': 'warranties',
            'فسخ': 'termination',
            'تعديل': 'amendment',
            'قانون': 'law',
            'محكمة': 'court',
            'تحكيم': 'arbitration'
        }

        key_terms = []

        if source_lang == "arabic":
            # Arabic to English translation
            for arabic, english in legal_terms.items():
                if arabic in source_text:
                    key_terms.append({
                        "arabic": arabic,
                        "english": english,
                        "explanation": f"Legal term: {english}"
                    })
        else:
            # English to Arabic translation
            english_to_arabic = {v: k for k, v in legal_terms.items()}
            source_lower = source_text.lower()
            for english, arabic in english_to_arabic.items():
                if english in source_lower:
                    key_terms.append({
                        "english": english,
                        "arabic": arabic,
                        "explanation": f"Legal term: {arabic}"
                    })

        return key_terms[:5]  # Limit to 5 key terms

    def _clean_mixed_language_response(self, response: str, target_lang: str) -> str:
        """Clean response to remove mixed language content"""
        import re

        # Remove Chinese characters
        response = re.sub(r'[\u4e00-\u9fff]+', '', response)

        # Remove common mixed language patterns
        if target_lang == "english":
            # Remove Arabic text when target is English
            response = re.sub(r'[\u0600-\u06ff\u0750-\u077f\u08a0-\u08ff\ufb50-\ufdff\ufe70-\ufeff]+', '', response)
        elif target_lang == "arabic":
            # Remove excessive English when target is Arabic (keep some for legal terms)
            lines = response.split('\n')
            cleaned_lines = []
            for line in lines:
                # Keep lines that are mostly Arabic or contain legal terms
                arabic_chars = len(re.findall(r'[\u0600-\u06ff]', line))
                total_chars = len(line.strip())
                if total_chars > 0 and (arabic_chars / total_chars > 0.3 or any(term in line.lower() for term in ['عقد', 'شروط', 'التزام'])):
                    cleaned_lines.append(line)
            response = '\n'.join(cleaned_lines)

        # Remove extra whitespace and clean up
        response = re.sub(r'\s+', ' ', response)
        response = response.strip()

        return response

    def _try_detailed_translation(self, contract_text: str, source_lang: str = "arabic", target_lang: str = "english") -> Dict[str, Any]:
        """Try a more detailed translation approach"""
        try:
            # Create appropriate prompts based on language direction
            if source_lang == "arabic":
                system_prompt = """You are an expert legal translator specializing in Arabic-English legal document translation.
                CRITICAL: Respond ONLY in English. Never include Arabic, Chinese, or any other language in your response.
                Provide accurate, professional English translations of Arabic legal documents."""
                prompt = f"""Translate the following Arabic legal contract to English.

STRICT REQUIREMENTS:
- Output ONLY in English language
- No Arabic text in the response
- No Chinese characters or other languages
- Professional legal English terminology only

Arabic Text:
{contract_text}

English Translation:"""
            else:
                system_prompt = """You are an expert legal translator specializing in English-Arabic legal document translation.
                CRITICAL: Respond ONLY in Arabic. Never include English, Chinese, or any other language in your response.
                Provide accurate, professional Arabic translations of English legal documents."""
                prompt = f"""Translate the following English legal contract to Arabic.

STRICT REQUIREMENTS:
- Output ONLY in Arabic language
- No English text in the response
- No Chinese characters or other languages
- Professional legal Arabic terminology only

English Text:
{contract_text}

Arabic Translation:"""

            response = self._make_request(prompt, system_prompt)

            if response and len(response.strip()) > 10:
                clean_translation = self._clean_mixed_language_response(response.strip(), target_lang)
                key_terms = self._extract_key_terms(contract_text, clean_translation, source_lang, target_lang)

                translation_key = "arabic_translation" if target_lang == "arabic" else "english_translation"

                return {
                    translation_key: clean_translation,
                    "source_language": source_lang,
                    "target_language": target_lang,
                    "translation_notes": ["Detailed professional translation"],
                    "confidence_level": "high",
                    "key_terms": key_terms
                }
            else:
                # Fall back to enhanced basic translation
                return self._create_enhanced_translation_fallback(contract_text)

        except Exception as e:
            return self._create_enhanced_translation_fallback(contract_text)

    def _create_enhanced_translation_fallback(self, contract_text: str) -> Dict[str, Any]:
        """Create an enhanced translation fallback with better term mapping"""
        # Enhanced legal terms mapping
        enhanced_translations = {
            'عقد توريد معدات': 'Equipment Supply Contract',
            'عقد توريد': 'Supply Contract',
            'عقد': 'Contract',
            'الطرف الأول': 'First Party',
            'الطرف الثاني': 'Second Party',
            'شركة': 'Company',
            'التزامات': 'Obligations',
            'مسؤوليات': 'Responsibilities',
            'توريد': 'Supply/Delivery',
            'معدات': 'Equipment',
            'شروط': 'Terms and Conditions',
            'دفع': 'Payment',
            'تسليم': 'Delivery',
            'مدة': 'Duration/Period',
            'ضمان': 'Guarantee/Warranty',
            'صيانة': 'Maintenance',
            'دعم فني': 'Technical Support',
            'مواصفات': 'Specifications',
            'جودة': 'Quality',
            'تركيب': 'Installation',
            'تشغيل': 'Operation',
            'دفعة مقدمة': 'Advance Payment',
            'تشغيل تجريبي': 'Trial Operation',
            'فسخ العقد': 'Contract Termination',
            'إخلال': 'Breach',
            'إشعار مسبق': 'Prior Notice',
            'تسوية': 'Settlement',
            'مستحقات': 'Dues',
            'قوانين': 'Laws',
            'محاكم': 'Courts',
            'مختصة': 'Competent'
        }

        # Create a more intelligent translation
        translation_lines = []
        lines = contract_text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                translation_lines.append('')
                continue

            translated_line = line
            # Replace terms in order of length (longest first)
            for arabic, english in sorted(enhanced_translations.items(), key=len, reverse=True):
                if arabic in translated_line:
                    translated_line = translated_line.replace(arabic, english)

            translation_lines.append(translated_line)

        enhanced_translation = '\n'.join(translation_lines)

        # Extract key terms found
        key_terms = []
        for arabic, english in enhanced_translations.items():
            if arabic in contract_text:
                key_terms.append({
                    "arabic": arabic,
                    "english": english,
                    "explanation": f"Legal term: {english}"
                })

        return {
            "english_translation": enhanced_translation,
            "translation_notes": [
                "Enhanced translation with professional legal terminology",
                "AI translation service was unavailable, using enhanced fallback method"
            ],
            "confidence_level": "medium",
            "key_terms": key_terms[:8]  # More key terms for comprehensive contracts
        }

    def _translate_contract_chunked(self, contract_text: str, source_lang: str = "arabic", target_lang: str = "english") -> Dict[str, Any]:
        """Translate large contracts by chunks and combine results"""
        chunks = self._split_text_intelligently(contract_text, max_size=3000)
        all_translations = []
        all_notes = []
        all_key_terms = []

        for i, chunk in enumerate(chunks):
            if source_lang == "arabic":
                system_prompt = """You are a professional legal translator specializing in Arabic to English translation.
                IMPORTANT: Respond ONLY in English. Never include Arabic, Chinese, or other languages."""
                prompt = f"""Translate this part of an Arabic legal contract to English (Part {i+1} of {len(chunks)}):

                REQUIREMENTS:
                - Output ONLY in English
                - No Arabic text in response
                - No Chinese or other languages
                - Maintain legal terminology accuracy

                Arabic Text:
                {chunk}

                English Translation:"""
            else:  # English to Arabic
                system_prompt = """You are a professional legal translator specializing in English to Arabic translation.
                IMPORTANT: Respond ONLY in Arabic. Never include English, Chinese, or other languages."""
                prompt = f"""Translate this part of an English legal contract to Arabic (Part {i+1} of {len(chunks)}):

                REQUIREMENTS:
                - Output ONLY in Arabic
                - No English text in response
                - No Chinese or other languages
                - Maintain legal terminology accuracy

                English Text:
                {chunk}

                Arabic Translation:"""

            try:
                response = self._make_request(prompt, system_prompt)

                # Handle response like the main translation function
                if response and len(response.strip()) > 5 and not any(error_word in response.lower() for error_word in ['error', 'service', 'unavailable', 'failed']):
                    # Clean up the response and remove mixed language content
                    clean_translation = self._clean_mixed_language_response(response.strip(), target_lang)
                    all_translations.append(clean_translation)
                    all_notes.append(f"Part {i+1} translated successfully")

                    # Extract key terms for this chunk
                    chunk_key_terms = self._extract_key_terms(chunk, clean_translation, source_lang, target_lang)
                    all_key_terms.extend(chunk_key_terms)
                else:
                    # Fallback for this chunk
                    all_translations.append(f"Translation unavailable for part {i+1}")
                    all_notes.append(f"Translation failed for part {i+1}")

            except Exception as e:
                all_translations.append(f"Translation error for part {i+1}: {str(e)}")
                all_notes.append(f"Error in part {i+1}: {str(e)}")

        # Create appropriate response based on target language
        translation_key = "arabic_translation" if target_lang == "arabic" else "english_translation"

        return {
            translation_key: "\n\n".join(all_translations),
            "source_language": source_lang,
            "target_language": target_lang,
            "translation_notes": all_notes,
            "confidence_level": "medium",
            "key_terms": all_key_terms
        }

    def _split_text_intelligently(self, text: str, max_size: int = 3000) -> List[str]:
        """Split text into chunks at logical boundaries"""
        if len(text) <= max_size:
            return [text]

        chunks = []
        current_chunk = ""

        # Split by paragraphs first
        paragraphs = text.split('\n\n')

        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) + 2 <= max_size:
                if current_chunk:
                    current_chunk += '\n\n' + paragraph
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = paragraph
                else:
                    # Paragraph is too long, split by sentences
                    sentences = paragraph.split('.')
                    for sentence in sentences:
                        if len(current_chunk) + len(sentence) + 1 <= max_size:
                            if current_chunk:
                                current_chunk += '.' + sentence
                            else:
                                current_chunk = sentence
                        else:
                            if current_chunk:
                                chunks.append(current_chunk)
                            current_chunk = sentence

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def analyze_legal_points(self, contract_text: str) -> List[Dict[str, Any]]:
        """Analyze contract for legal points according to Kuwaiti law"""

        # Check if text is too long and needs chunking
        if len(contract_text) > 2000:
            return self._analyze_legal_points_chunked(contract_text)

        # Get custom guidelines
        custom_guidelines = ""
        if self.guidelines_manager:
            custom_guidelines = self.guidelines_manager.get_guidelines_for_analysis(self.legal_system)

        legal_context = self.legal_manager.get_legal_context() if self.legal_manager else "القانون الكويتي"

        system_prompt = f"""أنت محامي متخصص في {legal_context}.
        لديك خبرة عميقة في القانون المدني والتجاري.
        قم بتحليل العقود وفقاً للقوانين المحلية وحدد النقاط القانونية المهمة.

{custom_guidelines}

استخدم المبادئ التوجيهية المخصصة أعلاه في تحليلك عند الاقتضاء."""

        prompt = f"""حلل العقد التالي وفقاً للقانون الكويتي وحدد النقاط القانونية المهمة:

        العقد:
        {contract_text}

        يرجى تحديد النقاط التالية وتصنيفها حسب الأهمية (عالية/متوسطة/منخفضة):
        1. البنود المخالفة للقانون الكويتي
        2. الحقوق والالتزامات غير الواضحة
        3. شروط الضمان والمسؤولية
        4. آليات فض النزاعات
        5. شروط الإنهاء والفسخ
        6. التزامات مالية ومواعيد السداد
        7. أي مخاطر قانونية أخرى

        IMPORTANT: قدم الإجابة في تنسيق JSON صحيح فقط، بدون أي نص إضافي. يجب أن تكون الإجابة مصفوفة JSON تحتوي على كائنات بالحقول التالية:
        [
          {{
            "title": "عنوان النقطة",
            "description": "وصف مفصل",
            "priority": "high/medium/low",
            "law_reference": "المرجع القانوني إن وجد",
            "risk_level": "high/medium/low"
          }}
        ]"""

        response = self._make_request(prompt, system_prompt)
        return self._parse_json_response(response, "legal_points")

    def _analyze_legal_points_chunked(self, contract_text: str) -> List[Dict[str, Any]]:
        """Analyze large contracts by chunks and combine results"""

        chunks = self._split_text_intelligently(contract_text, max_size=2000)
        all_points = []

        for i, chunk in enumerate(chunks):
            # Get system-specific prompt
            if self.legal_manager:
                system_prompt = self.legal_manager.get_analysis_prompt_prefix()
                legal_system_name = self.legal_manager.get_system_name()
            else:
                system_prompt = """أنت محامي متخصص في القانون المدني والتجاري.
                لديك خبرة عميقة في تحليل العقود وحدد النقاط القانونية المهمة."""
                legal_system_name = "القانون المعمول به"

            prompt = f"""حلل الجزء التالي من العقد (الجزء {i+1} من {len(chunks)}) وفقاً لـ {legal_system_name}:

            الجزء من العقد:
            {chunk}

            حدد النقاط القانونية المهمة في هذا الجزء وصنفها حسب الأهمية.

            IMPORTANT: قدم الإجابة في تنسيق JSON صحيح فقط، بدون أي نص إضافي. يجب أن تكون الإجابة مصفوفة JSON:
            [
              {{
                "title": "عنوان النقطة",
                "description": "وصف مفصل",
                "priority": "high/medium/low",
                "law_reference": "المرجع القانوني إن وجد",
                "risk_level": "high/medium/low"
              }}
            ]"""

            try:
                response = self._make_request(prompt, system_prompt)
                chunk_points = self._parse_json_response(response, "legal_points")
                if chunk_points:
                    all_points.extend(chunk_points)
            except Exception as e:
                # Add a fallback point for failed chunks
                all_points.append({
                    "title": f"خطأ في تحليل الجزء {i+1}",
                    "description": f"حدث خطأ أثناء تحليل جزء من العقد: {str(e)}",
                    "priority": "medium",
                    "law_reference": "",
                    "risk_level": "medium"
                })

        return all_points

    def assess_risks(self, contract_text: str) -> Dict[str, Any]:
        """Assess contract risks according to legal system"""
        # Check if text is too long and needs chunking
        if len(contract_text) > 2500:
            return self._assess_risks_chunked(contract_text)

        legal_context = self.legal_manager.get_legal_context() if self.legal_manager else "Kuwaiti law"

        system_prompt = f"""أنت خبير قانوني متخصص في {legal_context}. قم بتقييم المخاطر في العقد المقدم."""

        prompt = f"""قم بتحليل المخاطر في العقد التالي وفقاً لـ{legal_context}:

العقد:
{contract_text}

قدم تقييماً شاملاً للمخاطر يتضمن:
1. المخاطر القانونية
2. المخاطر المالية
3. المخاطر التشغيلية
4. نقاط الضعف في العقد
5. درجة المخاطر الإجمالية (0-100)

أجب بصيغة JSON:
{{
    "legal_risks": [
        {{"risk": "وصف المخاطر", "severity": "عالي/متوسط/منخفض", "impact": "التأثير المحتمل"}}
    ],
    "financial_risks": [...],
    "operational_risks": [...],
    "contract_weaknesses": [...],
    "overall_score": 0-100,
    "risk_level": "عالي/متوسط/منخفض"
}}"""

        response = self._make_request(prompt, system_prompt)
        return self._parse_json_response(response, "risk_assessment")

    def check_compliance(self, contract_text: str) -> Dict[str, Any]:
        """Check contract compliance with legal requirements"""
        # Check if text is too long and needs chunking
        if len(contract_text) > 2500:
            return self._check_compliance_chunked(contract_text)

        legal_context = self.legal_manager.get_legal_context() if self.legal_manager else "Kuwaiti law"

        system_prompt = f"""أنت خبير قانوني متخصص في {legal_context}. قم بفحص امتثال العقد للقوانين المعمول بها."""

        prompt = f"""فحص امتثال العقد التالي لـ{legal_context}:

العقد:
{contract_text}

قم بفحص:
1. الامتثال للقوانين الأساسية
2. البنود المطلوبة قانونياً
3. البنود المخالفة أو المشكوك فيها
4. التوصيات للامتثال الكامل

أجب بصيغة JSON:
{{
    "compliance_score": 0-100,
    "compliant_areas": ["المجالات المتوافقة"],
    "non_compliant_areas": [
        {{"area": "المجال", "issue": "المشكلة", "severity": "عالي/متوسط/منخفض"}}
    ],
    "required_clauses": ["البنود المطلوبة المفقودة"],
    "recommendations": ["توصيات الامتثال"]
}}"""

        response = self._make_request(prompt, system_prompt)
        return self._parse_json_response(response, "compliance_check")

    def generate_recommendations(self, contract_text: str, legal_points: List[Dict], risk_assessment: Dict) -> List[Dict[str, Any]]:
        """Generate recommendations based on analysis"""
        legal_context = self.legal_manager.get_legal_context() if self.legal_manager else "Kuwaiti law"

        system_prompt = f"""أنت خبير قانوني متخصص في {legal_context}. قدم توصيات عملية لتحسين العقد."""

        prompt = f"""بناءً على التحليل التالي للعقد، قدم توصيات عملية:

العقد:
{contract_text[:1000]}...

النقاط القانونية المحددة: {len(legal_points)} نقطة
درجة المخاطر: {risk_assessment.get('overall_score', 0)}

قدم توصيات شاملة تتضمن:
1. تحسينات على البنود الحالية
2. بنود إضافية مقترحة
3. تعديلات لتقليل المخاطر
4. توصيات للامتثال القانوني

أجب بصيغة JSON:
{{
    "recommendations": [
        {{
            "title": "عنوان التوصية",
            "description": "وصف مفصل",
            "priority": "عالي/متوسط/منخفض",
            "category": "قانوني/مالي/تشغيلي",
            "implementation": "كيفية التنفيذ"
        }}
    ]
}}"""

        response = self._make_request(prompt, system_prompt)
        result = self._parse_json_response(response, "recommendations")

        # Handle both dict and list responses
        if isinstance(result, dict):
            return result.get('recommendations', [])
        elif isinstance(result, list):
            return result
        else:
            return []

    def generate_summary(self, contract_text: str) -> Dict[str, Any]:
        """Generate contract summary"""
        # Check if text is too long and needs chunking
        if len(contract_text) > 2500:
            return self._generate_summary_chunked(contract_text)

        legal_context = self.legal_manager.get_legal_context() if self.legal_manager else "Kuwaiti law"

        system_prompt = f"""أنت خبير قانوني متخصص في {legal_context}. قم بإنشاء ملخص شامل للعقد."""

        prompt = f"""قم بإنشاء ملخص شامل للعقد التالي:

العقد:
{contract_text}

يجب أن يتضمن الملخص:
1. نوع العقد والغرض منه
2. الأطراف المتعاقدة
3. الالتزامات الرئيسية
4. المدة والتواريخ المهمة
5. الشروط المالية
6. شروط الإنهاء

أجب بصيغة JSON:
{{
    "contract_type": "نوع العقد",
    "purpose": "الغرض من العقد",
    "parties": ["الأطراف"],
    "key_obligations": ["الالتزامات الرئيسية"],
    "duration": "مدة العقد",
    "financial_terms": "الشروط المالية",
    "termination_conditions": "شروط الإنهاء",
    "executive_summary": "ملخص تنفيذي"
}}"""

        response = self._make_request(prompt, system_prompt)
        return self._parse_json_response(response, "contract_summary")
    

    def _parse_json_response(self, response: str, key: str):
        """Parse JSON response from AI model - returns appropriate type based on key"""
        if not response:
            return [] if key in ['legal_points', 'recommendations'] else {}

        if not isinstance(response, str):
            return [] if key in ['legal_points', 'recommendations'] else {}

        try:
            # Try to find JSON object first (for risk_assessment, compliance_check, contract_summary, translation)
            if key in ['risk_assessment', 'compliance_check', 'contract_summary', 'translation']:
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1

                if start_idx != -1 and end_idx != 0:
                    json_str = response[start_idx:end_idx]
                    parsed = json.loads(json_str)
                    if isinstance(parsed, dict):
                        return parsed
                    else:
                        return {}

            # Try to extract JSON array (for legal_points, recommendations)
            start_idx = response.find('[')
            end_idx = response.rfind(']') + 1

            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                parsed = json.loads(json_str)
                if isinstance(parsed, list):
                    return parsed
                else:
                    return [parsed] if isinstance(parsed, dict) else []
            else:
                # If no JSON array found, try to parse the whole response
                parsed = json.loads(response)
                if key in ['risk_assessment', 'compliance_check', 'contract_summary']:
                    return parsed if isinstance(parsed, dict) else {}
                else:
                    if isinstance(parsed, list):
                        return parsed
                    elif isinstance(parsed, dict):
                        return [parsed]
                    else:
                        return []

        except json.JSONDecodeError:
            # If JSON parsing fails, create structured response from text
            return self._create_fallback_response(response, key)
        except Exception:
            return self._create_fallback_response(response, key)
    
    def _create_fallback_response(self, response: str, key: str):
        """Create structured response when JSON parsing fails"""
        # Return appropriate fallback based on key type
        if key in ['risk_assessment', 'compliance_check', 'contract_summary']:
            return {
                'overall_score': 50,
                'risk_level': 'متوسط',
                'legal_risks': ['تعذر تحليل المخاطر القانونية'],
                'financial_risks': ['تعذر تحليل المخاطر المالية'],
                'operational_risks': ['تعذر تحليل المخاطر التشغيلية'],
                'contract_weaknesses': ['تعذر تحديد نقاط الضعف'],
                'compliance_status': 'غير محدد',
                'violations': ['تعذر تحديد المخالفات'],
                'required_clauses': ['تعذر تحديد البنود المطلوبة'],
                'recommendations': ['يرجى مراجعة العقد يدوياً'],
                'contract_type': 'غير محدد',
                'parties': 'غير محدد',
                'main_obligations': 'غير محدد',
                'duration': 'غير محدد',
                'financial_terms': 'غير محدد',
                'termination_conditions': 'غير محدد',
                'executive_summary': 'تعذر إنشاء ملخص تلقائي'
            }
        elif key == 'translation':
            # For translation, try to use the response as direct translation
            if response and len(response.strip()) > 5:
                return {
                    'english_translation': response.strip(),
                    'translation_notes': ['Direct translation from AI response'],
                    'confidence_level': 'medium',
                    'key_terms': []
                }
            else:
                return {
                    'english_translation': 'Translation could not be completed',
                    'translation_notes': ['AI response was empty or invalid'],
                    'confidence_level': 'low',
                    'key_terms': []
                }

        # For list-type responses (legal_points, recommendations)
        lines = response.strip().split('\n')
        items = []

        current_item = {}
        for line in lines:
            line = line.strip()
            if not line:
                continue

            if line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '-', '*')):
                if current_item:
                    items.append(current_item)

                current_item = {
                    'title': line,
                    'description': '',
                    'priority' if key == 'legal_points' else 'urgency': 'medium',
                    'law_reference': '',
                    'risk_level': 'medium' if key == 'legal_points' else None
                }
            else:
                if current_item:
                    current_item['description'] += ' ' + line
        
        if current_item:
            items.append(current_item)
        
        return items
    
    def test_connection(self) -> bool:
        """Test connection to AI backend"""
        try:
            if self.backend == "ollama":
                response = requests.get(f"{self.base_url}/api/tags", timeout=10)
                return response.status_code == 200
            elif self.backend == "lmstudio":
                response = requests.get(f"{self.base_url}/v1/models", timeout=10)
                return response.status_code == 200
        except:
            return False
        
        return False
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        try:
            if self.backend == "ollama":
                response = requests.get(f"{self.base_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    return [model['name'] for model in data.get('models', [])]
            elif self.backend == "lmstudio":
                response = requests.get(f"{self.base_url}/v1/models", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    return [model['id'] for model in data.get('data', [])]
        except:
            pass
        
        return []

    def _make_api_request(self, prompt: str, max_tokens: int = 100) -> str:
        """Make a simple API request for testing purposes"""
        try:
            return self._make_request(prompt, "")
        except Exception as e:
            raise Exception(f"API request failed: {str(e)}")

class KuwaitiLegalKnowledge:
    """Knowledge base for Kuwaiti legal system"""
    
    CIVIL_CODE_REFERENCES = {
        "contracts": "القانون المدني رقم 67/1980 - الباب الثاني: العقود",
        "obligations": "القانون المدني رقم 67/1980 - الباب الأول: الالتزامات",
        "damages": "القانون المدني رقم 67/1980 - المواد 227-234",
        "termination": "القانون المدني رقم 67/1980 - المواد 157-171"
    }
    
    COMMERCIAL_CODE_REFERENCES = {
        "commercial_contracts": "القانون التجاري رقم 68/1980 - الباب الثالث",
        "companies": "القانون التجاري رقم 68/1980 - الباب الرابع",
        "commercial_papers": "القانون التجاري رقم 68/1980 - الباب الخامس"
    }
    
    LEGAL_TERMS = {
        "contract": "عقد",
        "party": "طرف",
        "obligation": "التزام",
        "right": "حق",
        "liability": "مسؤولية",
        "warranty": "ضمان",
        "compensation": "تعويض",
        "termination": "إنهاء",
        "breach": "إخلال",
        "force_majeure": "قوة قاهرة"
    }
    
    @classmethod
    def get_legal_reference(cls, topic: str) -> str:
        """Get legal reference for a specific topic"""
        civil_ref = cls.CIVIL_CODE_REFERENCES.get(topic)
        commercial_ref = cls.COMMERCIAL_CODE_REFERENCES.get(topic)
        
        if civil_ref:
            return civil_ref
        elif commercial_ref:
            return commercial_ref
        else:
            return "يرجى مراجعة القوانين الكويتية ذات الصلة"
    
    @classmethod
    def translate_legal_term(cls, english_term: str) -> str:
        """Translate English legal term to Arabic"""
        return cls.LEGAL_TERMS.get(english_term.lower(), english_term)

    def _assess_risks_chunked(self, contract_text: str) -> Dict[str, Any]:
        """Assess risks for large contracts by chunking"""
        chunks = self._chunk_contract_for_analysis(contract_text)
        all_risks = []

        for i, chunk in enumerate(chunks):
            try:
                chunk_risks = self.assess_risks(chunk)  # Recursive call with smaller chunk
                if chunk_risks and 'risks' in chunk_risks:
                    all_risks.extend(chunk_risks['risks'])
            except Exception as e:
                st.warning(f"⚠️ فشل تقييم مخاطر الجزء {i+1}: {str(e)}")

        # Remove duplicates and calculate overall score
        unique_risks = []
        seen_risks = set()
        for risk in all_risks:
            risk_key = risk.get('risk', '') + risk.get('category', '')
            if risk_key not in seen_risks:
                unique_risks.append(risk)
                seen_risks.add(risk_key)

        return {
            'risks': unique_risks[:15],  # Limit to top 15
            'overall_score': min(len(unique_risks) * 8, 100),
            'risk_level': 'high' if len(unique_risks) > 10 else 'medium' if len(unique_risks) > 5 else 'low',
            'total_risks_found': len(unique_risks),
            'analysis_method': 'chunked'
        }

    def _check_compliance_chunked(self, contract_text: str) -> Dict[str, Any]:
        """Check compliance for large contracts by chunking"""
        chunks = self._chunk_contract_for_analysis(contract_text)
        all_issues = []

        for i, chunk in enumerate(chunks):
            try:
                chunk_compliance = self.check_compliance(chunk)  # Recursive call with smaller chunk
                if chunk_compliance and 'issues' in chunk_compliance:
                    all_issues.extend(chunk_compliance['issues'])
            except Exception as e:
                st.warning(f"⚠️ فشل فحص امتثال الجزء {i+1}: {str(e)}")

        # Remove duplicates
        unique_issues = []
        seen_issues = set()
        for issue in all_issues:
            issue_key = issue.get('issue', '') + issue.get('category', '')
            if issue_key not in seen_issues:
                unique_issues.append(issue)
                seen_issues.add(issue_key)

        return {
            'issues': unique_issues[:10],  # Limit to top 10
            'compliance_score': max(0, 100 - len(unique_issues) * 5),
            'total_issues_found': len(unique_issues),
            'analysis_method': 'chunked'
        }

    def _generate_summary_chunked(self, contract_text: str) -> Dict[str, Any]:
        """Generate summary for large contracts by analyzing key sections"""
        chunks = self._chunk_contract_for_analysis(contract_text)

        # Generate a high-level summary without calling AI for each chunk
        return {
            'contract_type': 'عقد معقد',
            'total_length': len(contract_text),
            'word_count': len(contract_text.split()),
            'chunks_count': len(chunks),
            'complexity_level': 'عالي',
            'summary_text': f'عقد معقد يحتوي على {len(contract_text.split())} كلمة، تم تقسيمه إلى {len(chunks)} أجزاء للتحليل. يتطلب مراجعة قانونية شاملة.',
            'key_sections': [f'الجزء {i+1}' for i in range(min(len(chunks), 5))],
            'analysis_method': 'chunked',
            'recommendations': [
                'مراجعة قانونية شاملة مطلوبة',
                'فحص تفصيلي لجميع البنود',
                'استشارة قانونية متخصصة'
            ]
        }
