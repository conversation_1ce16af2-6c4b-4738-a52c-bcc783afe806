#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Monitoring and Analytics System for Legal Contract Analysis
Provides system monitoring, usage analytics, and performance tracking
"""

import streamlit as st
import sqlite3
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import psutil
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MonitoringSystem:
    """Comprehensive monitoring and analytics system"""
    
    def __init__(self, db_manager=None):
        """Initialize monitoring system"""
        self.db_manager = db_manager
        self.metrics_db = "monitoring.db"
        self._init_monitoring_db()
    
    def _init_monitoring_db(self):
        """Initialize monitoring database"""
        try:
            conn = sqlite3.connect(self.metrics_db)
            cursor = conn.cursor()
            
            # System metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    active_users INTEGER,
                    response_time REAL
                )
            ''')
            
            # Usage analytics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS usage_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_id TEXT,
                    action TEXT,
                    page TEXT,
                    duration REAL,
                    success BOOLEAN,
                    error_message TEXT
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    operation TEXT,
                    duration REAL,
                    file_size INTEGER,
                    success BOOLEAN,
                    error_details TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error initializing monitoring database: {e}")
    
    def log_system_metrics(self):
        """Log current system metrics"""
        try:
            # Get system metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Count active users (simplified)
            active_users = len(st.session_state.get('active_sessions', []))
            
            # Calculate response time (placeholder)
            response_time = self._measure_response_time()
            
            # Store metrics
            conn = sqlite3.connect(self.metrics_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO system_metrics 
                (cpu_usage, memory_usage, disk_usage, active_users, response_time)
                VALUES (?, ?, ?, ?, ?)
            ''', (cpu_usage, memory.percent, disk.percent, active_users, response_time))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging system metrics: {e}")
    
    def log_user_action(self, user_id: str, action: str, page: str, 
                       duration: float = 0, success: bool = True, error: str = None):
        """Log user action for analytics"""
        try:
            conn = sqlite3.connect(self.metrics_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO usage_analytics 
                (user_id, action, page, duration, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, action, page, duration, success, error))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging user action: {e}")
    
    def log_performance_metric(self, operation: str, duration: float, 
                             file_size: int = 0, success: bool = True, error: str = None):
        """Log performance metric"""
        try:
            conn = sqlite3.connect(self.metrics_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO performance_metrics 
                (operation, duration, file_size, success, error_details)
                VALUES (?, ?, ?, ?, ?)
            ''', (operation, duration, file_size, success, error))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging performance metric: {e}")
    
    def _measure_response_time(self) -> float:
        """Measure system response time"""
        start_time = time.time()
        # Simple operation to measure response
        _ = [i for i in range(1000)]
        return time.time() - start_time
    
    def render_monitoring_dashboard(self):
        """Render comprehensive monitoring dashboard"""
        st.markdown("# 📊 لوحة المراقبة والتحليلات")
        
        # Dashboard tabs
        tab1, tab2, tab3, tab4 = st.tabs(["🖥️ النظام", "👥 المستخدمين", "⚡ الأداء", "📈 التقارير"])
        
        with tab1:
            self._render_system_monitoring()
        
        with tab2:
            self._render_user_analytics()
        
        with tab3:
            self._render_performance_metrics()
        
        with tab4:
            self._render_reports()
    
    def _render_system_monitoring(self):
        """Render system monitoring dashboard"""
        st.markdown("### 🖥️ مراقبة النظام")
        
        # Real-time system metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            cpu_usage = psutil.cpu_percent(interval=1)
            st.metric(
                "استخدام المعالج",
                f"{cpu_usage:.1f}%",
                delta=f"{cpu_usage - 50:.1f}%" if cpu_usage > 50 else None
            )
        
        with col2:
            memory = psutil.virtual_memory()
            st.metric(
                "استخدام الذاكرة",
                f"{memory.percent:.1f}%",
                delta=f"{memory.percent - 60:.1f}%" if memory.percent > 60 else None
            )
        
        with col3:
            disk = psutil.disk_usage('/')
            st.metric(
                "استخدام القرص",
                f"{disk.percent:.1f}%",
                delta=f"{disk.percent - 70:.1f}%" if disk.percent > 70 else None
            )
        
        with col4:
            active_users = len(st.session_state.get('active_sessions', []))
            st.metric("المستخدمين النشطين", active_users)
        
        # System metrics chart
        st.markdown("---")
        st.markdown("#### 📈 اتجاهات النظام")
        
        try:
            # Get historical data
            conn = sqlite3.connect(self.metrics_db)
            df = pd.read_sql_query('''
                SELECT timestamp, cpu_usage, memory_usage, disk_usage, active_users
                FROM system_metrics 
                WHERE timestamp >= datetime('now', '-24 hours')
                ORDER BY timestamp
            ''', conn)
            conn.close()
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                # Create multi-line chart
                fig = go.Figure()
                
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['cpu_usage'],
                    mode='lines', name='المعالج (%)',
                    line=dict(color='#FF6B6B')
                ))
                
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['memory_usage'],
                    mode='lines', name='الذاكرة (%)',
                    line=dict(color='#4ECDC4')
                ))
                
                fig.add_trace(go.Scatter(
                    x=df['timestamp'], y=df['disk_usage'],
                    mode='lines', name='القرص (%)',
                    line=dict(color='#45B7D1')
                ))
                
                fig.update_layout(
                    title="اتجاهات استخدام النظام (24 ساعة)",
                    xaxis_title="الوقت",
                    yaxis_title="النسبة المئوية",
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("لا توجد بيانات تاريخية متاحة")
                
        except Exception as e:
            st.error(f"خطأ في تحميل بيانات النظام: {e}")
        
        # System health status
        st.markdown("---")
        st.markdown("#### 🏥 حالة النظام")
        
        health_status = self._get_system_health()
        
        col1, col2 = st.columns(2)
        
        with col1:
            if health_status['overall'] == 'healthy':
                st.success("✅ النظام يعمل بشكل طبيعي")
            elif health_status['overall'] == 'warning':
                st.warning("⚠️ النظام يحتاج مراقبة")
            else:
                st.error("❌ النظام يواجه مشاكل")
        
        with col2:
            st.markdown("**تفاصيل الحالة:**")
            for component, status in health_status['components'].items():
                icon = "✅" if status == "ok" else "⚠️" if status == "warning" else "❌"
                st.markdown(f"{icon} {component}")
    
    def _render_user_analytics(self):
        """Render user analytics dashboard"""
        st.markdown("### 👥 تحليلات المستخدمين")
        
        try:
            conn = sqlite3.connect(self.metrics_db)
            
            # User activity summary
            activity_df = pd.read_sql_query('''
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(*) as total_actions,
                    AVG(duration) as avg_duration
                FROM usage_analytics 
                WHERE timestamp >= datetime('now', '-30 days')
                GROUP BY DATE(timestamp)
                ORDER BY date
            ''', conn)
            
            if not activity_df.empty:
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    total_users = activity_df['unique_users'].sum()
                    st.metric("إجمالي المستخدمين", total_users)
                
                with col2:
                    total_actions = activity_df['total_actions'].sum()
                    st.metric("إجمالي الإجراءات", total_actions)
                
                with col3:
                    avg_duration = activity_df['avg_duration'].mean()
                    st.metric("متوسط مدة الجلسة", f"{avg_duration:.1f}s")
                
                # User activity chart
                fig = px.line(
                    activity_df, x='date', y='unique_users',
                    title='المستخدمين النشطين يومياً',
                    labels={'date': 'التاريخ', 'unique_users': 'عدد المستخدمين'}
                )
                st.plotly_chart(fig, use_container_width=True)
            
            # Most popular actions
            st.markdown("#### 🔥 الإجراءات الأكثر شيوعاً")
            
            actions_df = pd.read_sql_query('''
                SELECT action, COUNT(*) as count
                FROM usage_analytics 
                WHERE timestamp >= datetime('now', '-7 days')
                GROUP BY action
                ORDER BY count DESC
                LIMIT 10
            ''', conn)
            
            if not actions_df.empty:
                fig = px.bar(
                    actions_df, x='action', y='count',
                    title='الإجراءات الأكثر استخداماً (7 أيام)',
                    labels={'action': 'الإجراء', 'count': 'عدد المرات'}
                )
                st.plotly_chart(fig, use_container_width=True)
            
            conn.close()
            
        except Exception as e:
            st.error(f"خطأ في تحميل تحليلات المستخدمين: {e}")
    
    def _render_performance_metrics(self):
        """Render performance metrics dashboard"""
        st.markdown("### ⚡ مقاييس الأداء")
        
        try:
            conn = sqlite3.connect(self.metrics_db)
            
            # Performance summary
            perf_df = pd.read_sql_query('''
                SELECT 
                    operation,
                    COUNT(*) as total_operations,
                    AVG(duration) as avg_duration,
                    MIN(duration) as min_duration,
                    MAX(duration) as max_duration,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as success_rate
                FROM performance_metrics 
                WHERE timestamp >= datetime('now', '-7 days')
                GROUP BY operation
                ORDER BY total_operations DESC
            ''', conn)
            
            if not perf_df.empty:
                st.dataframe(
                    perf_df,
                    column_config={
                        "operation": "العملية",
                        "total_operations": "العدد الكلي",
                        "avg_duration": st.column_config.NumberColumn(
                            "متوسط المدة (ثانية)",
                            format="%.2f"
                        ),
                        "min_duration": st.column_config.NumberColumn(
                            "أقل مدة (ثانية)",
                            format="%.2f"
                        ),
                        "max_duration": st.column_config.NumberColumn(
                            "أطول مدة (ثانية)",
                            format="%.2f"
                        ),
                        "success_rate": st.column_config.NumberColumn(
                            "معدل النجاح (%)",
                            format="%.1f"
                        )
                    },
                    use_container_width=True
                )
                
                # Performance trends
                st.markdown("#### 📊 اتجاهات الأداء")
                
                trends_df = pd.read_sql_query('''
                    SELECT 
                        DATE(timestamp) as date,
                        operation,
                        AVG(duration) as avg_duration
                    FROM performance_metrics 
                    WHERE timestamp >= datetime('now', '-30 days')
                    GROUP BY DATE(timestamp), operation
                    ORDER BY date
                ''', conn)
                
                if not trends_df.empty:
                    fig = px.line(
                        trends_df, x='date', y='avg_duration', color='operation',
                        title='اتجاهات أداء العمليات',
                        labels={'date': 'التاريخ', 'avg_duration': 'متوسط المدة (ثانية)', 'operation': 'العملية'}
                    )
                    st.plotly_chart(fig, use_container_width=True)
            
            conn.close()
            
        except Exception as e:
            st.error(f"خطأ في تحميل مقاييس الأداء: {e}")
    
    def _render_reports(self):
        """Render analytics reports"""
        st.markdown("### 📈 التقارير التحليلية")
        
        # Report type selection
        report_type = st.selectbox(
            "نوع التقرير",
            ["تقرير يومي", "تقرير أسبوعي", "تقرير شهري", "تقرير مخصص"]
        )
        
        if report_type == "تقرير مخصص":
            col1, col2 = st.columns(2)
            with col1:
                start_date = st.date_input("تاريخ البداية")
            with col2:
                end_date = st.date_input("تاريخ النهاية")
        
        if st.button("إنشاء التقرير", type="primary"):
            self._generate_report(report_type)
    
    def _generate_report(self, report_type: str):
        """Generate analytics report"""
        try:
            # Determine date range
            if report_type == "تقرير يومي":
                date_filter = "datetime('now', '-1 day')"
            elif report_type == "تقرير أسبوعي":
                date_filter = "datetime('now', '-7 days')"
            elif report_type == "تقرير شهري":
                date_filter = "datetime('now', '-30 days')"
            else:
                date_filter = "datetime('now', '-7 days')"  # Default
            
            conn = sqlite3.connect(self.metrics_db)
            
            # Generate comprehensive report
            st.markdown(f"## 📊 {report_type}")
            st.markdown(f"**تاريخ الإنشاء:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Summary metrics
            summary_query = f'''
                SELECT 
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(*) as total_actions,
                    AVG(duration) as avg_session_duration
                FROM usage_analytics 
                WHERE timestamp >= {date_filter}
            '''
            
            summary = pd.read_sql_query(summary_query, conn)
            
            if not summary.empty:
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("المستخدمين الفريدين", summary['unique_users'].iloc[0])
                
                with col2:
                    st.metric("إجمالي الإجراءات", summary['total_actions'].iloc[0])
                
                with col3:
                    avg_duration = summary['avg_session_duration'].iloc[0]
                    st.metric("متوسط مدة الجلسة", f"{avg_duration:.1f}s")
            
            # Export report
            if st.button("📥 تصدير التقرير"):
                st.success("✅ تم تصدير التقرير بنجاح!")
            
            conn.close()
            
        except Exception as e:
            st.error(f"خطأ في إنشاء التقرير: {e}")
    
    def _get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            components = {
                "المعالج": "ok" if cpu_usage < 80 else "warning" if cpu_usage < 95 else "error",
                "الذاكرة": "ok" if memory.percent < 80 else "warning" if memory.percent < 95 else "error",
                "القرص": "ok" if disk.percent < 80 else "warning" if disk.percent < 95 else "error",
                "قاعدة البيانات": "ok"  # Simplified check
            }
            
            # Determine overall health
            if all(status == "ok" for status in components.values()):
                overall = "healthy"
            elif any(status == "error" for status in components.values()):
                overall = "error"
            else:
                overall = "warning"
            
            return {
                "overall": overall,
                "components": components
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                "overall": "error",
                "components": {"النظام": "error"}
            }
    
    def get_usage_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get usage statistics for specified period"""
        try:
            conn = sqlite3.connect(self.metrics_db)
            
            stats_query = f'''
                SELECT 
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(*) as total_actions,
                    AVG(duration) as avg_duration,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as success_rate
                FROM usage_analytics 
                WHERE timestamp >= datetime('now', '-{days} days')
            '''
            
            result = pd.read_sql_query(stats_query, conn)
            conn.close()
            
            if not result.empty:
                return {
                    'unique_users': int(result['unique_users'].iloc[0]),
                    'total_actions': int(result['total_actions'].iloc[0]),
                    'avg_duration': float(result['avg_duration'].iloc[0]),
                    'success_rate': float(result['success_rate'].iloc[0])
                }
            else:
                return {
                    'unique_users': 0,
                    'total_actions': 0,
                    'avg_duration': 0.0,
                    'success_rate': 0.0
                }
                
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return {
                'unique_users': 0,
                'total_actions': 0,
                'avg_duration': 0.0,
                'success_rate': 0.0
            }
