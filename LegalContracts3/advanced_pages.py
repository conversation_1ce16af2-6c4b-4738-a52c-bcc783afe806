#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Legal Features Pages
Additional page implementations for the Enhanced Legal Contract Analyzer
"""

import streamlit as st
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class AdvancedPagesManager:
    """Manager for advanced legal feature pages"""
    
    def __init__(self, app_instance):
        self.app = app_instance
    
    def render_contract_generator_page(self):
        """Render AI-powered contract generator page"""
        st.markdown("##### 🤖 مولد العقود الذكي")
        st.markdown("---")
        
        # Get current user
        current_user = self.app.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return
        
        user_id = current_user.get('id')
        
        # Create tabs
        tab1, tab2, tab3 = st.tabs(["🎯 إنشاء عقد جديد", "📋 القوالب المتاحة", "📊 العقود المُنشأة"])
        
        with tab1:
            self._render_contract_creation_tab(user_id)
        
        with tab2:
            self._render_contract_templates_tab(user_id)
        
        with tab3:
            self._render_generated_contracts_tab(user_id)
    
    def _render_contract_creation_tab(self, user_id: str):
        """Render contract creation tab"""
        st.markdown("### إنشاء عقد جديد")
        
        # Contract type selection (outside form)
        col1, col2 = st.columns(2)

        with col1:
            contract_type = st.selectbox(
                "نوع العقد",
                options=["consulting", "supply", "employment", "service", "partnership", "custom"],
                format_func=lambda x: {
                    "consulting": "عقد استشارات",
                    "supply": "عقد توريد",
                    "employment": "عقد عمل",
                    "service": "عقد خدمات",
                    "partnership": "عقد شراكة",
                    "custom": "عقد مخصص"
                }[x],
                key="contract_type_select"
            )

        with col2:
            legal_system = st.selectbox(
                "النظام القانوني",
                options=["kuwait", "saudi"],
                format_func=lambda x: {"kuwait": "الكويت", "saudi": "السعودية"}[x],
                key="legal_system_select"
            )
        
        # Contract details form
        with st.form("contract_generation_form"):
            st.markdown("#### تفاصيل العقد")
            
            # Basic information
            col1, col2 = st.columns(2)
            
            with col1:
                party1_name = st.text_input("الطرف الأول *", placeholder="اسم الشركة أو الشخص")
                party1_type = st.selectbox("نوع الطرف الأول", options=["company", "individual"],
                                         format_func=lambda x: {"company": "شركة", "individual": "فرد"}[x])
                party1_address = st.text_area("عنوان الطرف الأول", height=80)
            
            with col2:
                party2_name = st.text_input("الطرف الثاني *", placeholder="اسم الشركة أو الشخص")
                party2_type = st.selectbox("نوع الطرف الثاني", options=["company", "individual"],
                                         format_func=lambda x: {"company": "شركة", "individual": "فرد"}[x])
                party2_address = st.text_area("عنوان الطرف الثاني", height=80)
            
            # Contract specifics
            st.markdown("#### تفاصيل العقد")
            
            contract_subject = st.text_area("موضوع العقد *", height=100,
                                          placeholder="وصف مفصل لموضوع العقد والخدمات أو السلع المتفق عليها...")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                contract_value = st.number_input("قيمة العقد", min_value=0.0, step=100.0)
                currency = st.selectbox("العملة", options=["KWD", "SAR", "USD", "EUR"])
            
            with col2:
                start_date = st.date_input("تاريخ البداية")
                end_date = st.date_input("تاريخ الانتهاء")
            
            with col3:
                payment_terms = st.selectbox("شروط الدفع", 
                                           options=["advance", "monthly", "milestone", "completion"],
                                           format_func=lambda x: {
                                               "advance": "دفعة مقدمة",
                                               "monthly": "شهري",
                                               "milestone": "حسب المراحل",
                                               "completion": "عند الإنجاز"
                                           }[x])
            
            # Additional clauses
            st.markdown("#### بنود إضافية")
            
            include_penalty = st.checkbox("تضمين بند الغرامات التأخيرية")
            include_confidentiality = st.checkbox("تضمين بند السرية")
            include_termination = st.checkbox("تضمين شروط الإنهاء المبكر")
            include_force_majeure = st.checkbox("تضمين بند القوة القاهرة")
            
            special_conditions = st.text_area("شروط خاصة", height=100,
                                            placeholder="أي شروط أو بنود خاصة تريد تضمينها في العقد...")
            
            # AI generation options
            st.markdown("#### خيارات الذكاء الاصطناعي")
            
            col1, col2 = st.columns(2)
            
            with col1:
                ai_model = st.selectbox("نموذج الذكاء الاصطناعي", 
                                      options=["lm_studio", "ollama"],
                                      format_func=lambda x: {"lm_studio": "LM Studio", "ollama": "Ollama"}[x])
            
            with col2:
                detail_level = st.selectbox("مستوى التفصيل",
                                          options=["basic", "detailed", "comprehensive"],
                                          format_func=lambda x: {
                                              "basic": "أساسي",
                                              "detailed": "مفصل", 
                                              "comprehensive": "شامل"
                                          }[x])
            
            submitted = st.form_submit_button("🤖 إنشاء العقد", type="primary")
            
            if submitted:
                if party1_name and party2_name and contract_subject:
                    # Prepare contract parameters
                    contract_params = {
                        'contract_type': contract_type,
                        'legal_system': legal_system,
                        'party1': {
                            'name': party1_name,
                            'type': party1_type,
                            'address': party1_address
                        },
                        'party2': {
                            'name': party2_name,
                            'type': party2_type,
                            'address': party2_address
                        },
                        'subject': contract_subject,
                        'value': contract_value,
                        'currency': currency,
                        'start_date': start_date.isoformat(),
                        'end_date': end_date.isoformat(),
                        'payment_terms': payment_terms,
                        'clauses': {
                            'penalty': include_penalty,
                            'confidentiality': include_confidentiality,
                            'termination': include_termination,
                            'force_majeure': include_force_majeure
                        },
                        'special_conditions': special_conditions,
                        'detail_level': detail_level
                    }
                    
                    # Generate contract
                    with st.spinner("🤖 جاري إنشاء العقد..."):
                        try:
                            generated_contract = self.app.contract_generator.generate_contract_with_ai(
                                contract_type, contract_params, legal_system, ai_model
                            )

                            if generated_contract and generated_contract.get('contract_content'):
                                st.success("✅ تم إنشاء العقد بنجاح!")

                                # Store in session state for display outside form
                                contract_content = generated_contract['contract_content']
                                st.session_state.generated_contract = {
                                    'content': contract_content,
                                    'contract_type': contract_type,
                                    'contract_params': contract_params,
                                    'generated_contract': generated_contract
                                }

                                # Save contract
                                contract_id = self.app.contract_generator.save_generated_contract(
                                    contract_params, contract_content, user_id
                                )

                                if contract_id:
                                    st.info(f"💾 تم حفظ العقد برقم: {contract_id}")
                                    st.session_state.generated_contract['contract_id'] = contract_id
                            else:
                                st.error("❌ فشل في إنشاء العقد - لم يتم إرجاع محتوى صالح من الذكاء الاصطناعي")
                                st.info("💡 تأكد من أن خدمة الذكاء الاصطناعي (LM Studio أو Ollama) تعمل بشكل صحيح")
                                
                        except Exception as e:
                            st.error(f"❌ خطأ في إنشاء العقد: {str(e)}")
                            logger.error(f"Contract generation error: {e}")
                else:
                    st.error("❌ يرجى ملء جميع الحقول المطلوبة (*)")

        # Display generated contract outside the form
        if 'generated_contract' in st.session_state:
            contract_data = st.session_state.generated_contract

            st.markdown("---")
            st.markdown("#### العقد المُنشأ")

            # Display contract content
            st.text_area("محتوى العقد", value=contract_data['content'], height=400, key="contract_display")

            # Download button (outside form)
            col1, col2, col3 = st.columns([1, 1, 2])

            with col1:
                st.download_button(
                    label="📥 تحميل العقد",
                    data=contract_data['content'],
                    file_name=f"contract_{contract_data['contract_type']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                    mime="text/plain"
                )

            with col2:
                if st.button("🗑️ مسح العقد"):
                    del st.session_state.generated_contract
                    st.rerun()

            # Confidence score (if available)
            try:
                confidence_score = self.app.confidence_scorer.calculate_confidence_score(
                    contract_data['content'], contract_data['contract_params']
                )

                st.markdown("#### تقييم الثقة")
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("نقاط الثقة", f"{confidence_score['overall_confidence_score']}%")

                with col2:
                    st.metric("مستوى الثقة", confidence_score['confidence_level'])

                with col3:
                    if confidence_score.get('recommendations'):
                        st.info("💡 " + confidence_score['recommendations'][0])
            except Exception as conf_e:
                logger.warning(f"Could not calculate confidence score: {conf_e}")
                st.info("📊 تم إنشاء العقد بنجاح - تقييم الثقة غير متاح حالياً")
    
    def _render_contract_templates_tab(self, user_id: str):
        """Render contract templates tab"""
        st.markdown("### قوالب العقود المتاحة")
        
        # Get available templates
        templates = self.app.contract_generator.get_contract_templates()
        
        if not templates:
            st.info("📝 لا توجد قوالب متاحة حالياً")
            return
        
        # Display templates
        for template in templates:
            with st.expander(f"📋 {template['name']} - {template.get('category', 'عام')}", expanded=False):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.markdown(f"**الوصف:** {template['description']}")
                    st.markdown(f"**النظام القانوني:** {template['legal_system']}")
                    st.markdown(f"**الفئة:** {template.get('category', 'عام')}")

                    if template.get('instructions'):
                        st.markdown(f"**التعليمات:** {template['instructions']}")

                    if template.get('variables'):
                        st.markdown("**المتغيرات المتاحة:**")
                        for var, desc in template['variables'].items():
                            st.write(f"• {var}: {desc}")

                with col2:
                    # Show template info
                    st.info(f"**الفئة:** {template.get('category', 'عام')}")

                    if template.get('risk_assessment'):
                        st.warning(f"**تقييم المخاطر:** {template['risk_assessment']}")

                    if st.button(f"🎯 استخدام القالب", key=f"use_template_{template['id']}"):
                        st.session_state.selected_template = template['id']
                        st.success("✅ تم تحديد القالب!")
                        st.info("💡 انتقل إلى تبويب 'إنشاء عقد جديد' لاستخدام القالب")
    
    def _render_generated_contracts_tab(self, user_id: str):
        """Render generated contracts history tab"""
        st.markdown("### العقود المُنشأة")
        
        # Get user's generated contracts
        contracts = self.app.contract_generator.get_user_contracts(user_id)
        
        if not contracts:
            st.info("📝 لم يتم إنشاء أي عقود بعد")
            return
        
        st.markdown(f"**إجمالي العقود: {len(contracts)}**")
        
        # Display contracts
        for contract in contracts:
            # Handle different possible field names for contract type
            contract_type = contract.get('contract_type', contract.get('category', 'عقد'))
            created_date = contract.get('created_at', datetime.now().isoformat())[:10]

            with st.expander(f"📄 {contract_type} - {created_date}", expanded=False):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.markdown(f"**نوع العقد:** {contract_type}")
                    st.markdown(f"**النظام القانوني:** {contract.get('legal_system', 'غير محدد')}")

                    if contract.get('parameters'):
                        try:
                            params = json.loads(contract['parameters']) if isinstance(contract['parameters'], str) else contract['parameters']
                            st.markdown(f"**الأطراف:** {params.get('party1', {}).get('name', 'غير محدد')} و {params.get('party2', {}).get('name', 'غير محدد')}")
                            st.markdown(f"**القيمة:** {params.get('value', 0)} {params.get('currency', '')}")
                        except:
                            st.markdown("**المعاملات:** غير متاحة")

                with col2:
                    st.metric("تاريخ الإنشاء", created_date)

                    if st.button(f"👁️ عرض العقد", key=f"view_contract_{contract.get('id', 'unknown')}"):
                        st.text_area("محتوى العقد", value=contract.get('content', 'المحتوى غير متاح'), height=300, key=f"content_{contract.get('id', 'unknown')}")

                    if st.button(f"📥 تحميل", key=f"download_contract_{contract.get('id', 'unknown')}"):
                        st.download_button(
                            label="💾 تحميل العقد",
                            data=contract.get('content', 'المحتوى غير متاح'),
                            file_name=f"contract_{contract.get('id', 'unknown')}.txt",
                            mime="text/plain",
                            key=f"download_btn_{contract.get('id', 'unknown')}"
                        )
    
    def render_compliance_checker_page(self):
        """Render compliance checker page"""
        st.markdown("##### ✅ فاحص الامتثال القانوني")
        st.markdown("---")
        
        # Get current user
        current_user = self.app.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return
        
        # Create tabs
        tab1, tab2, tab3 = st.tabs(["🔍 فحص الامتثال", "📋 قواعد الامتثال", "📊 تقارير الامتثال"])
        
        with tab1:
            self._render_compliance_check_tab()
        
        with tab2:
            self._render_compliance_rules_tab()
        
        with tab3:
            self._render_compliance_reports_tab()
    
    def _render_compliance_check_tab(self):
        """Render compliance checking tab"""
        st.markdown("### فحص امتثال العقد")
        
        # Contract input
        contract_input_method = st.radio(
            "طريقة إدخال العقد",
            options=["text", "file"],
            format_func=lambda x: {"text": "نص مباشر", "file": "رفع ملف"}[x]
        )
        
        contract_text = ""
        
        if contract_input_method == "text":
            contract_text = st.text_area("نص العقد", height=200, 
                                       placeholder="الصق نص العقد هنا للفحص...")
        else:
            uploaded_file = st.file_uploader("اختر ملف العقد", type=['txt', 'pdf', 'docx'])
            if uploaded_file:
                # Extract text from file (simplified)
                if uploaded_file.type == "text/plain":
                    contract_text = str(uploaded_file.read(), "utf-8")
                else:
                    st.info("📄 تم رفع الملف - سيتم استخراج النص تلقائياً")
                    contract_text = "نص العقد المستخرج من الملف..."  # Placeholder
        
        # Compliance settings
        col1, col2 = st.columns(2)
        
        with col1:
            legal_system = st.selectbox(
                "النظام القانوني للفحص",
                options=["kuwait", "saudi"],
                format_func=lambda x: {"kuwait": "الكويت", "saudi": "السعودية"}[x]
            )
        
        with col2:
            check_categories = st.multiselect(
                "فئات الفحص",
                options=["general", "employment", "commercial", "financial", "data_protection"],
                default=["general"],
                format_func=lambda x: {
                    "general": "عام",
                    "employment": "عمل",
                    "commercial": "تجاري",
                    "financial": "مالي",
                    "data_protection": "حماية البيانات"
                }[x]
            )
        
        if st.button("✅ فحص الامتثال", type="primary") and contract_text:
            with st.spinner("🔍 جاري فحص الامتثال..."):
                try:
                    compliance_result = self.app.compliance_checker.check_contract_compliance(
                        contract_text, legal_system, check_categories
                    )
                    
                    if compliance_result:
                        st.markdown("#### نتائج فحص الامتثال")
                        
                        # Overall compliance score
                        overall_score = compliance_result['overall_compliance_score']
                        score_color = "🟢" if overall_score >= 80 else "🟡" if overall_score >= 60 else "🔴"
                        
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            st.metric("نقاط الامتثال الإجمالية", f"{score_color} {overall_score}%")
                        
                        with col2:
                            st.metric("القواعد المطبقة", compliance_result['total_rules_checked'])
                        
                        with col3:
                            st.metric("المخالفات المكتشفة", len(compliance_result['violations']))
                        
                        # Violations details
                        if compliance_result['violations']:
                            st.markdown("#### ⚠️ المخالفات المكتشفة")
                            
                            for violation in compliance_result['violations']:
                                severity_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}[violation['severity']]
                                
                                with st.expander(f"{severity_color} {violation['rule_name']}", expanded=True):
                                    st.markdown(f"**الوصف:** {violation['description']}")
                                    st.markdown(f"**الشدة:** {violation['severity']}")
                                    
                                    if violation['recommendation']:
                                        st.markdown(f"**التوصية:** {violation['recommendation']}")
                                    
                                    if violation['legal_reference']:
                                        st.markdown(f"**المرجع القانوني:** {violation['legal_reference']}")
                        
                        else:
                            st.success("✅ لم يتم اكتشاف أي مخالفات امتثال")
                        
                        # Recommendations
                        if compliance_result.get('recommendations'):
                            st.markdown("##### التوصيات")
                            for rec in compliance_result['recommendations']:
                                st.info(f"• {rec}")
                    
                    else:
                        st.error("❌ فشل في فحص الامتثال")
                        
                except Exception as e:
                    st.error(f"❌ خطأ في فحص الامتثال: {str(e)}")
                    logger.error(f"Compliance check error: {e}")
    
    def _render_compliance_rules_tab(self):
        """Render compliance rules management tab"""
        st.markdown("### إدارة قواعد الامتثال")
        
        # Get compliance rules
        rules = self.app.compliance_checker.get_compliance_rules()
        
        # Filter controls
        col1, col2 = st.columns(2)
        
        with col1:
            filter_system = st.selectbox(
                "تصفية حسب النظام",
                options=["", "kuwait", "saudi"],
                format_func=lambda x: {"": "جميع الأنظمة", "kuwait": "الكويت", "saudi": "السعودية"}[x]
            )
        
        with col2:
            filter_category = st.selectbox(
                "تصفية حسب الفئة",
                options=["", "general", "employment", "commercial", "financial", "data_protection"],
                format_func=lambda x: {
                    "": "جميع الفئات",
                    "general": "عام",
                    "employment": "عمل", 
                    "commercial": "تجاري",
                    "financial": "مالي",
                    "data_protection": "حماية البيانات"
                }[x]
            )
        
        # Filter rules
        filtered_rules = rules
        if filter_system:
            filtered_rules = [r for r in filtered_rules if r['legal_system'] == filter_system]
        if filter_category:
            filtered_rules = [r for r in filtered_rules if r.get('rule_type', r.get('category', '')) == filter_category]
        
        st.markdown(f"**القواعد المتاحة: {len(filtered_rules)}**")
        
        # Display rules
        for rule in filtered_rules:
            rule_type = rule.get('rule_type', rule.get('category', 'عام'))
            with st.expander(f"📋 {rule['name']} - {rule_type}", expanded=False):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**الوصف:** {rule['description']}")
                    st.markdown(f"**النوع:** {rule_type}")
                    st.markdown(f"**النظام القانوني:** {rule['legal_system']}")

                    if rule.get('jurisdiction'):
                        st.markdown(f"**الاختصاص:** {rule['jurisdiction']}")

                    if rule.get('rule_content'):
                        content = rule['rule_content']
                        if isinstance(content, dict):
                            if content.get('pattern'):
                                st.markdown(f"**النمط:** `{content['pattern']}`")
                            if content.get('legal_reference'):
                                st.markdown(f"**المرجع القانوني:** {content['legal_reference']}")

                with col2:
                    severity = rule.get('severity', 'medium')
                    severity_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(severity, "🟡")
                    st.metric("الشدة", f"{severity_color} {severity}")
                    st.info(f"**النظام:** {rule['legal_system']}")
                    st.info(f"**النوع:** {rule_type}")
    
    def _render_compliance_reports_tab(self):
        """Render compliance reports tab"""
        st.markdown("### تقارير الامتثال")
        
        st.info("🚧 هذه الميزة قيد التطوير - ستتيح عرض تقارير مفصلة عن حالة الامتثال")
        
        # Placeholder for future implementation
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### إحصائيات الامتثال")
            st.metric("معدل الامتثال العام", "85%")
            st.metric("العقود المفحوصة", "127")
            st.metric("المخالفات المكتشفة", "23")
        
        with col2:
            st.markdown("#### أهم المخالفات")
            st.write("• بنود الإنهاء غير واضحة (15 مرة)")
            st.write("• شروط الدفع غير محددة (8 مرات)")
            st.write("• عدم تحديد القانون الواجب التطبيق (5 مرات)")
    
    def render_custom_prompts_page(self):
        """Render custom prompts management page"""
        st.markdown("##### 📝 القوالب المخصصة للتحليل")
        st.markdown("---")
        
        # Get current user
        current_user = self.app.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return
        
        user_id = current_user.get('id')
        
        # Create tabs
        tab1, tab2, tab3 = st.tabs(["🔍 استعراض القوالب", "➕ إنشاء قالب جديد", "📊 إحصائيات الاستخدام"])
        
        with tab1:
            self._render_prompts_browse_tab(user_id)
        
        with tab2:
            self._render_prompts_create_tab(user_id)
        
        with tab3:
            self._render_prompts_statistics_tab()
    
    def _render_prompts_browse_tab(self, user_id: str):
        """Render prompts browsing tab"""
        st.markdown("### استعراض القوالب المتاحة")
        
        # Filter controls
        col1, col2, col3 = st.columns(3)
        
        with col1:
            filter_system = st.selectbox(
                "النظام القانوني",
                options=["", "kuwait", "saudi"],
                format_func=lambda x: {"": "جميع الأنظمة", "kuwait": "الكويت", "saudi": "السعودية"}[x]
            )
        
        with col2:
            categories = self.app.prompts_manager.get_prompt_categories(filter_system if filter_system else None)
            category_options = [""] + [cat['category'] for cat in categories]
            filter_category = st.selectbox("الفئة", options=category_options)
        
        with col3:
            show_public = st.checkbox("إظهار القوالب العامة", value=True)
        
        # Get prompts
        prompts = self.app.prompts_manager.get_custom_prompts(
            created_by=user_id,
            category=filter_category if filter_category else None,
            legal_system=filter_system if filter_system else None,
            include_public=show_public
        )
        
        st.markdown(f"**القوالب المتاحة: {len(prompts)}**")
        
        # Display prompts
        for prompt in prompts:
            with st.expander(f"🎯 {prompt['name']} - {prompt['category']}", expanded=False):
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**الوصف:** {prompt['description']}")
                    
                    # Show prompt preview
                    if st.button(f"👁️ معاينة القالب", key=f"preview_{prompt['id']}"):
                        st.text_area("محتوى القالب", value=prompt['prompt_text'], height=200, key=f"preview_text_{prompt['id']}")
                
                with col2:
                    st.metric("مرات الاستخدام", prompt['usage_count'])
                    
                    if prompt['legal_system']:
                        st.info(f"**النظام:** {prompt['legal_system']}")
                    
                    st.info(f"**النوع:** {prompt['analysis_type']}")
                    
                    # Action buttons
                    if st.button(f"🎯 استخدام القالب", key=f"use_{prompt['id']}", type="primary"):
                        st.session_state.selected_custom_prompt = prompt['id']
                        st.success("✅ تم تحديد القالب!")
                        st.info("💡 انتقل إلى صفحة التحليل لاستخدام القالب المخصص")
                    
                    # Edit/delete for own prompts
                    if prompt['created_by'] == user_id:
                        if st.button(f"🗑️ حذف", key=f"delete_{prompt['id']}"):
                            if self.app.prompts_manager.delete_prompt(prompt['id'], user_id):
                                st.success("✅ تم حذف القالب")
                                st.rerun()
                            else:
                                st.error("❌ فشل في حذف القالب")
    
    def _render_prompts_create_tab(self, user_id: str):
        """Render prompt creation tab"""
        st.markdown("### ➕ إنشاء قالب تحليل جديد")
        
        with st.form("create_prompt_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                name = st.text_input("اسم القالب *", placeholder="مثال: تحليل مخاطر العقود التجارية")
                category = st.text_input("الفئة *", placeholder="مثال: تحليل المخاطر")
                legal_system = st.selectbox("النظام القانوني", 
                                          options=["", "kuwait", "saudi"],
                                          format_func=lambda x: {"": "عام", "kuwait": "الكويت", "saudi": "السعودية"}[x])
            
            with col2:
                analysis_type = st.selectbox("نوع التحليل",
                                           options=["custom", "risk_analysis", "compliance_review", "liability_analysis"],
                                           format_func=lambda x: {
                                               "custom": "مخصص",
                                               "risk_analysis": "تحليل مخاطر",
                                               "compliance_review": "مراجعة امتثال",
                                               "liability_analysis": "تحليل مسؤولية"
                                           }[x])
                
                is_public = st.checkbox("جعل القالب متاحاً للجميع")
            
            description = st.text_area("وصف القالب *", height=100,
                                     placeholder="وصف مختصر لما يقوم به هذا القالب...")
            
            st.markdown("#### محتوى القالب")
            st.info("💡 استخدم {contract_text} كمتغير لنص العقد في القالب")
            
            prompt_text = st.text_area("نص القالب *", height=300,
                                     placeholder="""مثال:
أنت خبير قانوني متخصص. قم بتحليل العقد التالي مع التركيز على:

1. النقاط الرئيسية:
   - تحديد الأطراف
   - الالتزامات الأساسية
   - الشروط المالية

2. تقييم المخاطر:
   - المخاطر القانونية
   - المخاطر المالية
   - المخاطر التشغيلية

العقد للتحليل:
{contract_text}

يرجى تقديم تحليل شامل مع التوصيات.""")
            
            submitted = st.form_submit_button("➕ إنشاء القالب", type="primary")
            
            if submitted:
                if name and category and description and prompt_text:
                    prompt_id = self.app.prompts_manager.add_custom_prompt(
                        name=name,
                        description=description,
                        prompt_text=prompt_text,
                        category=category,
                        legal_system=legal_system if legal_system else None,
                        analysis_type=analysis_type,
                        is_public=is_public,
                        created_by=user_id
                    )
                    
                    if prompt_id:
                        st.success("✅ تم إنشاء القالب بنجاح!")
                        st.rerun()
                    else:
                        st.error("❌ فشل في إنشاء القالب")
                else:
                    st.error("❌ يرجى ملء جميع الحقول المطلوبة (*)")
    
    def _render_prompts_statistics_tab(self):
        """Render prompts usage statistics tab"""
        st.markdown("### إحصائيات استخدام القوالب")
        
        # Get categories statistics
        categories = self.app.prompts_manager.get_prompt_categories()
        
        if categories:
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("#### توزيع الفئات")
                category_data = {cat['category']: cat['count'] for cat in categories}
                st.bar_chart(category_data)
            
            with col2:
                st.markdown("#### 📊 إحصائيات عامة")
                total_prompts = sum(cat['count'] for cat in categories)
                st.metric("إجمالي القوالب", total_prompts)
                st.metric("عدد الفئات", len(categories))
                
                # Most used categories
                st.markdown("**أكثر الفئات استخداماً:**")
                sorted_categories = sorted(categories, key=lambda x: x['count'], reverse=True)
                for cat in sorted_categories[:5]:
                    st.write(f"• {cat['category']}: {cat['count']} قالب")
        
        else:
            st.info("📝 لا توجد إحصائيات متاحة حالياً")
