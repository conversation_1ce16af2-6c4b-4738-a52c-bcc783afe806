#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Custom Legal Prompts Manager
User-defined prompts system for specific legal scenarios
"""

import json
import logging
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class CustomPromptsManager:
    """Manages custom legal analysis prompts"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self._initialize_default_prompts()
    
    def _initialize_default_prompts(self):
        """Initialize default custom prompts"""
        
        default_prompts = [
            {
                'name': 'تحليل مخاطر العقود التجارية',
                'description': 'تحليل متخصص للمخاطر في العقود التجارية',
                'prompt_text': '''
أنت خبير قانوني متخصص في تحليل المخاطر التجارية. قم بتحليل العقد التالي مع التركيز على:

1. المخاطر المالية:
   - شروط الدفع والتحصيل
   - الضمانات المالية
   - العقوبات المالية

2. المخاطر التشغيلية:
   - التزامات الأداء
   - معايير الجودة
   - مواعيد التسليم

3. المخاطر القانونية:
   - الامتثال للقوانين
   - تسوية النزاعات
   - القانون الواجب التطبيق

4. المخاطر التعاقدية:
   - وضوح الشروط
   - التزامات الأطراف
   - شروط الإنهاء

العقد للتحليل:
{contract_text}

يرجى تقديم تحليل شامل مع تقييم مستوى المخاطر (منخفض/متوسط/عالي) لكل فئة.
                ''',
                'category': 'تحليل المخاطر',
                'legal_system': 'kuwait',
                'analysis_type': 'risk_analysis'
            },
            {
                'name': 'مراجعة عقود العمل السعودية',
                'description': 'مراجعة متخصصة لعقود العمل وفقاً للنظام السعودي',
                'prompt_text': '''
أنت خبير في قانون العمل السعودي. قم بمراجعة عقد العمل التالي للتأكد من امتثاله لنظام العمل السعودي:

1. البيانات الأساسية:
   - هوية صاحب العمل والعامل
   - المسمى الوظيفي ووصف العمل
   - مكان العمل

2. الشروط المالية:
   - الراتب الأساسي والبدلات
   - ساعات العمل والعمل الإضافي
   - الإجازات والمكافآت

3. متطلبات السعودة:
   - الامتثال لبرنامج نطاقات
   - متطلبات التوطين

4. الحقوق والواجبات:
   - حقوق العامل
   - التزامات صاحب العمل
   - شروط الإنهاء

5. الامتثال التنظيمي:
   - التأمينات الاجتماعية
   - تأمين ساند
   - متطلبات وزارة الموارد البشرية

العقد للمراجعة:
{contract_text}

يرجى تحديد أي مخالفات أو نواقص وتقديم توصيات للتحسين.
                ''',
                'category': 'عقود العمل',
                'legal_system': 'saudi',
                'analysis_type': 'employment_review'
            },
            {
                'name': 'تحليل بنود المسؤولية',
                'description': 'تحليل متخصص لبنود المسؤولية والإعفاء',
                'prompt_text': '''
أنت خبير قانوني متخصص في بنود المسؤولية. قم بتحليل العقد التالي مع التركيز على:

1. بنود تحديد المسؤولية:
   - نطاق المسؤولية
   - استثناءات المسؤولية
   - حدود التعويض

2. بنود الإعفاء:
   - القوة القاهرة
   - الظروف الاستثنائية
   - إعفاء الأطراف الثالثة

3. التأمين والضمانات:
   - متطلبات التأمين
   - الضمانات المطلوبة
   - تغطية المخاطر

4. التقييم القانوني:
   - قابلية الإنفاذ
   - العدالة في التوزيع
   - الامتثال للقانون

العقد للتحليل:
{contract_text}

يرجى تقييم فعالية بنود المسؤولية وتقديم توصيات للتحسين.
                ''',
                'category': 'المسؤولية القانونية',
                'legal_system': 'kuwait',
                'analysis_type': 'liability_analysis'
            },
            {
                'name': 'مراجعة الامتثال التنظيمي',
                'description': 'مراجعة شاملة للامتثال للأنظمة واللوائح',
                'prompt_text': '''
أنت خبير في الامتثال التنظيمي. قم بمراجعة العقد التالي للتأكد من امتثاله للأنظمة واللوائح:

1. الامتثال القانوني العام:
   - القوانين الأساسية
   - اللوائح التنفيذية
   - المعايير المهنية

2. متطلبات الترخيص:
   - التراخيص المطلوبة
   - التجديدات والتحديثات
   - الامتثال للشروط

3. المتطلبات المالية:
   - الضرائب والرسوم
   - التقارير المالية
   - الامتثال المصرفي

4. حماية البيانات والخصوصية:
   - قوانين حماية البيانات
   - الخصوصية والسرية
   - الأمن السيبراني

5. المسؤولية الاجتماعية:
   - معايير العمل
   - حماية البيئة
   - المسؤولية المجتمعية

العقد للمراجعة:
{contract_text}

يرجى تحديد أي مخاطر امتثال وتقديم خطة للتصحيح.
                ''',
                'category': 'الامتثال التنظيمي',
                'legal_system': 'saudi',
                'analysis_type': 'compliance_review'
            },
            {
                'name': 'تحليل العقود الدولية',
                'description': 'تحليل متخصص للعقود ذات الطابع الدولي',
                'prompt_text': '''
أنت خبير في القانون التجاري الدولي. قم بتحليل العقد التالي مع التركيز على الجوانب الدولية:

1. الاختصاص القضائي:
   - القانون الواجب التطبيق
   - المحاكم المختصة
   - تنفيذ الأحكام

2. تسوية النزاعات الدولية:
   - التحكيم الدولي
   - الوساطة
   - القواعد المطبقة

3. العملات والمدفوعات:
   - العملات المستخدمة
   - تقلبات أسعار الصرف
   - طرق التحويل

4. الامتثال الدولي:
   - العقوبات الدولية
   - قوانين مكافحة الفساد
   - معايير التجارة الدولية

5. المخاطر الجيوسياسية:
   - المخاطر السياسية
   - تغيير القوانين
   - القيود التجارية

العقد للتحليل:
{contract_text}

يرجى تقييم المخاطر الدولية وتقديم استراتيجيات للحماية.
                ''',
                'category': 'العقود الدولية',
                'legal_system': 'kuwait',
                'analysis_type': 'international_analysis'
            }
        ]
        
        # Add prompts if they don't exist
        try:
            existing_prompts = self.get_custom_prompts()
            if len(existing_prompts) == 0:
                logger.info("Initializing default custom prompts...")
                
                for prompt in default_prompts:
                    prompt['created_by'] = 'system'  # Add system as creator for default prompts
                    self.add_custom_prompt(**prompt)
                
                logger.info(f"Added {len(default_prompts)} default prompts")
        except Exception as e:
            logger.error(f"Error initializing default prompts: {e}")
    
    def add_custom_prompt(self, name: str, description: str, prompt_text: str,
                         category: str = None, legal_system: str = None,
                         analysis_type: str = 'custom', is_public: bool = False,
                         created_by: str = None) -> Optional[str]:
        """Add a new custom prompt"""
        try:
            prompt_id = str(uuid.uuid4())

            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO custom_prompts 
                    (id, name, description, prompt_text, category, legal_system,
                     analysis_type, is_public, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (prompt_id, name, description, prompt_text, category, legal_system,
                      analysis_type, is_public, created_by))
                
                conn.commit()
                logger.info(f"Custom prompt added: {prompt_id}")
                return prompt_id
                
        except Exception as e:
            logger.error(f"Error adding custom prompt: {e}")
            return None
    
    def get_custom_prompts(self, created_by: str = None, category: str = None,
                          legal_system: str = None, include_public: bool = True) -> List[Dict[str, Any]]:
        """Get custom prompts with optional filtering"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT id, name, description, prompt_text, category, legal_system,
                           analysis_type, is_public, usage_count, created_by,
                           created_at, updated_at
                    FROM custom_prompts
                    WHERE 1=1
                '''
                params = []
                
                if created_by:
                    if include_public:
                        query += ' AND (created_by = ? OR is_public = 1)'
                        params.append(created_by)
                    else:
                        query += ' AND created_by = ?'
                        params.append(created_by)
                elif include_public:
                    query += ' AND is_public = 1'
                
                if category:
                    query += ' AND category = ?'
                    params.append(category)
                
                if legal_system:
                    query += ' AND (legal_system = ? OR legal_system IS NULL)'
                    params.append(legal_system)
                
                query += ' ORDER BY usage_count DESC, name'
                
                cursor.execute(query, params)
                
                prompts = []
                for row in cursor.fetchall():
                    prompts.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'prompt_text': row[3],
                        'category': row[4],
                        'legal_system': row[5],
                        'analysis_type': row[6],
                        'is_public': bool(row[7]),
                        'usage_count': row[8],
                        'created_by': row[9],
                        'created_at': row[10],
                        'updated_at': row[11]
                    })
                
                return prompts
                
        except Exception as e:
            logger.error(f"Error getting custom prompts: {e}")
            return []
    
    def get_prompt_by_id(self, prompt_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific prompt by ID"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, name, description, prompt_text, category, legal_system,
                           analysis_type, is_public, usage_count, created_by,
                           created_at, updated_at
                    FROM custom_prompts 
                    WHERE id = ?
                ''', (prompt_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'prompt_text': row[3],
                        'category': row[4],
                        'legal_system': row[5],
                        'analysis_type': row[6],
                        'is_public': bool(row[7]),
                        'usage_count': row[8],
                        'created_by': row[9],
                        'created_at': row[10],
                        'updated_at': row[11]
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting prompt by ID: {e}")
            return None
    
    def update_prompt(self, prompt_id: str, **kwargs) -> bool:
        """Update a custom prompt"""
        try:
            if not kwargs:
                return False
            
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                # Build update query dynamically
                set_clauses = []
                params = []
                
                for key, value in kwargs.items():
                    if key in ['name', 'description', 'prompt_text', 'category',
                              'legal_system', 'analysis_type', 'is_public']:
                        set_clauses.append(f'{key} = ?')
                        params.append(value)
                
                if not set_clauses:
                    return False
                
                set_clauses.append('updated_at = CURRENT_TIMESTAMP')
                params.append(prompt_id)
                
                query = f'''
                    UPDATE custom_prompts 
                    SET {', '.join(set_clauses)}
                    WHERE id = ?
                '''
                
                cursor.execute(query, params)
                conn.commit()
                
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error updating prompt: {e}")
            return False
    
    def delete_prompt(self, prompt_id: str, user_id: str) -> bool:
        """Delete a custom prompt (only by creator)"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    DELETE FROM custom_prompts 
                    WHERE id = ? AND created_by = ?
                ''', (prompt_id, user_id))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error deleting prompt: {e}")
            return False
    
    def increment_usage_count(self, prompt_id: str) -> bool:
        """Increment usage count for a prompt"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE custom_prompts 
                    SET usage_count = usage_count + 1, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (prompt_id,))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error incrementing usage count: {e}")
            return False
    
    def get_prompt_categories(self, legal_system: str = None) -> List[Dict[str, Any]]:
        """Get available prompt categories"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT category, COUNT(*) as count
                    FROM custom_prompts 
                    WHERE category IS NOT NULL
                '''
                params = []
                
                if legal_system:
                    query += ' AND (legal_system = ? OR legal_system IS NULL)'
                    params.append(legal_system)
                
                query += ' GROUP BY category ORDER BY count DESC, category'
                
                cursor.execute(query, params)
                
                categories = []
                for row in cursor.fetchall():
                    categories.append({
                        'category': row[0],
                        'count': row[1]
                    })
                
                return categories
                
        except Exception as e:
            logger.error(f"Error getting prompt categories: {e}")
            return []
    
    def apply_custom_prompt(self, prompt_id: str, contract_text: str) -> str:
        """Apply a custom prompt to contract text"""
        try:
            prompt = self.get_prompt_by_id(prompt_id)
            if not prompt:
                return None
            
            # Increment usage count
            self.increment_usage_count(prompt_id)
            
            # Replace placeholder with contract text
            formatted_prompt = prompt['prompt_text'].replace('{contract_text}', contract_text)
            
            return formatted_prompt
            
        except Exception as e:
            logger.error(f"Error applying custom prompt: {e}")
            return None
