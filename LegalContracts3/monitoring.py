"""
Monitoring and Logging System
Developed by MAXBIT LLC © 2025
"""

import logging
import logging.handlers
import time
import os
import psutil
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import streamlit as st
from config_manager import get_config

@dataclass
class SystemMetrics:
    """System performance metrics"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    uptime_seconds: float

@dataclass
class ApplicationMetrics:
    """Application-specific metrics"""
    timestamp: str
    active_users: int
    contracts_analyzed: int
    api_requests: int
    errors_count: int
    response_time_avg: float
    database_connections: int

@dataclass
class SecurityEvent:
    """Security event logging"""
    timestamp: str
    event_type: str  # login_success, login_failure, unauthorized_access, etc.
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    details: Dict[str, Any]

class LoggerSetup:
    """Centralized logging setup"""
    
    def __init__(self):
        self.config = get_config()
        self.setup_logging()
    
    def setup_logging(self):
        """Setup application logging"""
        # Create logs directory
        log_dir = Path(self.config.logging.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.config.logging.level.upper()),
            format=self.config.logging.format,
            handlers=[
                logging.StreamHandler(),  # Console output
                logging.handlers.RotatingFileHandler(
                    self.config.logging.file_path,
                    maxBytes=self.config.logging.max_file_size_mb * 1024 * 1024,
                    backupCount=self.config.logging.backup_count,
                    encoding='utf-8'
                )
            ]
        )
        
        # Create specialized loggers
        self.app_logger = logging.getLogger('app')
        self.security_logger = logging.getLogger('security')
        self.performance_logger = logging.getLogger('performance')
        self.api_logger = logging.getLogger('api')
        
        # Security logger with separate file
        security_handler = logging.handlers.RotatingFileHandler(
            'logs/security.log',
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        security_handler.setFormatter(logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        ))
        self.security_logger.addHandler(security_handler)
        self.security_logger.setLevel(logging.INFO)
        
        # Performance logger
        performance_handler = logging.handlers.RotatingFileHandler(
            'logs/performance.log',
            maxBytes=10 * 1024 * 1024,
            backupCount=5,
            encoding='utf-8'
        )
        performance_handler.setFormatter(logging.Formatter(
            '%(asctime)s - PERFORMANCE - %(message)s'
        ))
        self.performance_logger.addHandler(performance_handler)
        self.performance_logger.setLevel(logging.INFO)
        
        # API logger
        api_handler = logging.handlers.RotatingFileHandler(
            'logs/api.log',
            maxBytes=10 * 1024 * 1024,
            backupCount=5,
            encoding='utf-8'
        )
        api_handler.setFormatter(logging.Formatter(
            '%(asctime)s - API - %(levelname)s - %(message)s'
        ))
        self.api_logger.addHandler(api_handler)
        self.api_logger.setLevel(logging.INFO)

class SystemMonitor:
    """System performance monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
        self.start_time = time.time()
        self.metrics_history: List[SystemMetrics] = []
        self.max_history = 1000  # Keep last 1000 metrics
    
    def collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # Process count
            process_count = len(psutil.pids())
            
            # Uptime
            uptime = time.time() - self.start_time
            
            metrics = SystemMetrics(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                network_io=network_io,
                process_count=process_count,
                uptime_seconds=uptime
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history:
                self.metrics_history.pop(0)
            
            # Log if thresholds exceeded
            if cpu_percent > 80:
                self.logger.warning(f"High CPU usage: {cpu_percent}%")
            if memory.percent > 80:
                self.logger.warning(f"High memory usage: {memory.percent}%")
            if disk.percent > 90:
                self.logger.warning(f"High disk usage: {disk.percent}%")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return None
    
    def get_metrics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_metrics = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]
        
        if not recent_metrics:
            return {}
        
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        
        return {
            "period_hours": hours,
            "data_points": len(recent_metrics),
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values),
                "max": max(memory_values),
                "min": min(memory_values)
            },
            "uptime_hours": recent_metrics[-1].uptime_seconds / 3600
        }

class ApplicationMonitor:
    """Application-specific monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger('app')
        self.metrics: Dict[str, Any] = {
            'contracts_analyzed': 0,
            'api_requests': 0,
            'errors': 0,
            'response_times': [],
            'active_sessions': set()
        }
    
    def log_contract_analysis(self, user_id: str, contract_type: str, 
                            processing_time: float, success: bool):
        """Log contract analysis event"""
        if success:
            self.metrics['contracts_analyzed'] += 1
            self.metrics['response_times'].append(processing_time)
            
            self.logger.info(
                f"Contract analyzed - User: {user_id}, Type: {contract_type}, "
                f"Time: {processing_time:.2f}s"
            )
        else:
            self.metrics['errors'] += 1
            self.logger.error(
                f"Contract analysis failed - User: {user_id}, Type: {contract_type}"
            )
    
    def log_api_request(self, endpoint: str, method: str, status_code: int, 
                       response_time: float, user_id: Optional[str] = None):
        """Log API request"""
        self.metrics['api_requests'] += 1
        self.metrics['response_times'].append(response_time)
        
        log_level = logging.INFO if status_code < 400 else logging.ERROR
        
        api_logger = logging.getLogger('api')
        api_logger.log(
            log_level,
            f"{method} {endpoint} - Status: {status_code}, "
            f"Time: {response_time:.3f}s, User: {user_id or 'Anonymous'}"
        )
    
    def log_user_session(self, user_id: str, action: str):
        """Log user session events"""
        if action == 'login':
            self.metrics['active_sessions'].add(user_id)
        elif action == 'logout':
            self.metrics['active_sessions'].discard(user_id)
        
        self.logger.info(f"User session - {user_id}: {action}")
    
    def get_application_metrics(self) -> ApplicationMetrics:
        """Get current application metrics"""
        avg_response_time = (
            sum(self.metrics['response_times']) / len(self.metrics['response_times'])
            if self.metrics['response_times'] else 0
        )
        
        return ApplicationMetrics(
            timestamp=datetime.now().isoformat(),
            active_users=len(self.metrics['active_sessions']),
            contracts_analyzed=self.metrics['contracts_analyzed'],
            api_requests=self.metrics['api_requests'],
            errors_count=self.metrics['errors'],
            response_time_avg=avg_response_time,
            database_connections=0  # Would be implemented with actual DB monitoring
        )

class SecurityMonitor:
    """Security event monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
        self.failed_attempts: Dict[str, List[datetime]] = {}
        self.blocked_ips: Dict[str, datetime] = {}
    
    def log_security_event(self, event_type: str, user_id: Optional[str] = None,
                          ip_address: str = "unknown", user_agent: str = "unknown",
                          details: Dict[str, Any] = None):
        """Log security event"""
        event = SecurityEvent(
            timestamp=datetime.now().isoformat(),
            event_type=event_type,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {}
        )
        
        self.logger.info(json.dumps(asdict(event)))
        
        # Track failed login attempts
        if event_type == 'login_failure':
            self._track_failed_attempt(ip_address)
    
    def _track_failed_attempt(self, ip_address: str):
        """Track failed login attempts and block if necessary"""
        now = datetime.now()
        
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []
        
        # Remove attempts older than 1 hour
        self.failed_attempts[ip_address] = [
            attempt for attempt in self.failed_attempts[ip_address]
            if now - attempt < timedelta(hours=1)
        ]
        
        # Add current attempt
        self.failed_attempts[ip_address].append(now)
        
        # Block if too many attempts
        config = get_config()
        if len(self.failed_attempts[ip_address]) >= config.security.max_login_attempts:
            self.blocked_ips[ip_address] = now + timedelta(hours=1)
            self.logger.warning(f"IP blocked due to failed attempts: {ip_address}")
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP is currently blocked"""
        if ip_address in self.blocked_ips:
            if datetime.now() < self.blocked_ips[ip_address]:
                return True
            else:
                # Unblock expired IPs
                del self.blocked_ips[ip_address]
        return False

class MonitoringDashboard:
    """Streamlit dashboard for monitoring"""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.app_monitor = ApplicationMonitor()
        self.security_monitor = SecurityMonitor()
    
    def display_monitoring_dashboard(self):
        """Display comprehensive monitoring dashboard"""
        st.markdown("### 📊 مراقبة النظام والأداء")
        
        # Real-time metrics
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("#### 🖥️ مؤشرات النظام")
            system_metrics = self.system_monitor.collect_system_metrics()
            
            if system_metrics:
                st.metric("استخدام المعالج", f"{system_metrics.cpu_percent:.1f}%")
                st.metric("استخدام الذاكرة", f"{system_metrics.memory_percent:.1f}%")
                st.metric("استخدام القرص", f"{system_metrics.disk_percent:.1f}%")
                st.metric("وقت التشغيل", f"{system_metrics.uptime_seconds/3600:.1f} ساعة")
        
        with col2:
            st.markdown("#### 📱 مؤشرات التطبيق")
            app_metrics = self.app_monitor.get_application_metrics()
            
            st.metric("المستخدمون النشطون", app_metrics.active_users)
            st.metric("العقود المحللة", app_metrics.contracts_analyzed)
            st.metric("طلبات API", app_metrics.api_requests)
            st.metric("متوسط وقت الاستجابة", f"{app_metrics.response_time_avg:.3f}s")
        
        # Performance charts
        st.markdown("#### 📈 مخططات الأداء")
        
        if len(self.system_monitor.metrics_history) > 1:
            import pandas as pd
            import plotly.express as px
            
            # Prepare data
            metrics_data = []
            for metric in self.system_monitor.metrics_history[-50:]:  # Last 50 points
                metrics_data.append({
                    'timestamp': metric.timestamp,
                    'CPU': metric.cpu_percent,
                    'Memory': metric.memory_percent,
                    'Disk': metric.disk_percent
                })
            
            df = pd.DataFrame(metrics_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # CPU and Memory chart
            fig = px.line(df, x='timestamp', y=['CPU', 'Memory'], 
                         title='استخدام المعالج والذاكرة')
            st.plotly_chart(fig, use_container_width=True)
        
        # Security events
        st.markdown("#### 🔒 الأحداث الأمنية")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("عناوين IP المحجوبة", len(self.security_monitor.blocked_ips))
        
        with col2:
            total_failed_attempts = sum(
                len(attempts) for attempts in self.security_monitor.failed_attempts.values()
            )
            st.metric("محاولات تسجيل دخول فاشلة", total_failed_attempts)
        
        # Log viewer
        st.markdown("#### 📋 عارض السجلات")
        
        log_type = st.selectbox(
            "نوع السجل",
            options=["app", "security", "performance", "api"],
            format_func=lambda x: {
                "app": "سجل التطبيق",
                "security": "سجل الأمان",
                "performance": "سجل الأداء",
                "api": "سجل API"
            }[x]
        )
        
        log_file = f"logs/{log_type}.log"
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # Show last 20 lines
                recent_logs = ''.join(lines[-20:])
                st.text_area("آخر السجلات", recent_logs, height=300)
        else:
            st.info(f"ملف السجل غير موجود: {log_file}")

# Global monitoring instances
logger_setup = LoggerSetup()
system_monitor = SystemMonitor()
app_monitor = ApplicationMonitor()
security_monitor = SecurityMonitor()

def get_system_monitor() -> SystemMonitor:
    return system_monitor

def get_app_monitor() -> ApplicationMonitor:
    return app_monitor

def get_security_monitor() -> SecurityMonitor:
    return security_monitor
