#!/bin/bash

# Kuwaiti Legal Contract Analysis Platform
# Production Deployment Script
# Developed by MAXBIT LLC © 2025

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="kuwaiti-legal-platform"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="backups"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available disk space (minimum 5GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 5242880 ]; then  # 5GB in KB
        log_warning "Low disk space. At least 5GB recommended."
    fi
    
    log_success "System requirements check passed"
}

create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p data logs uploads ssl backups
    mkdir -p data/postgres data/redis
    
    # Set permissions
    chmod 755 data logs uploads
    chmod 700 ssl backups
    
    log_success "Directories created"
}

setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_info "Creating environment file..."
        cat > "$ENV_FILE" << EOF
# Kuwaiti Legal Platform Environment Configuration
# Generated on $(date)

# Application
ENVIRONMENT=production
DEBUG=false
APP_HOST=0.0.0.0
APP_PORT=8501
API_PORT=8000

# Database
DB_HOST=postgres
DB_PORT=5432
DB_NAME=legal_contracts
DB_USER=postgres
DB_PASSWORD=$(openssl rand -base64 32)

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=$(openssl rand -base64 32)

# Security
SECRET_KEY=$(openssl rand -base64 64)
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Configuration
AI_BACKEND=lmstudio
AI_BASE_URL=http://localhost:1234
AI_MODEL_NAME=law
AI_TIMEOUT=300

# File Upload
MAX_FILE_SIZE_MB=50

# Logging
LOG_LEVEL=INFO
LOG_MAX_FILE_SIZE_MB=10
LOG_BACKUP_COUNT=5

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
EOF
        log_success "Environment file created: $ENV_FILE"
    else
        log_info "Environment file already exists: $ENV_FILE"
    fi
}

setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
        log_info "Generating self-signed SSL certificate..."
        
        openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem \
            -days 365 -nodes -subj "/C=KW/ST=Kuwait/L=Kuwait City/O=MAXBIT LLC/CN=localhost"
        
        chmod 600 ssl/key.pem ssl/cert.pem
        
        log_success "SSL certificate generated"
        log_warning "Using self-signed certificate. Replace with proper SSL certificate for production."
    else
        log_info "SSL certificates already exist"
    fi
}

create_nginx_config() {
    log_info "Creating Nginx configuration..."
    
    cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream streamlit {
        server streamlit-app:8501;
    }
    
    upstream api {
        server api-server:8000;
    }
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=app:10m rate=5r/s;
    
    server {
        listen 80;
        server_name _;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name _;
        
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        
        # Main application
        location / {
            limit_req zone=app burst=10 nodelay;
            proxy_pass http://streamlit;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket support for Streamlit
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF
    
    log_success "Nginx configuration created"
}

create_monitoring_config() {
    log_info "Creating monitoring configuration..."
    
    # Prometheus configuration
    cat > prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'streamlit-app'
    static_configs:
      - targets: ['streamlit-app:8501']
    metrics_path: '/metrics'
    scrape_interval: 30s
  
  - job_name: 'api-server'
    static_configs:
      - targets: ['api-server:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF
    
    log_success "Monitoring configuration created"
}

backup_data() {
    if [ -d "data" ]; then
        log_info "Creating backup of existing data..."
        
        backup_name="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        tar -czf "$BACKUP_DIR/$backup_name.tar.gz" data/ logs/ 2>/dev/null || true
        
        log_success "Backup created: $BACKUP_DIR/$backup_name.tar.gz"
    fi
}

deploy_application() {
    log_info "Deploying application..."
    
    # Pull latest images
    docker-compose pull
    
    # Build custom images
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    log_success "Application deployed"
}

wait_for_services() {
    log_info "Waiting for services to start..."
    
    # Wait for database
    log_info "Waiting for database..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T postgres pg_isready -U postgres >/dev/null 2>&1; then
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Database failed to start"
        exit 1
    fi
    
    # Wait for application
    log_info "Waiting for application..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8501/_stcore/health >/dev/null 2>&1; then
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Application failed to start"
        exit 1
    fi
    
    log_success "All services are running"
}

show_status() {
    log_info "Deployment Status:"
    echo
    docker-compose ps
    echo
    log_success "Application URLs:"
    echo "  🌐 Main Application: https://localhost"
    echo "  🔌 API Documentation: https://localhost/api/v1/docs"
    echo "  📊 Monitoring (Grafana): http://localhost:3000"
    echo "  📈 Metrics (Prometheus): http://localhost:9090"
    echo
    log_info "Default credentials:"
    echo "  📱 Application: admin / admin123"
    echo "  📊 Grafana: admin / admin123"
    echo
    log_warning "Please change default passwords in production!"
}

# Main deployment process
main() {
    log_info "Starting deployment of $APP_NAME..."
    echo
    
    check_requirements
    create_directories
    setup_environment
    setup_ssl
    create_nginx_config
    create_monitoring_config
    backup_data
    deploy_application
    wait_for_services
    show_status
    
    echo
    log_success "🎉 Deployment completed successfully!"
    log_info "Check logs with: docker-compose logs -f"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping services..."
        docker-compose down
        log_success "Services stopped"
        ;;
    "restart")
        log_info "Restarting services..."
        docker-compose restart
        log_success "Services restarted"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        docker-compose ps
        ;;
    "backup")
        backup_data
        ;;
    "update")
        log_info "Updating application..."
        docker-compose pull
        docker-compose up -d --build
        log_success "Application updated"
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status|backup|update}"
        exit 1
        ;;
esac
