#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation Test Script for Kuwaiti Legal Contract Analysis Application
Tests all components and dependencies
"""

import sys
import importlib
import requests
import json
from pathlib import Path

def print_header():
    """Print test header"""
    print("=" * 60)
    print("🧪 اختبار تثبيت محلل العقود القانونية الكويتية")
    print("   Installation Test for Legal Contract Analyzer")
    print("=" * 60)
    print()

def test_python_version():
    """Test Python version compatibility"""
    print("🐍 اختبار إصدار Python...")
    
    version = sys.version_info
    if version >= (3, 8):
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - متوافق")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - غير متوافق (يتطلب 3.8+)")
        return False

def test_required_packages():
    """Test if required packages are installed"""
    print("\n📦 اختبار المكتبات المطلوبة...")
    
    required_packages = [
        "streamlit",
        "requests",
        "pandas",
        "python_docx",
        "PyPDF2",
        "pdfplumber",
        "reportlab",
        "Pillow"
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            # Handle package name differences
            import_name = package
            if package == "python_docx":
                import_name = "docx"
            elif package == "Pillow":
                import_name = "PIL"
            
            importlib.import_module(import_name)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(failed_packages)}")
        print("💡 قم بتثبيتها باستخدام: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ جميع المكتبات المطلوبة مثبتة")
        return True

def test_ollama_connection():
    """Test Ollama connection and availability"""
    print("\n🤖 اختبار اتصال Ollama...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama متصل ويعمل")
            
            # Check available models
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            
            if models:
                print(f"📋 النماذج المتاحة: {', '.join(models)}")
                
                # Check for recommended model
                if 'llama3.1:8b' in models:
                    print("✅ النموذج الموصى به (llama3.1:8b) متاح")
                else:
                    print("⚠️  النموذج الموصى به (llama3.1:8b) غير متاح")
                    print("💡 قم بتحميله باستخدام: ollama pull llama3.1:8b")
            else:
                print("⚠️  لا توجد نماذج مثبتة")
                print("💡 قم بتحميل نموذج باستخدام: ollama pull llama3.1:8b")
            
            return True
        else:
            print(f"❌ Ollama يستجيب لكن بخطأ: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بـ Ollama")
        print("💡 تأكد من تشغيل Ollama باستخدام: ollama serve")
        return False
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال بـ Ollama")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار Ollama: {str(e)}")
        return False

def test_lmstudio_connection():
    """Test LM Studio connection (optional)"""
    print("\n🎛️  اختبار اتصال LM Studio (اختياري)...")
    
    try:
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        if response.status_code == 200:
            print("✅ LM Studio متصل ويعمل")
            
            # Check available models
            data = response.json()
            models = [model['id'] for model in data.get('data', [])]
            
            if models:
                print(f"📋 النماذج المتاحة: {', '.join(models)}")
            else:
                print("⚠️  لا توجد نماذج محملة")
            
            return True
        else:
            print(f"⚠️  LM Studio يستجيب لكن بخطأ: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️  LM Studio غير متصل (اختياري)")
        return False
    except Exception as e:
        print(f"⚠️  خطأ في اختبار LM Studio: {str(e)}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\n📁 اختبار هيكل الملفات...")
    
    required_files = [
        "app.py",
        "ai_backend.py",
        "export_utils.py",
        "ui_components.py",
        "utils.py",
        "config.py",
        "requirements.txt",
        "README.md"
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️  الملفات المفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات المطلوبة موجودة")
        return True

def test_import_modules():
    """Test importing application modules"""
    print("\n🔧 اختبار استيراد وحدات التطبيق...")
    
    modules = [
        "ai_backend",
        "export_utils",
        "ui_components",
        "utils",
        "config"
    ]
    
    failed_imports = []
    
    for module in modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module} - خطأ في الاستيراد: {str(e)}")
            failed_imports.append(module)
        except Exception as e:
            print(f"⚠️  {module} - خطأ: {str(e)}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n⚠️  فشل استيراد: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ تم استيراد جميع الوحدات بنجاح")
        return True

def test_ai_functionality():
    """Test basic AI functionality"""
    print("\n🧠 اختبار وظائف الذكاء الاصطناعي...")
    
    try:
        from ai_backend import ContractAnalyzer
        
        # Test Ollama backend
        analyzer = ContractAnalyzer(backend="ollama")
        if analyzer.test_connection():
            print("✅ اختبار اتصال Ollama نجح")
        else:
            print("❌ اختبار اتصال Ollama فشل")
            return False
        
        # Test getting available models
        models = analyzer.get_available_models()
        if models:
            print(f"✅ تم الحصول على قائمة النماذج: {len(models)} نموذج")
        else:
            print("⚠️  لم يتم العثور على نماذج")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الذكاء الاصطناعي: {str(e)}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print_header()
    
    tests = [
        ("Python Version", test_python_version),
        ("Required Packages", test_required_packages),
        ("File Structure", test_file_structure),
        ("Module Imports", test_import_modules),
        ("Ollama Connection", test_ollama_connection),
        ("LM Studio Connection", test_lmstudio_connection),
        ("AI Functionality", test_ai_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    print(f"\nالنتيجة الإجمالية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("🚀 لتشغيل التطبيق: streamlit run app.py")
    elif passed >= total - 2:
        print("\n⚠️  معظم الاختبارات نجحت. التطبيق قد يعمل مع بعض القيود")
        print("💡 راجع الاختبارات الفاشلة أعلاه")
    else:
        print("\n❌ عدة اختبارات فشلت. يرجى إصلاح المشاكل قبل تشغيل التطبيق")
        print("💡 راجع تعليمات التثبيت في README.md")
    
    print("=" * 60)

if __name__ == "__main__":
    run_all_tests()
