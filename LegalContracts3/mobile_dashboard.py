"""
Mobile-Responsive Dashboard for Contract Analysis
Developed by MAXBIT LLC © 2025
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Any
from datetime import datetime, timedelta
from database import ContractDatabase
from advanced_reporting import ReportGenerator
from advanced_ai import SpecializedAnalyzer, AIInsightsGenerator

class MobileDashboard:
    """Mobile-optimized dashboard for contract analysis"""
    
    def __init__(self):
        self.db = ContractDatabase()
        self.report_generator = ReportGenerator()
        self.ai_insights = AIInsightsGenerator()
        self.specialized_analyzer = SpecializedAnalyzer()
    
    def display_mobile_dashboard(self, current_user: Dict[str, Any]):
        """Display mobile-optimized dashboard"""
        st.markdown("""
        <style>
        /* Mobile-first responsive design */
        @media (max-width: 768px) {
            .main-content {
                padding: 0.5rem !important;
            }
            .metric-card {
                margin: 0.25rem 0 !important;
                padding: 0.75rem !important;
            }
            .chart-container {
                height: 250px !important;
            }
            .mobile-card {
                background: white;
                border-radius: 8px;
                padding: 1rem;
                margin: 0.5rem 0;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-left: 4px solid #3498db;
            }
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .quick-action-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem;
            width: 100%;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-high { background-color: #e74c3c; }
        .status-medium { background-color: #f39c12; }
        .status-low { background-color: #27ae60; }
        </style>
        """, unsafe_allow_html=True)
        
        # Dashboard header
        st.markdown(f"""
        <div class="dashboard-header">
            <h2>📱 لوحة التحكم المحمولة</h2>
            <p>مرحباً {current_user['full_name']} - {self._get_role_name(current_user['role'])}</p>
            <small>{datetime.now().strftime('%Y-%m-%d %H:%M')}</small>
        </div>
        """, unsafe_allow_html=True)
        
        # Quick stats
        self._display_quick_stats(current_user)
        
        # Quick actions
        self._display_quick_actions()
        
        # Recent activity
        self._display_recent_activity(current_user)
        
        # Risk alerts
        self._display_risk_alerts(current_user)
        
        # Performance metrics
        self._display_performance_metrics(current_user)
    
    def _display_quick_stats(self, current_user: Dict[str, Any]):
        """Display quick statistics cards"""
        st.markdown("### 📊 إحصائيات سريعة")
        
        user_id = None if current_user["role"] == "admin" else current_user["id"]
        summary = self.report_generator.generate_executive_summary(user_id, 30)
        
        # Mobile-optimized metrics layout
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            <div class="mobile-card">
                <h4>📄 إجمالي العقود</h4>
                <h2 style="color: #3498db; margin: 0;">{}</h2>
                <small>آخر 30 يوم</small>
            </div>
            """.format(summary["total_contracts"]), unsafe_allow_html=True)
            
            st.markdown("""
            <div class="mobile-card">
                <h4>⚠️ عقود عالية المخاطر</h4>
                <h2 style="color: #e74c3c; margin: 0;">{}</h2>
                <small>تحتاج مراجعة</small>
            </div>
            """.format(summary["high_risk_contracts"]), unsafe_allow_html=True)
        
        with col2:
            st.markdown("""
            <div class="mobile-card">
                <h4>📈 متوسط المخاطر</h4>
                <h2 style="color: #f39c12; margin: 0;">{}/10</h2>
                <small>درجة المخاطر</small>
            </div>
            """.format(summary["average_risk_score"]), unsafe_allow_html=True)
            
            st.markdown("""
            <div class="mobile-card">
                <h4>💬 التعليقات النشطة</h4>
                <h2 style="color: #9b59b6; margin: 0;">{}</h2>
                <small>تحتاج رد</small>
            </div>
            """.format(summary["collaboration_stats"]["open_comments"]), unsafe_allow_html=True)
    
    def _display_quick_actions(self):
        """Display quick action buttons"""
        st.markdown("### ⚡ إجراءات سريعة")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📄 تحليل عقد جديد", key="mobile_analyze", use_container_width=True):
                st.session_state.current_page = "analyze"
                st.rerun()
            
            if st.button("📚 استخدام قالب", key="mobile_template", use_container_width=True):
                st.session_state.current_page = "templates"
                st.rerun()
        
        with col2:
            if st.button("📊 عرض التقارير", key="mobile_reports", use_container_width=True):
                st.session_state.current_page = "advanced_reports"
                st.rerun()
            
            if st.button("💬 التعاون", key="mobile_collab", use_container_width=True):
                st.session_state.current_page = "collaboration"
                st.rerun()
    
    def _display_recent_activity(self, current_user: Dict[str, Any]):
        """Display recent activity"""
        st.markdown("### 🕒 النشاط الأخير")
        
        user_id = None if current_user["role"] == "admin" else current_user["id"]
        recent_contracts = self.db.list_contracts(user_id=user_id, limit=5)
        
        if recent_contracts:
            for contract in recent_contracts:
                risk_color = self._get_risk_color(contract.get('risk_score', 0))
                status_icon = self._get_status_icon(contract['status'])
                
                st.markdown(f"""
                <div class="mobile-card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>{contract['title'][:30]}...</strong><br>
                            <small style="color: #666;">
                                {status_icon} {self._get_status_name(contract['status'])} • 
                                {contract['created_at'][:10]}
                            </small>
                        </div>
                        <div style="text-align: right;">
                            <span class="status-indicator status-{risk_color}"></span>
                            <small>{contract.get('risk_score', 0):.1f}/10</small>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("لا توجد عقود حديثة")
    
    def _display_risk_alerts(self, current_user: Dict[str, Any]):
        """Display risk alerts"""
        st.markdown("### 🚨 تنبيهات المخاطر")
        
        user_id = None if current_user["role"] == "admin" else current_user["id"]
        contracts = self.db.list_contracts(user_id=user_id, limit=50)
        
        high_risk_contracts = [c for c in contracts if c.get('risk_score', 0) >= 7]
        
        if high_risk_contracts:
            for contract in high_risk_contracts[:3]:  # Show top 3 high-risk
                st.markdown(f"""
                <div class="mobile-card" style="border-left-color: #e74c3c;">
                    <div style="display: flex; align-items: center;">
                        <span class="status-indicator status-high"></span>
                        <div>
                            <strong>{contract['title'][:25]}...</strong><br>
                            <small>درجة المخاطر: {contract.get('risk_score', 0):.1f}/10</small>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.success("✅ لا توجد تنبيهات مخاطر حالياً")
    
    def _display_performance_metrics(self, current_user: Dict[str, Any]):
        """Display performance metrics with mobile-optimized charts"""
        st.markdown("### 📈 مؤشرات الأداء")
        
        user_id = None if current_user["role"] == "admin" else current_user["id"]
        
        # Risk distribution pie chart
        contracts = self.db.list_contracts(user_id=user_id, limit=100)
        
        if contracts:
            risk_distribution = {
                "منخفض (0-3)": len([c for c in contracts if c.get('risk_score', 0) < 3]),
                "متوسط (3-6)": len([c for c in contracts if 3 <= c.get('risk_score', 0) < 6]),
                "عالي (6-8)": len([c for c in contracts if 6 <= c.get('risk_score', 0) < 8]),
                "حرج (8-10)": len([c for c in contracts if c.get('risk_score', 0) >= 8])
            }
            
            # Mobile-optimized pie chart
            fig = px.pie(
                values=list(risk_distribution.values()),
                names=list(risk_distribution.keys()),
                title="توزيع المخاطر",
                color_discrete_sequence=['#27ae60', '#f39c12', '#e67e22', '#e74c3c']
            )
            
            fig.update_layout(
                height=300,
                showlegend=True,
                legend=dict(
                    orientation="h",
                    yanchor="bottom",
                    y=-0.2,
                    xanchor="center",
                    x=0.5
                ),
                margin=dict(t=50, b=50, l=20, r=20)
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Contract types bar chart
            type_distribution = {}
            for contract in contracts:
                contract_type = contract.get('contract_type', 'other')
                type_name = self._get_type_name(contract_type)
                type_distribution[type_name] = type_distribution.get(type_name, 0) + 1
            
            if type_distribution:
                fig_bar = px.bar(
                    x=list(type_distribution.keys()),
                    y=list(type_distribution.values()),
                    title="أنواع العقود",
                    color=list(type_distribution.values()),
                    color_continuous_scale="Blues"
                )
                
                fig_bar.update_layout(
                    height=300,
                    xaxis_title="نوع العقد",
                    yaxis_title="العدد",
                    margin=dict(t=50, b=50, l=20, r=20)
                )
                
                st.plotly_chart(fig_bar, use_container_width=True)
        else:
            st.info("لا توجد بيانات كافية لعرض المؤشرات")
    
    def _get_risk_color(self, risk_score: float) -> str:
        """Get risk color based on score"""
        if risk_score >= 7:
            return "high"
        elif risk_score >= 4:
            return "medium"
        else:
            return "low"
    
    def _get_status_icon(self, status: str) -> str:
        """Get status icon"""
        icons = {
            "pending": "⏳",
            "analyzed": "✅",
            "reviewed": "👁️",
            "approved": "✅",
            "rejected": "❌"
        }
        return icons.get(status, "📄")
    
    def _get_status_name(self, status: str) -> str:
        """Get Arabic status name"""
        names = {
            "pending": "قيد الانتظار",
            "analyzed": "محلل",
            "reviewed": "مراجع",
            "approved": "موافق عليه",
            "rejected": "مرفوض"
        }
        return names.get(status, status)
    
    def _get_type_name(self, contract_type: str) -> str:
        """Get Arabic contract type name"""
        names = {
            "employment": "عقد عمل",
            "commercial": "عقد تجاري",
            "real_estate": "عقد عقاري",
            "service": "عقد خدمة",
            "partnership": "عقد شراكة",
            "other": "أخرى"
        }
        return names.get(contract_type, contract_type)
    
    def _get_role_name(self, role: str) -> str:
        """Get Arabic role name"""
        names = {
            "admin": "مدير النظام",
            "lawyer": "محامي",
            "paralegal": "مساعد قانوني",
            "client": "عميل"
        }
        return names.get(role, role)

class AdvancedAnalysisUI:
    """UI for advanced AI analysis features"""
    
    def __init__(self):
        self.specialized_analyzer = SpecializedAnalyzer()
        self.ai_insights = AIInsightsGenerator()
    
    def display_advanced_analysis_page(self, current_user: Dict[str, Any]):
        """Display advanced analysis page"""
        st.markdown("### 🤖 التحليل المتقدم بالذكاء الاصطناعي")
        
        # Contract selector
        db = ContractDatabase()
        contracts = db.list_contracts(
            user_id=current_user["id"] if current_user["role"] != "admin" else None,
            limit=50
        )
        
        if contracts:
            contract_options = {f"{c['title']} ({c['created_at'][:10]})": c for c in contracts}
            selected_contract_name = st.selectbox("اختر العقد للتحليل المتقدم", list(contract_options.keys()))
            
            if selected_contract_name:
                contract = contract_options[selected_contract_name]
                
                # Perform advanced analysis
                with st.spinner("🔍 جارٍ التحليل المتقدم..."):
                    advanced_analysis = self.specialized_analyzer.analyze_specialized_contract(
                        contract['content'], 
                        contract['contract_type']
                    )
                
                if advanced_analysis:
                    # Display results in tabs
                    tab1, tab2, tab3, tab4 = st.tabs([
                        "📋 ملخص تنفيذي", "🎯 تحليل المخاطر", 
                        "⚖️ الامتثال القانوني", "💡 التوصيات"
                    ])
                    
                    with tab1:
                        self._display_executive_summary(advanced_analysis)
                    
                    with tab2:
                        self._display_risk_analysis(advanced_analysis)
                    
                    with tab3:
                        self._display_compliance_analysis(advanced_analysis)
                    
                    with tab4:
                        self._display_recommendations(advanced_analysis)
        else:
            st.info("لا توجد عقود متاحة للتحليل المتقدم")
    
    def _display_executive_summary(self, analysis: Dict[str, Any]):
        """Display executive summary"""
        summary = self.ai_insights.generate_executive_summary(analysis)
        st.markdown(summary)
        
        # Key metrics
        specialized = analysis.get("specialized_insights", {})
        compliance = analysis.get("compliance_check", {})
        risks = analysis.get("risk_assessment", {})
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            completeness = specialized.get("completeness_score", 0)
            st.metric("اكتمال العقد", f"{completeness:.1f}%")
        
        with col2:
            compliance_score = compliance.get("overall_score", 0)
            st.metric("الامتثال القانوني", f"{compliance_score:.1f}%")
        
        with col3:
            risk_level = risks.get("risk_level", "unknown")
            st.metric("مستوى المخاطر", risk_level.title())
    
    def _display_risk_analysis(self, analysis: Dict[str, Any]):
        """Display detailed risk analysis"""
        risk_insights = self.ai_insights.generate_risk_insights(analysis)
        
        st.markdown("#### 🎯 تحليل المخاطر التفصيلي")
        
        # Risk level indicator
        risk_level = risk_insights["overall_risk_level"]
        risk_score = risk_insights["risk_score"]
        
        if risk_level == "high":
            st.error(f"⚠️ مستوى مخاطر عالي: {risk_score}/100")
        elif risk_level == "medium":
            st.warning(f"⚠️ مستوى مخاطر متوسط: {risk_score}/100")
        else:
            st.success(f"✅ مستوى مخاطر منخفض: {risk_score}/100")
        
        # Critical risks
        critical_risks = risk_insights["critical_risks"]
        if critical_risks:
            st.markdown("##### 🚨 المخاطر الحرجة")
            for risk in critical_risks:
                st.markdown(f"• **{risk['risk_name']}**: {risk['description']}")
        
        # Mitigation strategies
        strategies = risk_insights["mitigation_strategies"]
        if strategies:
            st.markdown("##### 🛡️ استراتيجيات التخفيف")
            for strategy in strategies:
                with st.expander(f"📋 {strategy['strategy']}"):
                    st.markdown(f"**الإجراء:** {strategy['action']}")
                    st.markdown(f"**الجدول الزمني:** {strategy['timeline']}")
    
    def _display_compliance_analysis(self, analysis: Dict[str, Any]):
        """Display compliance analysis"""
        compliance = analysis.get("compliance_check", {})
        
        st.markdown("#### ⚖️ تحليل الامتثال للقانون الكويتي")
        
        # Overall compliance score
        overall_score = compliance.get("overall_score", 0)
        compliance_level = compliance.get("compliance_level", "unknown")
        
        if compliance_level == "high":
            st.success(f"✅ امتثال عالي: {overall_score}%")
        elif compliance_level == "medium":
            st.warning(f"⚠️ امتثال متوسط: {overall_score}%")
        else:
            st.error(f"❌ امتثال منخفض: {overall_score}%")
        
        # Compliance issues
        issues = compliance.get("issues", [])
        if issues:
            st.markdown("##### 📋 مشاكل الامتثال")
            for issue in issues:
                severity_icon = "🚨" if issue["severity"] == "high" else "⚠️"
                st.markdown(f"{severity_icon} **{issue['issue']}**")
                st.markdown(f"   - {issue['description']}")
                st.markdown(f"   - المرجع القانوني: {issue['law_reference']}")
        else:
            st.success("✅ لا توجد مشاكل امتثال")
    
    def _display_recommendations(self, analysis: Dict[str, Any]):
        """Display improvement recommendations"""
        improvements = analysis.get("improvement_suggestions", [])
        precedents = analysis.get("legal_precedents", [])
        
        st.markdown("#### 💡 التوصيات والتحسينات")
        
        if improvements:
            # Group by priority
            high_priority = [imp for imp in improvements if imp.get("priority") == "high"]
            medium_priority = [imp for imp in improvements if imp.get("priority") == "medium"]
            
            if high_priority:
                st.markdown("##### 🔴 أولوية عالية")
                for imp in high_priority:
                    with st.expander(f"📌 {imp['suggestion']}"):
                        st.markdown(f"**الوصف:** {imp['description']}")
                        st.markdown(f"**الفئة:** {imp['category']}")
                        if imp.get('kuwaiti_law_reference'):
                            st.markdown(f"**المرجع القانوني:** {imp['kuwaiti_law_reference']}")
            
            if medium_priority:
                st.markdown("##### 🟡 أولوية متوسطة")
                for imp in medium_priority:
                    with st.expander(f"📝 {imp['suggestion']}"):
                        st.markdown(f"**الوصف:** {imp['description']}")
                        st.markdown(f"**الفئة:** {imp['category']}")
                        if imp.get('kuwaiti_law_reference'):
                            st.markdown(f"**المرجع القانوني:** {imp['kuwaiti_law_reference']}")
        
        # Legal precedents
        if precedents:
            st.markdown("##### 📚 السوابق القانونية ذات الصلة")
            for precedent in precedents:
                with st.expander(f"⚖️ {precedent['case_id']}"):
                    st.markdown(f"**الملخص:** {precedent['summary']}")
                    st.markdown(f"**المرجع القانوني:** {precedent['law_reference']}")
                    st.markdown(f"**التأثير:** {precedent['implication']}")
        
        if not improvements and not precedents:
            st.success("✅ العقد يبدو جيداً ولا يحتاج تحسينات كبيرة")
