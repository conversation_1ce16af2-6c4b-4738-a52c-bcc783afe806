#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Confidence Scoring System
Reliability indicators and confidence scoring for AI-generated recommendations
"""

import re
import json
import logging
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class ConfidenceScorer:
    """AI confidence scoring and reliability assessment system"""
    
    def __init__(self):
        self.confidence_thresholds = {
            'very_high': 90,
            'high': 75,
            'medium': 60,
            'low': 40,
            'very_low': 0
        }
        
        self.uncertainty_indicators = [
            'قد يكون', 'ربما', 'من المحتمل', 'يمكن أن', 'قد يتطلب',
            'ينبغي مراجعة', 'يُنصح بالتحقق', 'قد يحتاج', 'من الممكن',
            'يجب التأكد', 'قد يختلف', 'حسب الظروف', 'قد يعتمد على'
        ]
        
        self.confidence_indicators = [
            'بوضوح', 'بالتأكيد', 'لا شك', 'من المؤكد', 'بشكل قاطع',
            'يجب', 'ضروري', 'إلزامي', 'مطلوب', 'واضح', 'محدد'
        ]
    
    def calculate_confidence_score(self, ai_response: str, analysis_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Calculate comprehensive confidence score for AI response"""
        try:
            # Initialize scoring components
            scores = {
                'language_confidence': self._assess_language_confidence(ai_response),
                'content_specificity': self._assess_content_specificity(ai_response),
                'legal_terminology': self._assess_legal_terminology(ai_response),
                'uncertainty_level': self._assess_uncertainty_level(ai_response),
                'completeness_score': self._assess_completeness(ai_response, analysis_context),
                'consistency_score': self._assess_internal_consistency(ai_response)
            }
            
            # Calculate weighted overall score
            weights = {
                'language_confidence': 0.15,
                'content_specificity': 0.20,
                'legal_terminology': 0.25,
                'uncertainty_level': 0.20,
                'completeness_score': 0.15,
                'consistency_score': 0.05
            }
            
            overall_score = sum(scores[key] * weights[key] for key in scores.keys())
            
            # Determine confidence level
            confidence_level = self._determine_confidence_level(overall_score)
            
            # Generate reliability indicators
            reliability_indicators = self._generate_reliability_indicators(scores, ai_response)
            
            # Calculate uncertainty quantification
            uncertainty_metrics = self._quantify_uncertainty(ai_response, scores)
            
            return {
                'overall_confidence_score': round(overall_score, 2),
                'confidence_level': confidence_level,
                'component_scores': scores,
                'reliability_indicators': reliability_indicators,
                'uncertainty_metrics': uncertainty_metrics,
                'recommendations': self._generate_confidence_recommendations(scores, overall_score),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return {
                'overall_confidence_score': 0,
                'confidence_level': 'unknown',
                'error': str(e)
            }
    
    def _assess_language_confidence(self, text: str) -> float:
        """Assess confidence based on language patterns"""
        try:
            # Count uncertainty vs confidence indicators
            uncertainty_count = sum(1 for indicator in self.uncertainty_indicators 
                                  if indicator in text.lower())
            confidence_count = sum(1 for indicator in self.confidence_indicators 
                                 if indicator in text.lower())
            
            total_indicators = uncertainty_count + confidence_count
            
            if total_indicators == 0:
                return 70  # Neutral score
            
            confidence_ratio = confidence_count / total_indicators
            return min(100, confidence_ratio * 100 + 30)  # Base score of 30
            
        except Exception as e:
            logger.error(f"Error assessing language confidence: {e}")
            return 50
    
    def _assess_content_specificity(self, text: str) -> float:
        """Assess how specific and detailed the content is"""
        try:
            # Count specific legal references
            specific_patterns = [
                r'\d+\s*(مادة|فقرة|بند|شرط)',  # Article/clause numbers
                r'(القانون|النظام|اللائحة)\s+رقم\s+\d+',  # Law numbers
                r'\d+\s*(يوم|شهر|سنة)',  # Time periods
                r'\d+\s*(ريال|دينار|درهم|%)',  # Amounts and percentages
                r'(المحكمة|التحكيم|الهيئة)\s+[أ-ي\s]+',  # Specific institutions
            ]
            
            specificity_count = sum(len(re.findall(pattern, text)) for pattern in specific_patterns)
            text_length = len(text.split())
            
            if text_length == 0:
                return 0
            
            specificity_ratio = specificity_count / (text_length / 100)  # Per 100 words
            return min(100, specificity_ratio * 25 + 40)  # Base score of 40
            
        except Exception as e:
            logger.error(f"Error assessing content specificity: {e}")
            return 50
    
    def _assess_legal_terminology(self, text: str) -> float:
        """Assess proper use of legal terminology"""
        try:
            legal_terms = [
                'التزام', 'مسؤولية', 'ضمان', 'كفالة', 'تعويض', 'عقد', 'اتفاقية',
                'شرط', 'بند', 'مادة', 'فقرة', 'نص', 'حكم', 'قرار', 'قانون',
                'نظام', 'لائحة', 'تنظيم', 'إجراء', 'دعوى', 'نزاع', 'تحكيم',
                'قضاء', 'محكمة', 'حكم', 'قرار', 'أمر', 'تنفيذ', 'إنفاذ'
            ]
            
            term_count = sum(1 for term in legal_terms if term in text)
            text_length = len(text.split())
            
            if text_length == 0:
                return 0
            
            terminology_density = term_count / (text_length / 100)  # Per 100 words
            return min(100, terminology_density * 10 + 30)  # Base score of 30
            
        except Exception as e:
            logger.error(f"Error assessing legal terminology: {e}")
            return 50
    
    def _assess_uncertainty_level(self, text: str) -> float:
        """Assess level of uncertainty in the response"""
        try:
            uncertainty_patterns = [
                r'(قد|ربما|من المحتمل|يمكن أن|قد يكون)',
                r'(ينبغي مراجعة|يُنصح بالتحقق|قد يحتاج)',
                r'(حسب الظروف|قد يعتمد على|قد يختلف)',
                r'(غير واضح|غير محدد|يحتاج توضيح)'
            ]
            
            uncertainty_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                                  for pattern in uncertainty_patterns)
            
            sentences = len(re.split(r'[.!?]', text))
            
            if sentences == 0:
                return 50
            
            uncertainty_ratio = uncertainty_count / sentences
            return max(0, 100 - (uncertainty_ratio * 100))  # Inverse scoring
            
        except Exception as e:
            logger.error(f"Error assessing uncertainty level: {e}")
            return 50
    
    def _assess_completeness(self, text: str, context: Dict[str, Any] = None) -> float:
        """Assess completeness of the analysis"""
        try:
            expected_sections = [
                'مخاطر', 'توصيات', 'نقاط قانونية', 'التزامات',
                'شروط', 'بنود', 'أطراف', 'مسؤوليات'
            ]
            
            found_sections = sum(1 for section in expected_sections 
                               if section in text.lower())
            
            completeness_ratio = found_sections / len(expected_sections)
            
            # Adjust based on text length
            word_count = len(text.split())
            length_factor = min(1.0, word_count / 500)  # Optimal around 500 words
            
            return min(100, (completeness_ratio * 80 + length_factor * 20))
            
        except Exception as e:
            logger.error(f"Error assessing completeness: {e}")
            return 50
    
    def _assess_internal_consistency(self, text: str) -> float:
        """Assess internal consistency of the response"""
        try:
            # Look for contradictory statements
            contradictory_patterns = [
                (r'منخفض المخاطر', r'عالي المخاطر'),
                (r'يُنصح', r'لا يُنصح'),
                (r'ضروري', r'غير ضروري'),
                (r'مطلوب', r'غير مطلوب')
            ]
            
            contradictions = 0
            for positive, negative in contradictory_patterns:
                if re.search(positive, text, re.IGNORECASE) and re.search(negative, text, re.IGNORECASE):
                    contradictions += 1
            
            # Penalize contradictions
            consistency_score = max(0, 100 - (contradictions * 20))
            
            return consistency_score
            
        except Exception as e:
            logger.error(f"Error assessing internal consistency: {e}")
            return 80
    
    def _determine_confidence_level(self, score: float) -> str:
        """Determine confidence level based on score"""
        for level, threshold in self.confidence_thresholds.items():
            if score >= threshold:
                return level
        return 'very_low'
    
    def _generate_reliability_indicators(self, scores: Dict[str, float], text: str) -> List[Dict[str, Any]]:
        """Generate specific reliability indicators"""
        indicators = []
        
        # Language confidence indicator
        if scores['language_confidence'] < 60:
            indicators.append({
                'type': 'warning',
                'category': 'language',
                'message': 'يحتوي النص على مؤشرات عدم يقين عالية',
                'severity': 'medium'
            })
        
        # Legal terminology indicator
        if scores['legal_terminology'] < 50:
            indicators.append({
                'type': 'warning',
                'category': 'terminology',
                'message': 'استخدام محدود للمصطلحات القانونية المتخصصة',
                'severity': 'low'
            })
        
        # Completeness indicator
        if scores['completeness_score'] < 70:
            indicators.append({
                'type': 'warning',
                'category': 'completeness',
                'message': 'التحليل قد يحتاج إلى مزيد من التفصيل',
                'severity': 'medium'
            })
        
        # Uncertainty indicator
        if scores['uncertainty_level'] < 50:
            indicators.append({
                'type': 'caution',
                'category': 'uncertainty',
                'message': 'مستوى عالي من عدم اليقين في التحليل',
                'severity': 'high'
            })
        
        return indicators
    
    def _quantify_uncertainty(self, text: str, scores: Dict[str, float]) -> Dict[str, Any]:
        """Quantify uncertainty metrics"""
        try:
            # Count uncertainty phrases
            uncertainty_phrases = sum(1 for indicator in self.uncertainty_indicators 
                                    if indicator in text.lower())
            
            # Calculate uncertainty distribution
            total_sentences = len(re.split(r'[.!?]', text))
            uncertainty_density = uncertainty_phrases / max(1, total_sentences)
            
            # Categorize uncertainty sources
            uncertainty_sources = {
                'legal_interpretation': len(re.findall(r'(قد يفسر|يمكن تفسير|حسب التفسير)', text, re.IGNORECASE)),
                'factual_verification': len(re.findall(r'(يجب التحقق|ينبغي مراجعة|قد يحتاج تأكيد)', text, re.IGNORECASE)),
                'contextual_dependency': len(re.findall(r'(حسب الظروف|قد يعتمد|يختلف حسب)', text, re.IGNORECASE))
            }
            
            return {
                'uncertainty_density': round(uncertainty_density, 3),
                'uncertainty_phrases_count': uncertainty_phrases,
                'uncertainty_sources': uncertainty_sources,
                'confidence_variance': round(statistics.stdev(scores.values()), 2) if len(scores) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"Error quantifying uncertainty: {e}")
            return {'error': str(e)}
    
    def _generate_confidence_recommendations(self, scores: Dict[str, float], overall_score: float) -> List[str]:
        """Generate recommendations based on confidence analysis"""
        recommendations = []
        
        if overall_score < 60:
            recommendations.append("يُنصح بمراجعة التحليل مع خبير قانوني متخصص")
        
        if scores['legal_terminology'] < 50:
            recommendations.append("قد يحتاج التحليل إلى مزيد من المراجع القانونية المتخصصة")
        
        if scores['uncertainty_level'] < 50:
            recommendations.append("يُنصح بالحصول على رأي قانوني إضافي للنقاط غير المؤكدة")
        
        if scores['completeness_score'] < 70:
            recommendations.append("يمكن تحسين التحليل بإضافة مزيد من التفاصيل والأمثلة")
        
        if scores['content_specificity'] < 60:
            recommendations.append("يُنصح بتضمين مراجع قانونية محددة ونصوص نظامية")
        
        if not recommendations:
            recommendations.append("التحليل يظهر مستوى ثقة جيد ويمكن الاعتماد عليه")
        
        return recommendations
    
    def compare_confidence_scores(self, scores_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compare multiple confidence scores for consistency analysis"""
        try:
            if not scores_list:
                return {'error': 'No scores provided'}
            
            overall_scores = [score['overall_confidence_score'] for score in scores_list]
            
            comparison = {
                'average_confidence': round(statistics.mean(overall_scores), 2),
                'confidence_variance': round(statistics.stdev(overall_scores), 2) if len(overall_scores) > 1 else 0,
                'min_confidence': min(overall_scores),
                'max_confidence': max(overall_scores),
                'consistency_rating': self._rate_consistency(overall_scores),
                'recommendation': self._get_consistency_recommendation(overall_scores)
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing confidence scores: {e}")
            return {'error': str(e)}
    
    def _rate_consistency(self, scores: List[float]) -> str:
        """Rate consistency of confidence scores"""
        if len(scores) < 2:
            return 'insufficient_data'
        
        variance = statistics.stdev(scores)
        
        if variance < 5:
            return 'very_consistent'
        elif variance < 10:
            return 'consistent'
        elif variance < 20:
            return 'moderately_consistent'
        else:
            return 'inconsistent'
    
    def _get_consistency_recommendation(self, scores: List[float]) -> str:
        """Get recommendation based on score consistency"""
        if len(scores) < 2:
            return "يحتاج إلى مزيد من التحليلات للمقارنة"
        
        variance = statistics.stdev(scores)
        
        if variance < 5:
            return "النتائج متسقة ويمكن الاعتماد عليها"
        elif variance < 10:
            return "النتائج متسقة إلى حد كبير"
        elif variance < 20:
            return "توجد بعض التباينات في النتائج - يُنصح بالمراجعة"
        else:
            return "توجد تباينات كبيرة - يُنصح بإعادة التحليل أو الاستعانة بخبير"
