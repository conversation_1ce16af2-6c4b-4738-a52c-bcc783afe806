#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Theme Manager
Beautiful theme system with multiple modern themes
"""

import streamlit as st
from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum

class ThemeType(Enum):
    """Available theme types"""
    LIGHT = "light"
    DARK = "dark"
    BLUE_OCEAN = "blue_ocean"
    EMERALD_FOREST = "emerald_forest"
    SUNSET_ORANGE = "sunset_orange"
    ROYAL_PURPLE = "royal_purple"
    ROSE_GOLD = "rose_gold"
    MIDNIGHT_BLUE = "midnight_blue"
    ARCTIC_WHITE = "arctic_white"
    DESERT_SAND = "desert_sand"

@dataclass
class ThemeColors:
    """Theme color configuration"""
    primary: str
    secondary: str
    background: str
    surface: str
    text_primary: str
    text_secondary: str
    accent: str
    success: str
    warning: str
    error: str
    info: str
    border: str
    shadow: str
    gradient_start: str
    gradient_end: str

class ThemeManager:
    """Advanced theme management system"""
    
    def __init__(self):
        self.current_theme = ThemeType.LIGHT
        self.themes = self._initialize_themes()
    
    def _initialize_themes(self) -> Dict[ThemeType, ThemeColors]:
        """Initialize all available themes"""
        return {
            ThemeType.LIGHT: ThemeColors(
                primary="#3498db",
                secondary="#2ecc71",
                background="#ffffff",
                surface="#f8f9fa",
                text_primary="#2c3e50",
                text_secondary="#7f8c8d",
                accent="#e74c3c",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c",
                info="#3498db",
                border="#dee2e6",
                shadow="rgba(0,0,0,0.1)",
                gradient_start="#f4f7fa",
                gradient_end="#e8f4f8"
            ),
            
            ThemeType.DARK: ThemeColors(
                primary="#3498db",
                secondary="#2ecc71",
                background="#1a1a1a",
                surface="#2d2d2d",
                text_primary="#ffffff",
                text_secondary="#b0b0b0",
                accent="#e74c3c",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c",
                info="#3498db",
                border="#404040",
                shadow="rgba(0,0,0,0.3)",
                gradient_start="#2c3e50",
                gradient_end="#34495e"
            ),
            
            ThemeType.BLUE_OCEAN: ThemeColors(
                primary="#0077be",
                secondary="#00a8cc",
                background="#f0f8ff",
                surface="#e6f3ff",
                text_primary="#003d5c",
                text_secondary="#0066a3",
                accent="#ff6b35",
                success="#00cc88",
                warning="#ffaa00",
                error="#ff4757",
                info="#0077be",
                border="#b3d9ff",
                shadow="rgba(0,119,190,0.15)",
                gradient_start="#e6f3ff",
                gradient_end="#cce7ff"
            ),
            
            ThemeType.EMERALD_FOREST: ThemeColors(
                primary="#27ae60",
                secondary="#2ecc71",
                background="#f8fff8",
                surface="#e8f5e8",
                text_primary="#1e3a1e",
                text_secondary="#2d5a2d",
                accent="#e67e22",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c",
                info="#16a085",
                border="#a8d8a8",
                shadow="rgba(39,174,96,0.15)",
                gradient_start="#e8f5e8",
                gradient_end="#d4edda"
            ),
            
            ThemeType.SUNSET_ORANGE: ThemeColors(
                primary="#e67e22",
                secondary="#f39c12",
                background="#fff8f0",
                surface="#ffede0",
                text_primary="#8b4513",
                text_secondary="#cd853f",
                accent="#c0392b",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c",
                info="#3498db",
                border="#ffd4a3",
                shadow="rgba(230,126,34,0.15)",
                gradient_start="#fff8f0",
                gradient_end="#ffe4cc"
            ),
            
            ThemeType.ROYAL_PURPLE: ThemeColors(
                primary="#8e44ad",
                secondary="#9b59b6",
                background="#faf8ff",
                surface="#f0e6ff",
                text_primary="#4a235a",
                text_secondary="#6c3483",
                accent="#e74c3c",
                success="#27ae60",
                warning="#f39c12",
                error="#e74c3c",
                info="#8e44ad",
                border="#d7bfff",
                shadow="rgba(142,68,173,0.15)",
                gradient_start="#f0e6ff",
                gradient_end="#e6ccff"
            ),
            
            ThemeType.ROSE_GOLD: ThemeColors(
                primary="#e91e63",
                secondary="#f06292",
                background="#fff5f8",
                surface="#ffe0e6",
                text_primary="#880e4f",
                text_secondary="#ad1457",
                accent="#ff5722",
                success="#4caf50",
                warning="#ff9800",
                error="#f44336",
                info="#2196f3",
                border="#ffb3c1",
                shadow="rgba(233,30,99,0.15)",
                gradient_start="#fff5f8",
                gradient_end="#ffccd5"
            ),
            
            ThemeType.MIDNIGHT_BLUE: ThemeColors(
                primary="#1565c0",
                secondary="#1976d2",
                background="#0a0e1a",
                surface="#1a1f2e",
                text_primary="#e3f2fd",
                text_secondary="#90caf9",
                accent="#ff4081",
                success="#00e676",
                warning="#ffc107",
                error="#f44336",
                info="#2196f3",
                border="#2a3f5f",
                shadow="rgba(21,101,192,0.3)",
                gradient_start="#0a0e1a",
                gradient_end="#1a1f2e"
            ),
            
            ThemeType.ARCTIC_WHITE: ThemeColors(
                primary="#607d8b",
                secondary="#78909c",
                background="#fafafa",
                surface="#ffffff",
                text_primary="#263238",
                text_secondary="#546e7a",
                accent="#ff5722",
                success="#4caf50",
                warning="#ff9800",
                error="#f44336",
                info="#2196f3",
                border="#e0e0e0",
                shadow="rgba(96,125,139,0.1)",
                gradient_start="#fafafa",
                gradient_end="#f5f5f5"
            ),
            
            ThemeType.DESERT_SAND: ThemeColors(
                primary="#8d6e63",
                secondary="#a1887f",
                background="#fdf6f0",
                surface="#f5e6d3",
                text_primary="#3e2723",
                text_secondary="#5d4037",
                accent="#ff7043",
                success="#66bb6a",
                warning="#ffb74d",
                error="#ef5350",
                info="#42a5f5",
                border="#d7ccc8",
                shadow="rgba(141,110,99,0.15)",
                gradient_start="#fdf6f0",
                gradient_end="#f5e6d3"
            )
        }
    
    def set_theme(self, theme_type: ThemeType):
        """Set the current theme"""
        self.current_theme = theme_type
        if 'theme' not in st.session_state:
            st.session_state.theme = theme_type.value
        else:
            st.session_state.theme = theme_type.value
    
    def get_current_theme(self) -> ThemeColors:
        """Get current theme colors"""
        return self.themes[self.current_theme]
    
    def get_theme_names(self) -> Dict[str, str]:
        """Get available theme names"""
        return {
            ThemeType.LIGHT.value: "🌞 فاتح كلاسيكي",
            ThemeType.DARK.value: "🌙 داكن أنيق",
            ThemeType.BLUE_OCEAN.value: "🌊 محيط أزرق",
            ThemeType.EMERALD_FOREST.value: "🌲 غابة زمردية",
            ThemeType.SUNSET_ORANGE.value: "🌅 غروب برتقالي",
            ThemeType.ROYAL_PURPLE.value: "👑 بنفسجي ملكي",
            ThemeType.ROSE_GOLD.value: "🌹 ذهبي وردي",
            ThemeType.MIDNIGHT_BLUE.value: "🌌 أزرق منتصف الليل",
            ThemeType.ARCTIC_WHITE.value: "❄️ أبيض قطبي",
            ThemeType.DESERT_SAND.value: "🏜️ رمال الصحراء"
        }
    
    def generate_css(self) -> str:
        """Generate CSS for current theme"""
        colors = self.get_current_theme()
        
        return f"""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
        
        /* Root variables */
        :root {{
            --primary-color: {colors.primary};
            --secondary-color: {colors.secondary};
            --background-color: {colors.background};
            --surface-color: {colors.surface};
            --text-primary: {colors.text_primary};
            --text-secondary: {colors.text_secondary};
            --accent-color: {colors.accent};
            --success-color: {colors.success};
            --warning-color: {colors.warning};
            --error-color: {colors.error};
            --info-color: {colors.info};
            --border-color: {colors.border};
            --shadow-color: {colors.shadow};
            --gradient-start: {colors.gradient_start};
            --gradient-end: {colors.gradient_end};
        }}
        
        /* Global styles */
        .stApp {{
            background: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
            color: var(--text-primary);
            font-family: 'Noto Sans Arabic', 'Inter', Arial, sans-serif;
            direction: rtl;
        }}
        
        /* Main content container */
        .main-content {{
            background: var(--surface-color);
            border-radius: 20px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 8px 32px var(--shadow-color);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }}
        
        .main-content:hover {{
            transform: translateY(-2px);
            box-shadow: 0 12px 40px var(--shadow-color);
        }}
        
        /* Beautiful cards */
        .metric-card {{
            background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
            border-radius: 16px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 4px 20px var(--shadow-color);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        
        .metric-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }}
        
        .metric-card:hover {{
            transform: translateY(-4px);
            box-shadow: 0 8px 30px var(--shadow-color);
        }}
        
        /* Buttons */
        .stButton > button {{
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px var(--shadow-color);
        }}
        
        .stButton > button:hover {{
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-color);
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }}
        
        /* Sidebar */
        .css-1d391kg {{
            background: var(--surface-color);
            border-right: 1px solid var(--border-color);
        }}
        
        /* Tabs */
        .stTabs [data-baseweb="tab-list"] {{
            background: var(--surface-color);
            border-radius: 12px;
            padding: 0.5rem;
            margin-bottom: 1rem;
        }}
        
        .stTabs [data-baseweb="tab"] {{
            background: transparent;
            border-radius: 8px;
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }}
        
        .stTabs [aria-selected="true"] {{
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }}
        
        /* Input fields */
        .stTextInput > div > div > input,
        .stTextArea > div > div > textarea,
        .stSelectbox > div > div > select {{
            background: var(--background-color);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }}
        
        .stTextInput > div > div > input:focus,
        .stTextArea > div > div > textarea:focus,
        .stSelectbox > div > div > select:focus {{
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }}
        
        /* Progress bars */
        .stProgress > div > div > div {{
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 10px;
        }}
        
        /* Alerts */
        .stAlert {{
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 15px var(--shadow-color);
        }}
        
        /* Metrics */
        .metric-container {{
            background: var(--surface-color);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 20px var(--shadow-color);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }}
        
        .metric-container:hover {{
            transform: scale(1.02);
        }}
        
        /* Animation classes */
        .fade-in {{
            animation: fadeIn 0.6s ease-out;
        }}
        
        .slide-up {{
            animation: slideUp 0.5s ease-out;
        }}
        
        .bounce-in {{
            animation: bounceIn 0.8s ease-out;
        }}
        
        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(20px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        @keyframes slideUp {{
            from {{ opacity: 0; transform: translateY(30px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}
        
        @keyframes bounceIn {{
            0% {{ opacity: 0; transform: scale(0.3); }}
            50% {{ opacity: 1; transform: scale(1.05); }}
            70% {{ transform: scale(0.9); }}
            100% {{ opacity: 1; transform: scale(1); }}
        }}
        
        /* Responsive design */
        @media (max-width: 768px) {{
            .main-content {{
                padding: 1rem;
                margin: 0.5rem;
                border-radius: 16px;
            }}
            
            .metric-card {{
                padding: 1rem;
                margin: 0.5rem 0;
            }}
        }}
        </style>
        """

# Global theme manager instance
theme_manager = ThemeManager()

def get_theme_manager() -> ThemeManager:
    """Get global theme manager instance"""
    return theme_manager
