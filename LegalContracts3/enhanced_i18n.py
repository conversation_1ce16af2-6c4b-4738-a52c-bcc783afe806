#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Internationalization System
Complete Arabic/English interface switching with comprehensive translations
"""

import streamlit as st
import json
from pathlib import Path
from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum

class Language(Enum):
    """Supported languages"""
    ARABIC = "ar"
    ENGLISH = "en"

@dataclass
class LanguageConfig:
    """Language configuration"""
    code: str
    name: str
    native_name: str
    direction: str
    font_family: str

class EnhancedI18nManager:
    """Enhanced internationalization manager"""
    
    def __init__(self):
        self.current_language = Language.ARABIC
        self.translations = {}
        self.language_configs = self._initialize_language_configs()
        self._load_translations()
    
    def _initialize_language_configs(self) -> Dict[Language, LanguageConfig]:
        """Initialize language configurations"""
        return {
            Language.ARABIC: LanguageConfig(
                code="ar",
                name="Arabic",
                native_name="العربية",
                direction="rtl",
                font_family="'Noto Sans Arabic', Arial, sans-serif"
            ),
            Language.ENGLISH: LanguageConfig(
                code="en",
                name="English",
                native_name="English",
                direction="ltr",
                font_family="'Inter', 'Segoe UI', Arial, sans-serif"
            )
        }
    
    def _load_translations(self):
        """Load all translations"""
        self.translations = {
            Language.ARABIC: {
                # App Information
                "app": {
                    "title": "محلل العقود القانونية",
                    "subtitle": "تحليل ذكي للعقود باستخدام الذكاء الاصطناعي",
                    "description": "نظام متطور لتحليل العقود وفقاً للقوانين العربية",
                    "company": "MAXBIT LLC",
                    "version": "الإصدار 2.0",
                    "welcome": "مرحباً بك في محلل العقود القانونية"
                },
                
                # Navigation
                "nav": {
                    "home": "🏠 الرئيسية",
                    "analysis": "📄 تحليل عقد جديد",
                    "database": "📊 قاعدة البيانات",
                    "statistics": "📈 الإحصائيات",
                    "templates": "📚 مكتبة النماذج",
                    "history": "📜 تاريخ التحليل",
                    "risk": "🎯 تحليل المخاطر",
                    "collaboration": "💬 التعاون والتعليقات",
                    "reports": "📊 التقارير المتقدمة",
                    "advanced": "🤖 التحليل المتقدم",
                    "insights": "🤖 رؤى الذكاء الاصطناعي",
                    "monitoring": "📊 مراقبة النظام",
                    "settings": "⚙️ الإعدادات"
                },
                
                # Buttons and Actions
                "buttons": {
                    "analyze": "🔍 تحليل العقد",
                    "upload": "📤 رفع ملف",
                    "download": "📥 تحميل",
                    "save": "💾 حفظ",
                    "cancel": "❌ إلغاء",
                    "edit": "✏️ تعديل",
                    "delete": "🗑️ حذف",
                    "export": "📤 تصدير",
                    "import": "📥 استيراد",
                    "search": "🔍 بحث",
                    "filter": "🔽 تصفية",
                    "refresh": "🔄 تحديث",
                    "apply": "✅ تطبيق",
                    "reset": "🔄 إعادة تعيين",
                    "submit": "📤 إرسال",
                    "close": "❌ إغلاق",
                    "next": "➡️ التالي",
                    "previous": "⬅️ السابق",
                    "finish": "✅ إنهاء",
                    "login": "🔐 تسجيل الدخول",
                    "logout": "🚪 تسجيل الخروج"
                },
                
                # Messages and Status
                "messages": {
                    "success": "✅ تم بنجاح",
                    "error": "❌ حدث خطأ",
                    "warning": "⚠️ تحذير",
                    "info": "ℹ️ معلومات",
                    "loading": "⏳ جارٍ التحميل...",
                    "processing": "⚙️ جارٍ المعالجة...",
                    "completed": "✅ مكتمل",
                    "failed": "❌ فشل",
                    "saved": "💾 تم الحفظ",
                    "deleted": "🗑️ تم الحذف",
                    "updated": "🔄 تم التحديث",
                    "uploaded": "📤 تم الرفع",
                    "downloaded": "📥 تم التحميل"
                },
                
                # Form Fields
                "forms": {
                    "contract_text": "نص العقد",
                    "contract_type": "نوع العقد",
                    "file_upload": "رفع ملف",
                    "analysis_type": "نوع التحليل",
                    "language": "اللغة",
                    "comments": "التعليقات",
                    "notes": "الملاحظات",
                    "title": "العنوان",
                    "description": "الوصف",
                    "category": "الفئة",
                    "priority": "الأولوية",
                    "status": "الحالة",
                    "date": "التاريخ",
                    "time": "الوقت",
                    "user": "المستخدم",
                    "email": "البريد الإلكتروني",
                    "password": "كلمة المرور",
                    "confirm_password": "تأكيد كلمة المرور",
                    "legal_system": "النظام القانوني"
                },
                
                # Legal Systems and Terms
                "legal": {
                    "kuwait_law": "🇰🇼 القانون الكويتي",
                    "saudi_law": "🇸🇦 النظام السعودي",
                    "civil_code": "القانون المدني",
                    "commercial_code": "القانون التجاري",
                    "labor_law": "قانون العمل",
                    "sharia_compliance": "الامتثال الشرعي",
                    "contract_formation": "تكوين العقد",
                    "obligations": "الالتزامات",
                    "liability": "المسؤولية",
                    "termination": "الإنهاء",
                    "risk_assessment": "تقييم المخاطر",
                    "compliance_check": "فحص الامتثال",
                    "legal_analysis": "التحليل القانوني",
                    "recommendations": "التوصيات القانونية"
                },
                
                # Themes
                "themes": {
                    "light": "🌞 فاتح كلاسيكي",
                    "dark": "🌙 داكن أنيق",
                    "blue_ocean": "🌊 محيط أزرق",
                    "emerald_forest": "🌲 غابة زمردية",
                    "sunset_orange": "🌅 غروب برتقالي",
                    "royal_purple": "👑 بنفسجي ملكي",
                    "rose_gold": "🌹 ذهبي وردي",
                    "midnight_blue": "🌌 أزرق منتصف الليل",
                    "arctic_white": "❄️ أبيض قطبي",
                    "desert_sand": "🏜️ رمال الصحراء"
                },
                
                # Settings
                "settings": {
                    "user_preferences": "👤 تفضيلات المستخدم",
                    "ai_settings": "🤖 إعدادات الذكاء الاصطناعي",
                    "security": "🔒 الأمان",
                    "system": "⚙️ النظام",
                    "theme": "🎨 المظهر",
                    "language_settings": "🌐 إعدادات اللغة",
                    "notifications": "🔔 التنبيهات",
                    "privacy": "🔐 الخصوصية",
                    "backup": "💾 النسخ الاحتياطي",
                    "updates": "🔄 التحديثات"
                }
            },
            
            Language.ENGLISH: {
                # App Information
                "app": {
                    "title": "Legal Contract Analyzer",
                    "subtitle": "Smart contract analysis using artificial intelligence",
                    "description": "Advanced system for analyzing contracts according to Arab laws",
                    "company": "MAXBIT LLC",
                    "version": "Version 2.0",
                    "welcome": "Welcome to Legal Contract Analyzer"
                },
                
                # Navigation
                "nav": {
                    "home": "🏠 Home",
                    "analysis": "📄 New Contract Analysis",
                    "database": "📊 Database",
                    "statistics": "📈 Statistics",
                    "templates": "📚 Template Library",
                    "history": "📜 Analysis History",
                    "risk": "🎯 Risk Analysis",
                    "collaboration": "💬 Collaboration & Comments",
                    "reports": "📊 Advanced Reports",
                    "advanced": "🤖 Advanced Analysis",
                    "insights": "🤖 AI Insights",
                    "monitoring": "📊 System Monitoring",
                    "settings": "⚙️ Settings"
                },
                
                # Buttons and Actions
                "buttons": {
                    "analyze": "🔍 Analyze Contract",
                    "upload": "📤 Upload File",
                    "download": "📥 Download",
                    "save": "💾 Save",
                    "cancel": "❌ Cancel",
                    "edit": "✏️ Edit",
                    "delete": "🗑️ Delete",
                    "export": "📤 Export",
                    "import": "📥 Import",
                    "search": "🔍 Search",
                    "filter": "🔽 Filter",
                    "refresh": "🔄 Refresh",
                    "apply": "✅ Apply",
                    "reset": "🔄 Reset",
                    "submit": "📤 Submit",
                    "close": "❌ Close",
                    "next": "➡️ Next",
                    "previous": "⬅️ Previous",
                    "finish": "✅ Finish",
                    "login": "🔐 Login",
                    "logout": "🚪 Logout"
                },
                
                # Messages and Status
                "messages": {
                    "success": "✅ Success",
                    "error": "❌ Error occurred",
                    "warning": "⚠️ Warning",
                    "info": "ℹ️ Information",
                    "loading": "⏳ Loading...",
                    "processing": "⚙️ Processing...",
                    "completed": "✅ Completed",
                    "failed": "❌ Failed",
                    "saved": "💾 Saved",
                    "deleted": "🗑️ Deleted",
                    "updated": "🔄 Updated",
                    "uploaded": "📤 Uploaded",
                    "downloaded": "📥 Downloaded"
                },
                
                # Form Fields
                "forms": {
                    "contract_text": "Contract Text",
                    "contract_type": "Contract Type",
                    "file_upload": "File Upload",
                    "analysis_type": "Analysis Type",
                    "language": "Language",
                    "comments": "Comments",
                    "notes": "Notes",
                    "title": "Title",
                    "description": "Description",
                    "category": "Category",
                    "priority": "Priority",
                    "status": "Status",
                    "date": "Date",
                    "time": "Time",
                    "user": "User",
                    "email": "Email",
                    "password": "Password",
                    "confirm_password": "Confirm Password",
                    "legal_system": "Legal System"
                },
                
                # Legal Systems and Terms
                "legal": {
                    "kuwait_law": "🇰🇼 Kuwaiti Law",
                    "saudi_law": "🇸🇦 Saudi Law",
                    "civil_code": "Civil Code",
                    "commercial_code": "Commercial Code",
                    "labor_law": "Labor Law",
                    "sharia_compliance": "Sharia Compliance",
                    "contract_formation": "Contract Formation",
                    "obligations": "Obligations",
                    "liability": "Liability",
                    "termination": "Termination",
                    "risk_assessment": "Risk Assessment",
                    "compliance_check": "Compliance Check",
                    "legal_analysis": "Legal Analysis",
                    "recommendations": "Legal Recommendations"
                },
                
                # Themes
                "themes": {
                    "light": "🌞 Classic Light",
                    "dark": "🌙 Elegant Dark",
                    "blue_ocean": "🌊 Blue Ocean",
                    "emerald_forest": "🌲 Emerald Forest",
                    "sunset_orange": "🌅 Sunset Orange",
                    "royal_purple": "👑 Royal Purple",
                    "rose_gold": "🌹 Rose Gold",
                    "midnight_blue": "🌌 Midnight Blue",
                    "arctic_white": "❄️ Arctic White",
                    "desert_sand": "🏜️ Desert Sand"
                },
                
                # Settings
                "settings": {
                    "user_preferences": "👤 User Preferences",
                    "ai_settings": "🤖 AI Settings",
                    "security": "🔒 Security",
                    "system": "⚙️ System",
                    "theme": "🎨 Theme",
                    "language_settings": "🌐 Language Settings",
                    "notifications": "🔔 Notifications",
                    "privacy": "🔐 Privacy",
                    "backup": "💾 Backup",
                    "updates": "🔄 Updates"
                }
            }
        }
    
    def set_language(self, language: Language):
        """Set current language"""
        self.current_language = language
        if 'language' not in st.session_state:
            st.session_state.language = language.value
        else:
            st.session_state.language = language.value
    
    def get_current_language(self) -> Language:
        """Get current language"""
        if 'language' in st.session_state:
            try:
                return Language(st.session_state.language)
            except ValueError:
                return Language.ARABIC
        return self.current_language
    
    def get_language_config(self) -> LanguageConfig:
        """Get current language configuration"""
        return self.language_configs[self.get_current_language()]
    
    def t(self, key: str, default: str = None) -> str:
        """Get translated text"""
        current_lang = self.get_current_language()
        keys = key.split('.')
        
        try:
            value = self.translations[current_lang]
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default or key
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages"""
        return {
            Language.ARABIC.value: "العربية",
            Language.ENGLISH.value: "English"
        }
    
    def is_rtl(self) -> bool:
        """Check if current language is RTL"""
        config = self.get_language_config()
        return config.direction == "rtl"
    
    def get_font_family(self) -> str:
        """Get font family for current language"""
        config = self.get_language_config()
        return config.font_family

# Global instance
enhanced_i18n = EnhancedI18nManager()

def get_i18n() -> EnhancedI18nManager:
    """Get global i18n manager"""
    return enhanced_i18n

def t(key: str, default: str = None) -> str:
    """Shorthand for translation"""
    return enhanced_i18n.t(key, default)
