"""
Advanced Security Manager for Enterprise Deployment
Developed by MAXBIT LLC © 2025
"""

import hashlib
import secrets
import hmac
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import sqlite3
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

@dataclass
class SecurityEvent:
    """Security event data structure"""
    timestamp: str
    event_type: str
    severity: str  # low, medium, high, critical
    user_id: Optional[str]
    ip_address: str
    user_agent: str
    details: Dict
    resolved: bool = False

@dataclass
class AuditLog:
    """Audit log entry"""
    timestamp: str
    user_id: str
    action: str
    resource: str
    old_value: Optional[str]
    new_value: Optional[str]
    ip_address: str
    success: bool

class EncryptionManager:
    """Advanced encryption for sensitive data"""
    
    def __init__(self, master_key: Optional[str] = None):
        if master_key:
            self.key = master_key.encode()
        else:
            self.key = self._generate_key()
        
        # Derive encryption key from master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'maxbit_legal_salt_2025',
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.key))
        self.fernet = Fernet(key)
    
    def _generate_key(self) -> bytes:
        """Generate a new encryption key"""
        return secrets.token_bytes(32)
    
    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.fernet.encrypt(data.encode()).decode()
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.fernet.decrypt(encrypted_data.encode()).decode()
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> Tuple[str, str]:
        """Hash password with salt"""
        if not salt:
            salt = secrets.token_hex(32)
        
        # Use PBKDF2 with SHA256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        key = kdf.derive(password.encode())
        hashed = base64.urlsafe_b64encode(key).decode()
        
        return hashed, salt
    
    def verify_password(self, password: str, hashed: str, salt: str) -> bool:
        """Verify password against hash"""
        new_hash, _ = self.hash_password(password, salt)
        return hmac.compare_digest(hashed, new_hash)

class SecurityManager:
    """Comprehensive security management"""
    
    def __init__(self, db_path: str = "security.db"):
        self.db_path = db_path
        self.encryption = EncryptionManager()
        self.logger = logging.getLogger('security')
        self._init_database()
        
        # Security thresholds
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(hours=1)
        self.session_timeout = timedelta(hours=2)
        self.password_min_length = 8
        
        # Rate limiting
        self.rate_limits = {
            'login': {'requests': 10, 'window': 300},  # 10 requests per 5 minutes
            'api': {'requests': 100, 'window': 60},    # 100 requests per minute
            'upload': {'requests': 5, 'window': 300}   # 5 uploads per 5 minutes
        }
        
        self.request_history = {}
    
    def _init_database(self):
        """Initialize security database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    user_id TEXT,
                    ip_address TEXT NOT NULL,
                    user_agent TEXT,
                    details TEXT,
                    resolved BOOLEAN DEFAULT FALSE
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    resource TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    ip_address TEXT NOT NULL,
                    success BOOLEAN NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS failed_attempts (
                    ip_address TEXT PRIMARY KEY,
                    attempts INTEGER DEFAULT 0,
                    last_attempt TEXT,
                    locked_until TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS active_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    ip_address TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_activity TEXT NOT NULL,
                    expires_at TEXT NOT NULL
                )
            """)
    
    def log_security_event(self, event_type: str, severity: str, 
                          user_id: Optional[str] = None, ip_address: str = "unknown",
                          user_agent: str = "unknown", details: Dict = None):
        """Log security event"""
        event = SecurityEvent(
            timestamp=datetime.now().isoformat(),
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
            resolved=False
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO security_events 
                (timestamp, event_type, severity, user_id, ip_address, user_agent, details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                event.timestamp, event.event_type, event.severity,
                event.user_id, event.ip_address, event.user_agent,
                json.dumps(event.details)
            ))
        
        # Log to file as well
        self.logger.warning(f"SECURITY EVENT: {event_type} - {severity} - {ip_address}")
        
        # Alert on critical events
        if severity == "critical":
            self._send_security_alert(event)
    
    def log_audit_event(self, user_id: str, action: str, resource: str,
                       old_value: Optional[str] = None, new_value: Optional[str] = None,
                       ip_address: str = "unknown", success: bool = True):
        """Log audit event"""
        audit = AuditLog(
            timestamp=datetime.now().isoformat(),
            user_id=user_id,
            action=action,
            resource=resource,
            old_value=old_value,
            new_value=new_value,
            ip_address=ip_address,
            success=success
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO audit_logs 
                (timestamp, user_id, action, resource, old_value, new_value, ip_address, success)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                audit.timestamp, audit.user_id, audit.action, audit.resource,
                audit.old_value, audit.new_value, audit.ip_address, audit.success
            ))
    
    def check_rate_limit(self, ip_address: str, endpoint: str) -> bool:
        """Check if request is within rate limits"""
        now = time.time()
        
        if endpoint not in self.rate_limits:
            return True
        
        limit_config = self.rate_limits[endpoint]
        key = f"{ip_address}:{endpoint}"
        
        if key not in self.request_history:
            self.request_history[key] = []
        
        # Clean old requests
        window_start = now - limit_config['window']
        self.request_history[key] = [
            req_time for req_time in self.request_history[key]
            if req_time > window_start
        ]
        
        # Check limit
        if len(self.request_history[key]) >= limit_config['requests']:
            self.log_security_event(
                "rate_limit_exceeded",
                "medium",
                ip_address=ip_address,
                details={"endpoint": endpoint, "limit": limit_config['requests']}
            )
            return False
        
        # Add current request
        self.request_history[key].append(now)
        return True
    
    def track_failed_login(self, ip_address: str, username: str = None) -> bool:
        """Track failed login attempt and return if IP should be blocked"""
        now = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT attempts, locked_until FROM failed_attempts WHERE ip_address = ?",
                (ip_address,)
            )
            result = cursor.fetchone()
            
            if result:
                attempts, locked_until_str = result
                locked_until = datetime.fromisoformat(locked_until_str) if locked_until_str else None
                
                # Check if still locked
                if locked_until and now < locked_until:
                    return True
                
                # Increment attempts
                attempts += 1
                
                # Lock if exceeded max attempts
                if attempts >= self.max_login_attempts:
                    locked_until = now + self.lockout_duration
                    conn.execute("""
                        UPDATE failed_attempts 
                        SET attempts = ?, last_attempt = ?, locked_until = ?
                        WHERE ip_address = ?
                    """, (attempts, now.isoformat(), locked_until.isoformat(), ip_address))
                    
                    self.log_security_event(
                        "ip_blocked",
                        "high",
                        ip_address=ip_address,
                        details={"username": username, "attempts": attempts}
                    )
                    return True
                else:
                    conn.execute("""
                        UPDATE failed_attempts 
                        SET attempts = ?, last_attempt = ?
                        WHERE ip_address = ?
                    """, (attempts, now.isoformat(), ip_address))
            else:
                # First failed attempt
                conn.execute("""
                    INSERT INTO failed_attempts (ip_address, attempts, last_attempt)
                    VALUES (?, 1, ?)
                """, (ip_address, now.isoformat()))
        
        self.log_security_event(
            "login_failed",
            "low",
            ip_address=ip_address,
            details={"username": username}
        )
        
        return False
    
    def clear_failed_attempts(self, ip_address: str):
        """Clear failed login attempts for IP"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("DELETE FROM failed_attempts WHERE ip_address = ?", (ip_address,))
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP is currently blocked"""
        now = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT locked_until FROM failed_attempts WHERE ip_address = ?",
                (ip_address,)
            )
            result = cursor.fetchone()
            
            if result and result[0]:
                locked_until = datetime.fromisoformat(result[0])
                return now < locked_until
        
        return False
    
    def validate_password_strength(self, password: str) -> Tuple[bool, List[str]]:
        """Validate password strength"""
        issues = []
        
        if len(password) < self.password_min_length:
            issues.append(f"Password must be at least {self.password_min_length} characters")
        
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one number")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")
        
        # Check against common passwords
        common_passwords = ["password", "123456", "admin", "user", "test"]
        if password.lower() in common_passwords:
            issues.append("Password is too common")
        
        return len(issues) == 0, issues
    
    def _send_security_alert(self, event: SecurityEvent):
        """Send security alert (placeholder for email/SMS integration)"""
        alert_message = f"""
        CRITICAL SECURITY ALERT
        
        Event: {event.event_type}
        Time: {event.timestamp}
        IP: {event.ip_address}
        User: {event.user_id or 'Unknown'}
        Details: {json.dumps(event.details, indent=2)}
        
        Please investigate immediately.
        """
        
        # In production, this would send email/SMS alerts
        self.logger.critical(f"SECURITY ALERT: {alert_message}")
    
    def get_security_dashboard_data(self) -> Dict:
        """Get security dashboard data"""
        with sqlite3.connect(self.db_path) as conn:
            # Recent security events
            cursor = conn.execute("""
                SELECT event_type, severity, COUNT(*) as count
                FROM security_events 
                WHERE timestamp > datetime('now', '-24 hours')
                GROUP BY event_type, severity
                ORDER BY count DESC
            """)
            recent_events = cursor.fetchall()
            
            # Failed login attempts
            cursor = conn.execute("""
                SELECT COUNT(*) FROM failed_attempts WHERE attempts > 0
            """)
            blocked_ips = cursor.fetchone()[0]
            
            # Active sessions
            cursor = conn.execute("""
                SELECT COUNT(*) FROM active_sessions 
                WHERE expires_at > datetime('now')
            """)
            active_sessions = cursor.fetchone()[0]
            
            # Audit log summary
            cursor = conn.execute("""
                SELECT action, COUNT(*) as count
                FROM audit_logs 
                WHERE timestamp > datetime('now', '-24 hours')
                GROUP BY action
                ORDER BY count DESC
                LIMIT 10
            """)
            audit_summary = cursor.fetchall()
        
        return {
            "recent_events": recent_events,
            "blocked_ips": blocked_ips,
            "active_sessions": active_sessions,
            "audit_summary": audit_summary,
            "security_score": self._calculate_security_score()
        }
    
    def _calculate_security_score(self) -> int:
        """Calculate overall security score (0-100)"""
        score = 100
        
        with sqlite3.connect(self.db_path) as conn:
            # Deduct points for recent security events
            cursor = conn.execute("""
                SELECT severity, COUNT(*) 
                FROM security_events 
                WHERE timestamp > datetime('now', '-24 hours')
                GROUP BY severity
            """)
            
            for severity, count in cursor.fetchall():
                if severity == "critical":
                    score -= count * 20
                elif severity == "high":
                    score -= count * 10
                elif severity == "medium":
                    score -= count * 5
                elif severity == "low":
                    score -= count * 1
        
        return max(0, min(100, score))

# Global security manager instance
security_manager = SecurityManager()

def get_security_manager() -> SecurityManager:
    """Get global security manager instance"""
    return security_manager
