#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analytics Export Manager for Legal Contract Analysis
Provides comprehensive analytics export functionality in multiple formats
"""

import streamlit as st
import pandas as pd
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
import logging
import io
import base64
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnalyticsExportManager:
    """Comprehensive analytics export system"""
    
    def __init__(self, db_manager=None):
        """Initialize analytics export manager"""
        self.db_manager = db_manager
        self.supported_formats = ['csv', 'excel', 'json', 'pdf']
        
    def render_analytics_export_interface(self, user_id: str):
        """Render comprehensive analytics export interface"""
        st.markdown("### 📤 تصدير التحليلات")
        st.markdown("تصدير شامل لجميع البيانات والتحليلات بصيغ متعددة")
        
        # Export type selection
        export_type = st.selectbox(
            "نوع التصدير",
            ["تحليلات المستخدم", "تحليلات النظام", "تقارير الأداء", "إحصائيات شاملة"],
            key="analytics_export_type"
        )
        
        # Date range selection
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "تاريخ البداية",
                value=datetime.now() - timedelta(days=30),
                key="export_start_date"
            )
        with col2:
            end_date = st.date_input(
                "تاريخ النهاية",
                value=datetime.now(),
                key="export_end_date"
            )
        
        # Format selection
        st.markdown("#### 📋 تحديد صيغة التصدير")
        format_cols = st.columns(4)
        
        selected_formats = []
        with format_cols[0]:
            if st.checkbox("📊 Excel (.xlsx)", key="export_excel"):
                selected_formats.append('excel')
        with format_cols[1]:
            if st.checkbox("📄 CSV (.csv)", key="export_csv"):
                selected_formats.append('csv')
        with format_cols[2]:
            if st.checkbox("🔧 JSON (.json)", key="export_json"):
                selected_formats.append('json')
        with format_cols[3]:
            if st.checkbox("📑 PDF Report", key="export_pdf"):
                selected_formats.append('pdf')
        
        # Export options
        st.markdown("#### ⚙️ خيارات التصدير")
        include_charts = st.checkbox("تضمين الرسوم البيانية", value=True)
        include_summary = st.checkbox("تضمين الملخص التنفيذي", value=True)
        include_recommendations = st.checkbox("تضمين التوصيات", value=True)
        
        # Export button
        if st.button("🚀 بدء التصدير", type="primary", use_container_width=True):
            if not selected_formats:
                st.error("❌ يرجى تحديد صيغة واحدة على الأقل للتصدير")
                return
            
            self._perform_analytics_export(
                user_id=user_id,
                export_type=export_type,
                start_date=start_date,
                end_date=end_date,
                formats=selected_formats,
                include_charts=include_charts,
                include_summary=include_summary,
                include_recommendations=include_recommendations
            )
    
    def _perform_analytics_export(self, user_id: str, export_type: str, 
                                start_date, end_date, formats: List[str],
                                include_charts: bool, include_summary: bool,
                                include_recommendations: bool):
        """Perform the analytics export operation"""
        try:
            with st.spinner("🔄 جاري تحضير البيانات للتصدير..."):
                # Get analytics data based on type
                analytics_data = self._get_analytics_data(
                    user_id, export_type, start_date, end_date
                )
                
                if not analytics_data:
                    st.warning("⚠️ لا توجد بيانات متاحة للفترة المحددة")
                    return
                
                # Generate exports for each format
                export_files = {}
                
                for format_type in formats:
                    with st.spinner(f"📦 إنشاء ملف {format_type.upper()}..."):
                        if format_type == 'excel':
                            export_files[format_type] = self._export_to_excel(
                                analytics_data, include_charts, include_summary
                            )
                        elif format_type == 'csv':
                            export_files[format_type] = self._export_to_csv(analytics_data)
                        elif format_type == 'json':
                            export_files[format_type] = self._export_to_json(analytics_data)
                        elif format_type == 'pdf':
                            export_files[format_type] = self._export_to_pdf(
                                analytics_data, include_charts, include_summary, include_recommendations
                            )
                
                # Display download buttons
                self._display_download_buttons(export_files, export_type)
                
                st.success("✅ تم إنشاء ملفات التصدير بنجاح!")
                
        except Exception as e:
            logger.error(f"Analytics export error: {e}")
            st.error(f"❌ خطأ في تصدير التحليلات: {str(e)}")
    
    def _get_analytics_data(self, user_id: str, export_type: str, 
                          start_date, end_date) -> Dict[str, Any]:
        """Get analytics data based on export type"""
        try:
            if not self.db_manager:
                return self._get_sample_analytics_data(export_type)
            
            # Convert dates to strings for SQL
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')
            
            if export_type == "تحليلات المستخدم":
                return self._get_user_analytics(user_id, start_str, end_str)
            elif export_type == "تحليلات النظام":
                return self._get_system_analytics(start_str, end_str)
            elif export_type == "تقارير الأداء":
                return self._get_performance_analytics(start_str, end_str)
            else:  # إحصائيات شاملة
                return self._get_comprehensive_analytics(start_str, end_str)
                
        except Exception as e:
            logger.error(f"Error getting analytics data: {e}")
            return self._get_sample_analytics_data(export_type)
    
    def _get_user_analytics(self, user_id: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get user-specific analytics data"""
        try:
            conn = sqlite3.connect(self.db_manager.db_path)
            
            # User contracts analysis
            contracts_query = '''
                SELECT 
                    contract_type,
                    COUNT(*) as count,
                    AVG(risk_score) as avg_risk,
                    created_at
                FROM contracts 
                WHERE user_id = ? AND DATE(created_at) BETWEEN ? AND ?
                GROUP BY contract_type
            '''
            
            contracts_df = pd.read_sql_query(contracts_query, conn, params=[user_id, start_date, end_date])
            
            # User activity analysis
            activity_query = '''
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as daily_contracts
                FROM contracts 
                WHERE user_id = ? AND DATE(created_at) BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY date
            '''
            
            activity_df = pd.read_sql_query(activity_query, conn, params=[user_id, start_date, end_date])
            
            conn.close()
            
            return {
                'export_type': 'تحليلات المستخدم',
                'user_id': user_id,
                'period': f"{start_date} إلى {end_date}",
                'contracts_by_type': contracts_df.to_dict('records'),
                'daily_activity': activity_df.to_dict('records'),
                'summary': {
                    'total_contracts': len(contracts_df),
                    'avg_risk_score': contracts_df['avg_risk'].mean() if not contracts_df.empty else 0,
                    'most_common_type': contracts_df.loc[contracts_df['count'].idxmax(), 'contract_type'] if not contracts_df.empty else 'N/A'
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting user analytics: {e}")
            return self._get_sample_analytics_data('تحليلات المستخدم')
    
    def _get_sample_analytics_data(self, export_type: str) -> Dict[str, Any]:
        """Generate sample analytics data for demonstration"""
        return {
            'export_type': export_type,
            'period': f"{datetime.now() - timedelta(days=30)} إلى {datetime.now()}",
            'contracts_by_type': [
                {'contract_type': 'عقد خدمات', 'count': 15, 'avg_risk': 25.5},
                {'contract_type': 'عقد عمل', 'count': 8, 'avg_risk': 18.2},
                {'contract_type': 'عقد شراكة', 'count': 5, 'avg_risk': 32.1}
            ],
            'daily_activity': [
                {'date': '2024-01-01', 'daily_contracts': 3},
                {'date': '2024-01-02', 'daily_contracts': 5},
                {'date': '2024-01-03', 'daily_contracts': 2}
            ],
            'summary': {
                'total_contracts': 28,
                'avg_risk_score': 25.3,
                'most_common_type': 'عقد خدمات'
            }
        }
    
    def _export_to_excel(self, data: Dict[str, Any], include_charts: bool, include_summary: bool) -> bytes:
        """Export analytics data to Excel format"""
        try:
            output = io.BytesIO()
            
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # Summary sheet
                if include_summary and 'summary' in data:
                    summary_df = pd.DataFrame([data['summary']])
                    summary_df.to_excel(writer, sheet_name='الملخص', index=False)
                
                # Contracts by type
                if 'contracts_by_type' in data:
                    contracts_df = pd.DataFrame(data['contracts_by_type'])
                    contracts_df.to_excel(writer, sheet_name='العقود حسب النوع', index=False)
                
                # Daily activity
                if 'daily_activity' in data:
                    activity_df = pd.DataFrame(data['daily_activity'])
                    activity_df.to_excel(writer, sheet_name='النشاط اليومي', index=False)
            
            output.seek(0)
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Excel export error: {e}")
            raise
    
    def _export_to_csv(self, data: Dict[str, Any]) -> bytes:
        """Export analytics data to CSV format"""
        try:
            output = io.StringIO()
            
            # Combine all data into a single CSV
            all_data = []
            
            if 'contracts_by_type' in data:
                for item in data['contracts_by_type']:
                    all_data.append({
                        'نوع البيانات': 'عقود حسب النوع',
                        'نوع العقد': item.get('contract_type', ''),
                        'العدد': item.get('count', 0),
                        'متوسط المخاطر': item.get('avg_risk', 0)
                    })
            
            if 'daily_activity' in data:
                for item in data['daily_activity']:
                    all_data.append({
                        'نوع البيانات': 'النشاط اليومي',
                        'التاريخ': item.get('date', ''),
                        'عدد العقود': item.get('daily_contracts', 0)
                    })
            
            df = pd.DataFrame(all_data)
            csv_content = df.to_csv(index=False, encoding='utf-8-sig')
            
            return csv_content.encode('utf-8-sig')
            
        except Exception as e:
            logger.error(f"CSV export error: {e}")
            raise
    
    def _export_to_json(self, data: Dict[str, Any]) -> bytes:
        """Export analytics data to JSON format"""
        try:
            # Add metadata
            export_data = {
                'metadata': {
                    'export_timestamp': datetime.now().isoformat(),
                    'export_type': data.get('export_type', 'Unknown'),
                    'period': data.get('period', 'Unknown')
                },
                'data': data
            }
            
            json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
            return json_str.encode('utf-8')
            
        except Exception as e:
            logger.error(f"JSON export error: {e}")
            raise
    
    def _export_to_pdf(self, data: Dict[str, Any], include_charts: bool, 
                      include_summary: bool, include_recommendations: bool) -> bytes:
        """Export analytics data to PDF format"""
        try:
            # For now, create a simple text-based PDF
            # This can be enhanced with proper PDF libraries
            
            content = f"""
تقرير التحليلات
================

نوع التقرير: {data.get('export_type', 'غير محدد')}
الفترة: {data.get('period', 'غير محددة')}
تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

الملخص التنفيذي:
---------------
إجمالي العقود: {data.get('summary', {}).get('total_contracts', 0)}
متوسط نقاط المخاطر: {data.get('summary', {}).get('avg_risk_score', 0):.1f}
النوع الأكثر شيوعاً: {data.get('summary', {}).get('most_common_type', 'غير محدد')}

العقود حسب النوع:
-----------------
"""
            
            if 'contracts_by_type' in data:
                for contract in data['contracts_by_type']:
                    content += f"- {contract.get('contract_type', 'غير محدد')}: {contract.get('count', 0)} عقد (متوسط المخاطر: {contract.get('avg_risk', 0):.1f})\n"
            
            return content.encode('utf-8')
            
        except Exception as e:
            logger.error(f"PDF export error: {e}")
            raise
    
    def _display_download_buttons(self, export_files: Dict[str, bytes], export_type: str):
        """Display download buttons for exported files"""
        st.markdown("#### 📥 تحميل الملفات")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        cols = st.columns(len(export_files))
        
        for i, (format_type, file_data) in enumerate(export_files.items()):
            with cols[i]:
                file_extension = {
                    'excel': 'xlsx',
                    'csv': 'csv', 
                    'json': 'json',
                    'pdf': 'pdf'
                }.get(format_type, 'txt')
                
                filename = f"analytics_{export_type.replace(' ', '_')}_{timestamp}.{file_extension}"
                
                mime_type = {
                    'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'csv': 'text/csv',
                    'json': 'application/json',
                    'pdf': 'application/pdf'
                }.get(format_type, 'text/plain')
                
                st.download_button(
                    label=f"📥 {format_type.upper()}",
                    data=file_data,
                    file_name=filename,
                    mime=mime_type,
                    use_container_width=True
                )

    def _get_system_analytics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get system-wide analytics data"""
        try:
            if not self.db_manager:
                return self._get_sample_system_analytics()

            conn = sqlite3.connect(self.db_manager.db_path)

            # System usage statistics
            usage_query = '''
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as total_contracts,
                    COUNT(DISTINCT user_id) as unique_users,
                    AVG(risk_score) as avg_risk
                FROM contracts
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY date
            '''

            usage_df = pd.read_sql_query(usage_query, conn, params=[start_date, end_date])

            # Contract types distribution
            types_query = '''
                SELECT
                    contract_type,
                    COUNT(*) as count,
                    AVG(risk_score) as avg_risk
                FROM contracts
                WHERE DATE(created_at) BETWEEN ? AND ?
                GROUP BY contract_type
                ORDER BY count DESC
            '''

            types_df = pd.read_sql_query(types_query, conn, params=[start_date, end_date])

            conn.close()

            return {
                'export_type': 'تحليلات النظام',
                'period': f"{start_date} إلى {end_date}",
                'daily_usage': usage_df.to_dict('records'),
                'contract_types_distribution': types_df.to_dict('records'),
                'summary': {
                    'total_contracts': usage_df['total_contracts'].sum() if not usage_df.empty else 0,
                    'total_users': usage_df['unique_users'].sum() if not usage_df.empty else 0,
                    'avg_daily_contracts': usage_df['total_contracts'].mean() if not usage_df.empty else 0,
                    'system_avg_risk': usage_df['avg_risk'].mean() if not usage_df.empty else 0
                }
            }

        except Exception as e:
            logger.error(f"Error getting system analytics: {e}")
            return self._get_sample_system_analytics()

    def _get_sample_system_analytics(self) -> Dict[str, Any]:
        """Generate sample system analytics data"""
        return {
            'export_type': 'تحليلات النظام',
            'period': f"{datetime.now() - timedelta(days=30)} إلى {datetime.now()}",
            'daily_usage': [
                {'date': '2024-01-01', 'total_contracts': 12, 'unique_users': 8, 'avg_risk': 22.5},
                {'date': '2024-01-02', 'total_contracts': 15, 'unique_users': 10, 'avg_risk': 28.1},
                {'date': '2024-01-03', 'total_contracts': 9, 'unique_users': 6, 'avg_risk': 19.8}
            ],
            'contract_types_distribution': [
                {'contract_type': 'عقد خدمات', 'count': 45, 'avg_risk': 25.5},
                {'contract_type': 'عقد عمل', 'count': 32, 'avg_risk': 18.2},
                {'contract_type': 'عقد شراكة', 'count': 18, 'avg_risk': 32.1}
            ],
            'summary': {
                'total_contracts': 95,
                'total_users': 24,
                'avg_daily_contracts': 12.0,
                'system_avg_risk': 25.3
            }
        }

    def _get_performance_analytics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get performance analytics data"""
        try:
            # This would integrate with monitoring system if available
            return {
                'export_type': 'تقارير الأداء',
                'period': f"{start_date} إلى {end_date}",
                'response_times': [
                    {'operation': 'تحليل العقد', 'avg_time': 2.5, 'success_rate': 98.5},
                    {'operation': 'إنشاء العقد', 'avg_time': 1.8, 'success_rate': 99.2},
                    {'operation': 'تصدير PDF', 'avg_time': 3.2, 'success_rate': 97.8}
                ],
                'system_health': [
                    {'metric': 'استخدام المعالج', 'avg_value': 45.2, 'max_value': 78.5},
                    {'metric': 'استخدام الذاكرة', 'avg_value': 62.1, 'max_value': 85.3},
                    {'metric': 'استخدام القرص', 'avg_value': 34.7, 'max_value': 52.1}
                ],
                'summary': {
                    'avg_response_time': 2.5,
                    'overall_success_rate': 98.5,
                    'system_uptime': 99.8,
                    'total_operations': 1250
                }
            }

        except Exception as e:
            logger.error(f"Error getting performance analytics: {e}")
            return self._get_sample_performance_analytics()

    def _get_sample_performance_analytics(self) -> Dict[str, Any]:
        """Generate sample performance analytics data"""
        return self._get_performance_analytics("", "")

    def _get_comprehensive_analytics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Get comprehensive analytics combining all data types"""
        try:
            user_data = self._get_sample_analytics_data('تحليلات المستخدم')
            system_data = self._get_system_analytics(start_date, end_date)
            performance_data = self._get_performance_analytics(start_date, end_date)

            return {
                'export_type': 'إحصائيات شاملة',
                'period': f"{start_date} إلى {end_date}",
                'user_analytics': user_data,
                'system_analytics': system_data,
                'performance_analytics': performance_data,
                'summary': {
                    'total_contracts': system_data['summary']['total_contracts'],
                    'total_users': system_data['summary']['total_users'],
                    'avg_risk_score': system_data['summary']['system_avg_risk'],
                    'system_performance': performance_data['summary']['overall_success_rate']
                }
            }

        except Exception as e:
            logger.error(f"Error getting comprehensive analytics: {e}")
            return self._get_sample_analytics_data('إحصائيات شاملة')
