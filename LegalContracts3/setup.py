#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for Kuwaiti Legal Contract Analysis Application
Automated installation and configuration
"""

import os
import sys
import subprocess
import platform
import urllib.request
import json
from pathlib import Path

def print_header():
    """Print application header"""
    print("=" * 60)
    print("🏛️  محلل العقود القانونية الكويتية")
    print("   Kuwaiti Legal Contract Analysis Application")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 فحص إصدار Python...")
    
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """Install Python requirements"""
    print("\n📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def check_ollama():
    """Check if Ollama is installed and running"""
    print("\n🤖 فحص Ollama...")
    
    try:
        # Check if ollama command exists
        subprocess.run(["ollama", "--version"], 
                      capture_output=True, check=True)
        print("✅ Ollama مثبت")
        
        # Check if Ollama is running
        try:
            response = urllib.request.urlopen("http://localhost:11434/api/tags", timeout=5)
            if response.status == 200:
                print("✅ Ollama يعمل")
                return True
            else:
                print("⚠️  Ollama مثبت لكن غير مشغل")
                return False
        except:
            print("⚠️  Ollama مثبت لكن غير مشغل")
            return False
            
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Ollama غير مثبت")
        return False

def install_ollama():
    """Install Ollama based on the operating system"""
    print("\n🔧 تثبيت Ollama...")
    
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        print("🍎 نظام macOS - استخدام Homebrew...")
        try:
            subprocess.check_call(["brew", "install", "ollama"])
            print("✅ تم تثبيت Ollama")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل التثبيت. تأكد من تثبيت Homebrew")
            print("   أو قم بالتحميل من: https://ollama.ai/download")
            return False
    
    elif system == "linux":
        print("🐧 نظام Linux...")
        try:
            # Download and run install script
            install_script = urllib.request.urlopen(
                "https://ollama.ai/install.sh"
            ).read().decode()
            
            with open("/tmp/ollama_install.sh", "w") as f:
                f.write(install_script)
            
            subprocess.check_call(["bash", "/tmp/ollama_install.sh"])
            print("✅ تم تثبيت Ollama")
            return True
        except Exception as e:
            print(f"❌ فشل التثبيت: {e}")
            print("   قم بالتحميل من: https://ollama.ai/download")
            return False
    
    else:  # Windows or other
        print("🪟 نظام Windows أو آخر...")
        print("⚠️  يرجى تحميل Ollama من: https://ollama.ai/download")
        print("   ثم تشغيل هذا السكريبت مرة أخرى")
        return False

def start_ollama():
    """Start Ollama service"""
    print("\n🚀 تشغيل Ollama...")
    
    try:
        # Start Ollama in background
        if platform.system().lower() == "windows":
            subprocess.Popen(["ollama", "serve"], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            subprocess.Popen(["ollama", "serve"])
        
        print("✅ تم تشغيل Ollama")
        return True
    except Exception as e:
        print(f"❌ فشل في تشغيل Ollama: {e}")
        return False

def download_model():
    """Download Llama 3.1 model"""
    print("\n📥 تحميل نموذج Llama 3.1...")
    
    try:
        print("⏳ جارٍ التحميل... (قد يستغرق عدة دقائق)")
        subprocess.check_call(["ollama", "pull", "llama3.1:8b"])
        print("✅ تم تحميل النموذج بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تحميل النموذج: {e}")
        return False

def test_installation():
    """Test the complete installation"""
    print("\n🧪 اختبار التثبيت...")
    
    try:
        # Test Ollama API
        response = urllib.request.urlopen("http://localhost:11434/api/tags", timeout=10)
        data = json.loads(response.read().decode())
        
        models = [model['name'] for model in data.get('models', [])]
        if 'llama3.1:8b' in models:
            print("✅ النموذج متاح ويعمل")
            return True
        else:
            print("⚠️  النموذج غير متاح")
            return False
            
    except Exception as e:
        print(f"❌ فشل الاختبار: {e}")
        return False

def create_run_script():
    """Create a script to run the application"""
    print("\n📝 إنشاء سكريبت التشغيل...")
    
    script_content = """#!/bin/bash
# Kuwaiti Legal Contract Analysis Application Runner

echo "🏛️  محلل العقود القانونية الكويتية"
echo "   Starting application..."

# Check if Ollama is running
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "⚠️  Starting Ollama..."
    ollama serve &
    sleep 5
fi

# Start Streamlit app
echo "🚀 Starting Streamlit application..."
streamlit run app.py

echo "✅ Application started successfully!"
echo "🌐 Open your browser to: http://localhost:8501"
"""
    
    try:
        with open("run_app.sh", "w") as f:
            f.write(script_content)
        
        # Make executable on Unix systems
        if platform.system().lower() != "windows":
            os.chmod("run_app.sh", 0o755)
        
        print("✅ تم إنشاء سكريبت التشغيل: run_app.sh")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء السكريبت: {e}")
        return False

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install Python requirements
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        sys.exit(1)
    
    # Check/Install Ollama
    if not check_ollama():
        print("\n🔧 Ollama غير متاح. هل تريد تثبيته؟ (y/n): ", end="")
        if input().lower() in ['y', 'yes', 'نعم']:
            if not install_ollama():
                print("\n❌ فشل في تثبيت Ollama")
                sys.exit(1)
            
            # Start Ollama
            if not start_ollama():
                print("\n❌ فشل في تشغيل Ollama")
                sys.exit(1)
            
            # Wait for Ollama to start
            print("⏳ انتظار تشغيل Ollama...")
            import time
            time.sleep(5)
        else:
            print("\n⚠️  يرجى تثبيت Ollama يدوياً من: https://ollama.ai/download")
            sys.exit(1)
    
    # Download model
    print("\n🤖 هل تريد تحميل نموذج Llama 3.1 8B؟ (y/n): ", end="")
    if input().lower() in ['y', 'yes', 'نعم']:
        if not download_model():
            print("\n⚠️  يمكنك تحميل النموذج لاحقاً باستخدام: ollama pull llama3.1:8b")
    
    # Test installation
    if test_installation():
        print("\n🎉 تم التثبيت بنجاح!")
    else:
        print("\n⚠️  التثبيت مكتمل لكن هناك مشاكل في الاختبار")
    
    # Create run script
    create_run_script()
    
    print("\n" + "=" * 60)
    print("✅ الإعداد مكتمل!")
    print("\n🚀 لتشغيل التطبيق:")
    print("   ./run_app.sh")
    print("   أو")
    print("   streamlit run app.py")
    print("\n🌐 ثم افتح المتصفح على: http://localhost:8501")
    print("=" * 60)

if __name__ == "__main__":
    main()
