#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legal Guidelines Management Interface
Streamlit interface for managing custom legal guidelines
"""

import streamlit as st
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from legal_guidelines_manager import LegalGuidelinesManager

class LegalGuidelinesInterface:
    """Interface for managing legal guidelines"""
    
    def __init__(self):
        """Initialize guidelines interface"""
        self.guidelines_manager = LegalGuidelinesManager()
    
    def render_guidelines_management(self):
        """Render the complete guidelines management interface"""
        st.markdown("# 📚 إدارة المبادئ التوجيهية القانونية")
        st.markdown("إدارة المبادئ التوجيهية المخصصة لتحسين دقة تحليل العقود")
        
        # Tabs for different functions
        tab1, tab2, tab3, tab4 = st.tabs([
            "📋 عرض المبادئ", 
            "➕ إضافة مبدأ جديد", 
            "📂 إدارة الفئات", 
            "📊 إحصائيات الاستخدام"
        ])
        
        with tab1:
            self._render_guidelines_list()
        
        with tab2:
            self._render_add_guideline()
        
        with tab3:
            self._render_categories_management()
        
        with tab4:
            self._render_usage_statistics()
    
    def _render_guidelines_list(self):
        """Render list of existing guidelines"""
        st.markdown("### 📋 المبادئ التوجيهية الحالية")
        
        # Filters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            legal_systems = ['الكل', 'kuwait', 'saudi']
            selected_system = st.selectbox(
                "النظام القانوني:",
                legal_systems,
                key="filter_legal_system"
            )
        
        with col2:
            categories = self.guidelines_manager.get_categories()
            category_names = ['الكل'] + [cat['name'] for cat in categories]
            selected_category = st.selectbox(
                "الفئة:",
                category_names,
                key="filter_category"
            )
        
        with col3:
            show_inactive = st.checkbox("عرض المبادئ غير النشطة", key="show_inactive")
        
        # Get filtered guidelines
        filter_system = None if selected_system == 'الكل' else selected_system
        filter_category = None if selected_category == 'الكل' else selected_category
        
        guidelines = self.guidelines_manager.get_guidelines(
            legal_system=filter_system,
            category=filter_category,
            active_only=not show_inactive
        )
        
        if not guidelines:
            st.info("لا توجد مبادئ توجيهية متطابقة مع المرشحات المحددة")
            return
        
        # Display guidelines
        for guideline in guidelines:
            with st.expander(
                f"{'🟢' if guideline['is_active'] else '🔴'} {guideline['title']}", 
                expanded=False
            ):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.markdown(f"**الفئة:** {guideline['category']}")
                    st.markdown(f"**النظام القانوني:** {guideline['legal_system']}")
                    st.markdown(f"**الأولوية:** {guideline['priority']}")
                    
                    if guideline['source']:
                        st.markdown(f"**المصدر:** {guideline['source']}")
                    
                    if guideline['tags']:
                        tags_str = ', '.join(guideline['tags'])
                        st.markdown(f"**العلامات:** {tags_str}")
                    
                    st.markdown("**المحتوى:**")
                    st.text_area(
                        "المحتوى",
                        value=guideline['content'],
                        height=100,
                        key=f"content_display_{guideline['id']}",
                        disabled=True
                    )
                
                with col2:
                    st.markdown(f"**تاريخ الإنشاء:** {guideline['created_at'][:10]}")
                    st.markdown(f"**آخر تحديث:** {guideline['updated_at'][:10]}")
                    st.markdown(f"**الإصدار:** {guideline['version']}")
                    
                    # Action buttons
                    if st.button(f"✏️ تعديل", key=f"edit_{guideline['id']}"):
                        st.session_state[f"edit_guideline_{guideline['id']}"] = True
                        st.rerun()
                    
                    if guideline['is_active']:
                        if st.button(f"🔴 إلغاء تفعيل", key=f"deactivate_{guideline['id']}"):
                            if self.guidelines_manager.delete_guideline(guideline['id'], soft_delete=True):
                                st.success("تم إلغاء تفعيل المبدأ التوجيهي")
                                st.rerun()
                    else:
                        if st.button(f"🟢 تفعيل", key=f"activate_{guideline['id']}"):
                            if self.guidelines_manager.update_guideline(guideline['id'], is_active=True):
                                st.success("تم تفعيل المبدأ التوجيهي")
                                st.rerun()
                
                # Edit form (if edit mode is active)
                if st.session_state.get(f"edit_guideline_{guideline['id']}", False):
                    self._render_edit_guideline_form(guideline)
    
    def _render_add_guideline(self):
        """Render form to add new guideline"""
        st.markdown("### ➕ إضافة مبدأ توجيهي جديد")
        
        with st.form("add_guideline_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                title = st.text_input("عنوان المبدأ التوجيهي *", key="new_title")
                
                # Get categories for dropdown
                categories = self.guidelines_manager.get_categories()
                category_names = [cat['name'] for cat in categories]
                
                if category_names:
                    category = st.selectbox("الفئة *", category_names, key="new_category")
                else:
                    category = st.text_input("الفئة *", key="new_category_text")
                
                legal_system = st.selectbox(
                    "النظام القانوني *",
                    ['kuwait', 'saudi'],
                    format_func=lambda x: 'الكويت' if x == 'kuwait' else 'السعودية',
                    key="new_legal_system"
                )
            
            with col2:
                source = st.text_input("المصدر (اختياري)", key="new_source")
                priority = st.slider("الأولوية", 1, 10, 5, key="new_priority")
                
                tags_input = st.text_input(
                    "العلامات (مفصولة بفواصل)",
                    help="مثال: بنك مركزي، تمويل، قروض",
                    key="new_tags"
                )
            
            content = st.text_area(
                "محتوى المبدأ التوجيهي *",
                height=200,
                help="اكتب المبدأ التوجيهي بالتفصيل",
                key="new_content"
            )
            
            submitted = st.form_submit_button("💾 حفظ المبدأ التوجيهي", type="primary")
            
            if submitted:
                if not title or not category or not content:
                    st.error("يرجى ملء جميع الحقول المطلوبة (*)")
                else:
                    try:
                        # Parse tags
                        tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()] if tags_input else []
                        
                        # Get current user
                        current_user = st.session_state.get('current_user', {})
                        created_by = current_user.get('username', 'system')
                        
                        # Add guideline
                        guideline_id = self.guidelines_manager.add_guideline(
                            title=title,
                            category=category,
                            content=content,
                            legal_system=legal_system,
                            source=source,
                            tags=tags,
                            priority=priority,
                            created_by=created_by
                        )
                        
                        if guideline_id:
                            st.success("✅ تم إضافة المبدأ التوجيهي بنجاح!")
                            st.rerun()
                        else:
                            st.error("❌ فشل في إضافة المبدأ التوجيهي")
                            
                    except Exception as e:
                        st.error(f"❌ خطأ في إضافة المبدأ التوجيهي: {str(e)}")
    
    def _render_categories_management(self):
        """Render categories management interface"""
        st.markdown("### 📂 إدارة فئات المبادئ التوجيهية")
        
        # Display existing categories
        categories = self.guidelines_manager.get_categories()
        
        if categories:
            st.markdown("#### الفئات الحالية:")
            for category in categories:
                with st.expander(f"📁 {category['name']}", expanded=False):
                    st.markdown(f"**الوصف:** {category['description']}")
                    st.markdown(f"**النظام القانوني:** {category['legal_system']}")
                    st.markdown(f"**تاريخ الإنشاء:** {category['created_at'][:10]}")
                    
                    # Count guidelines in this category
                    guidelines_count = len(self.guidelines_manager.get_guidelines(
                        category=category['name']
                    ))
                    st.markdown(f"**عدد المبادئ:** {guidelines_count}")
        else:
            st.info("لا توجد فئات محددة")
        
        # Add new category
        st.markdown("#### إضافة فئة جديدة:")
        with st.form("add_category_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                cat_name = st.text_input("اسم الفئة *")
                cat_legal_system = st.selectbox(
                    "النظام القانوني *",
                    ['kuwait', 'saudi'],
                    format_func=lambda x: 'الكويت' if x == 'kuwait' else 'السعودية'
                )
            
            with col2:
                cat_description = st.text_area("وصف الفئة")
            
            if st.form_submit_button("➕ إضافة فئة"):
                if cat_name:
                    try:
                        import sqlite3
                        import uuid
                        
                        with sqlite3.connect(self.guidelines_manager.db_path) as conn:
                            conn.execute("""
                                INSERT INTO guideline_categories (id, name, description, legal_system, created_at)
                                VALUES (?, ?, ?, ?, ?)
                            """, (
                                str(uuid.uuid4()),
                                cat_name,
                                cat_description,
                                cat_legal_system,
                                datetime.now().isoformat()
                            ))
                        
                        st.success("✅ تم إضافة الفئة بنجاح!")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"❌ خطأ في إضافة الفئة: {str(e)}")
                else:
                    st.error("يرجى إدخال اسم الفئة")
    
    def _render_usage_statistics(self):
        """Render usage statistics for guidelines"""
        st.markdown("### 📊 إحصائيات استخدام المبادئ التوجيهية")
        
        # Get all guidelines
        guidelines = self.guidelines_manager.get_guidelines()
        
        if not guidelines:
            st.info("لا توجد مبادئ توجيهية لعرض الإحصائيات")
            return
        
        # Statistics overview
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_guidelines = len(guidelines)
            st.metric("إجمالي المبادئ", total_guidelines)
        
        with col2:
            active_guidelines = len([g for g in guidelines if g['is_active']])
            st.metric("المبادئ النشطة", active_guidelines)
        
        with col3:
            kuwait_guidelines = len([g for g in guidelines if g['legal_system'] == 'kuwait'])
            st.metric("مبادئ كويتية", kuwait_guidelines)
        
        with col4:
            saudi_guidelines = len([g for g in guidelines if g['legal_system'] == 'saudi'])
            st.metric("مبادئ سعودية", saudi_guidelines)
        
        # Guidelines by category
        st.markdown("#### توزيع المبادئ حسب الفئة:")
        category_counts = {}
        for guideline in guidelines:
            category = guideline['category']
            category_counts[category] = category_counts.get(category, 0) + 1
        
        if category_counts:
            import pandas as pd
            df = pd.DataFrame(list(category_counts.items()), columns=['الفئة', 'العدد'])
            st.bar_chart(df.set_index('الفئة'))
        
        # Recent guidelines
        st.markdown("#### أحدث المبادئ التوجيهية:")
        recent_guidelines = sorted(guidelines, key=lambda x: x['created_at'], reverse=True)[:5]
        
        for guideline in recent_guidelines:
            st.markdown(f"• **{guideline['title']}** ({guideline['category']}) - {guideline['created_at'][:10]}")
    
    def _render_edit_guideline_form(self, guideline: Dict[str, Any]):
        """Render edit form for a guideline"""
        st.markdown("#### ✏️ تعديل المبدأ التوجيهي")
        
        with st.form(f"edit_guideline_form_{guideline['id']}"):
            col1, col2 = st.columns(2)
            
            with col1:
                new_title = st.text_input("العنوان", value=guideline['title'])
                new_category = st.text_input("الفئة", value=guideline['category'])
                new_legal_system = st.selectbox(
                    "النظام القانوني",
                    ['kuwait', 'saudi'],
                    index=0 if guideline['legal_system'] == 'kuwait' else 1,
                    format_func=lambda x: 'الكويت' if x == 'kuwait' else 'السعودية'
                )
            
            with col2:
                new_source = st.text_input("المصدر", value=guideline['source'] or "")
                new_priority = st.slider("الأولوية", 1, 10, guideline['priority'])
                
                tags_str = ', '.join(guideline['tags']) if guideline['tags'] else ""
                new_tags_input = st.text_input("العلامات", value=tags_str)
            
            new_content = st.text_area(
                "المحتوى",
                value=guideline['content'],
                height=200
            )
            
            col_save, col_cancel = st.columns(2)
            
            with col_save:
                if st.form_submit_button("💾 حفظ التغييرات", type="primary"):
                    try:
                        new_tags = [tag.strip() for tag in new_tags_input.split(',') if tag.strip()]
                        
                        success = self.guidelines_manager.update_guideline(
                            guideline['id'],
                            title=new_title,
                            category=new_category,
                            content=new_content,
                            legal_system=new_legal_system,
                            source=new_source,
                            tags=new_tags,
                            priority=new_priority
                        )
                        
                        if success:
                            st.success("✅ تم تحديث المبدأ التوجيهي بنجاح!")
                            del st.session_state[f"edit_guideline_{guideline['id']}"]
                            st.rerun()
                        else:
                            st.error("❌ فشل في تحديث المبدأ التوجيهي")
                            
                    except Exception as e:
                        st.error(f"❌ خطأ في التحديث: {str(e)}")
            
            with col_cancel:
                if st.form_submit_button("❌ إلغاء"):
                    del st.session_state[f"edit_guideline_{guideline['id']}"]
                    st.rerun()
    
    def render_guidelines_selector(self, legal_system: str = None) -> List[str]:
        """Render guidelines selector for analysis configuration"""
        st.markdown("#### 📚 اختيار المبادئ التوجيهية للتحليل")
        
        guidelines = self.guidelines_manager.get_guidelines(legal_system=legal_system)
        
        if not guidelines:
            st.info("لا توجد مبادئ توجيهية متاحة لهذا النظام القانوني")
            return []
        
        selected_guidelines = []
        
        # Group by category
        categories = {}
        for guideline in guidelines:
            category = guideline['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(guideline)
        
        for category, cat_guidelines in categories.items():
            with st.expander(f"📁 {category}", expanded=True):
                for guideline in cat_guidelines:
                    if st.checkbox(
                        f"{guideline['title']} (أولوية: {guideline['priority']})",
                        key=f"select_guideline_{guideline['id']}"
                    ):
                        selected_guidelines.append(guideline['id'])
        
        return selected_guidelines
