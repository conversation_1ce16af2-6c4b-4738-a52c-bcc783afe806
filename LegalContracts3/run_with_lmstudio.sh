#!/bin/bash
# Kuwaiti Legal Contract Analysis Application - LM Studio Version
# محلل العقود القانونية الكويتية - نسخة LM Studio

echo "🏛️  محلل العقود القانونية الكويتية - LM Studio"
echo "   Kuwaiti Legal Contract Analysis - LM Studio Version"
echo "=" * 60

# Check if Python is available
echo "🔍 Checking Python..."
if ! command -v python3 > /dev/null 2>&1; then
    echo "❌ Python 3 not found"
    echo "   Please install Python 3.8+ from: https://python.org/downloads"
    exit 1
fi
echo "✅ Python 3 is available"

# Check if dependencies are installed
echo "📦 Checking Python dependencies..."
if python3 -c "import streamlit" 2>/dev/null; then
    echo "✅ Streamlit is installed"
else
    echo "❌ Streamlit not found"
    echo "📦 Installing dependencies..."
    pip3 install -r requirements.txt
    if [ $? -eq 0 ]; then
        echo "✅ Dependencies installed successfully"
    else
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Check if LM Studio is running
echo "🤖 Checking LM Studio connection..."
if curl -s http://localhost:1234/v1/models > /dev/null 2>&1; then
    echo "✅ LM Studio is running and accessible"
    
    # Check available models
    models=$(curl -s http://localhost:1234/v1/models | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    models = [model['id'] for model in data.get('data', [])]
    if models:
        print('📋 Available models:', ', '.join(models))
    else:
        print('⚠️  No models loaded in LM Studio')
except:
    print('⚠️  Could not parse models response')
")
    echo "$models"
    
else
    echo "❌ LM Studio is not running or not accessible"
    echo ""
    echo "🔧 Please follow these steps:"
    echo "1. Download LM Studio from: https://lmstudio.ai/"
    echo "2. Install and open LM Studio"
    echo "3. Download a model (recommended: llama-3.1-8b-instruct)"
    echo "4. Go to 'Local Server' tab"
    echo "5. Start the server on port 1234"
    echo "6. Run this script again"
    echo ""
    echo "📖 For detailed instructions, see: LM_STUDIO_SETUP.md"
    echo ""
    read -p "Do you want to continue anyway? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Start the Streamlit application
echo ""
echo "🚀 Starting Streamlit application with LM Studio backend..."
echo "🌐 The application will open in your browser at: http://localhost:8501"
echo "⏹️  Press Ctrl+C to stop the application"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping application..."
    echo "✅ Cleanup complete"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start Streamlit with LM Studio configuration
export AI_BACKEND=lmstudio
export DEFAULT_MODEL=llama-3.1-8b-instruct

streamlit run app.py --server.port 8501 --server.address localhost

# If we reach here, Streamlit exited normally
cleanup
