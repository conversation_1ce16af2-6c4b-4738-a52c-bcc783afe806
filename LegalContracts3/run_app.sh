#!/bin/bash
# Kuwaiti Legal Contract Analysis Application Runner
# محلل العقود القانونية الكويتية

echo "🏛️  محلل العقود القانونية الكويتية"
echo "   Kuwaiti Legal Contract Analysis Application"
echo "=" * 50

# Check if <PERSON>llama is running
echo "🔍 Checking Ollama status..."
if ! curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "⚠️  Ollama is not running. Starting Ollama..."
    
    # Start Ollama in background
    if command -v ollama > /dev/null 2>&1; then
        ollama serve &
        OLLAMA_PID=$!
        echo "⏳ Waiting for <PERSON>lla<PERSON> to start..."
        sleep 10
        
        # Check if Ollama started successfully
        if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
            echo "✅ Ollama started successfully"
        else
            echo "❌ Failed to start Ollama"
            echo "   Please install Ollama from: https://ollama.ai/download"
            exit 1
        fi
    else
        echo "❌ Ollama not found"
        echo "   Please install Ollama from: https://ollama.ai/download"
        exit 1
    fi
else
    echo "✅ Ollama is running"
fi

# Check if required model is available
echo "🤖 Checking for Llama 3.1 model..."
if curl -s http://localhost:11434/api/tags | grep -q "llama3.1:8b"; then
    echo "✅ Llama 3.1 8B model is available"
else
    echo "⚠️  Llama 3.1 8B model not found"
    echo "🔄 Would you like to download it now? (y/n): "
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY]|نعم)$ ]]; then
        echo "📥 Downloading Llama 3.1 8B model..."
        ollama pull llama3.1:8b
        if [ $? -eq 0 ]; then
            echo "✅ Model downloaded successfully"
        else
            echo "❌ Failed to download model"
            echo "   You can download it later with: ollama pull llama3.1:8b"
        fi
    else
        echo "⚠️  You can download the model later with: ollama pull llama3.1:8b"
    fi
fi

# Check if Python dependencies are installed
echo "📦 Checking Python dependencies..."
if python3 -c "import streamlit" 2>/dev/null; then
    echo "✅ Streamlit is installed"
else
    echo "❌ Streamlit not found"
    echo "📦 Installing dependencies..."
    pip3 install -r requirements.txt
    if [ $? -eq 0 ]; then
        echo "✅ Dependencies installed successfully"
    else
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Start the Streamlit application
echo "🚀 Starting Streamlit application..."
echo "🌐 The application will open in your browser at: http://localhost:8501"
echo "⏹️  Press Ctrl+C to stop the application"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping application..."
    if [ ! -z "$OLLAMA_PID" ]; then
        echo "🔄 Stopping Ollama..."
        kill $OLLAMA_PID 2>/dev/null
    fi
    echo "✅ Cleanup complete"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start Streamlit
streamlit run app.py

# If we reach here, Streamlit exited normally
cleanup
