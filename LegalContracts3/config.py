#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for Kuwaiti Legal Contract Analysis Application
Contains all application settings and constants
"""

import os
from pathlib import Path

# Application Information
APP_NAME = "محلل العقود القانونية الكويتية"
APP_NAME_EN = "Kuwaiti Legal Contract Analysis Application"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "تحليل العقود الإنجليزية وفقاً للقانون الكويتي باستخدام الذكاء الاصطناعي المحلي"

# Company Information
COMPANY_NAME = "MAXBIT LLC"
COMPANY_NAME_AR = "شركة ماكس بت المحدودة"
COMPANY_YEAR = "2025"
COMPANY_LOGO_URL = "https://maxbit.net/wp-content/uploads/2022/08/logo.svg"
COMPANY_WEBSITE = "https://maxbit.net"

# File Upload Settings
MAX_FILE_SIZE_MB = 10
ALLOWED_FILE_TYPES = ['.txt', '.doc', '.docx', '.pdf']
UPLOAD_FOLDER = Path("uploads")

# AI Backend Settings
DEFAULT_AI_BACKEND = "lmstudio"
OLLAMA_BASE_URL = "http://localhost:11434"
LMSTUDIO_BASE_URL = "http://localhost:1234"
DEFAULT_MODEL = "law"

# Available Models
AVAILABLE_MODELS = {
    "ollama": [
        "llama3.1:8b",
        "llama3.1:13b",
        "llama3.1:70b",
        "llama3:8b",
        "llama3:13b",
        "llama3:70b",
        "mistral:7b",
        "codellama:7b",
        "codellama:13b"
    ],
    "lmstudio": [
        "llama-3.1-8b-instruct",
        "llama-3.1-13b-instruct",
        "llama-3-8b-instruct",
        "mistral-7b-instruct",
        "codellama-7b-instruct"
    ]
}

# AI Model Parameters
AI_PARAMETERS = {
    "temperature": 0.3,
    "top_p": 0.9,
    "max_tokens": 4000,
    "timeout": 300
}

# UI Theme Settings
THEME_COLORS = {
    "primary": "#3498db",
    "secondary": "#2980b9",
    "background": "#f4f7fa",
    "background_secondary": "#e8f4f8",
    "text_primary": "#2c3e50",
    "text_secondary": "#7f8c8d",
    "success": "#27ae60",
    "warning": "#f39c12",
    "error": "#e74c3c",
    "info": "#3498db"
}

# Arabic Font Settings
ARABIC_FONTS = [
    "Noto Sans Arabic",
    "Arial Unicode MS",
    "Tahoma",
    "Arial"
]

# Legal References
KUWAITI_LAWS = {
    "civil_code": {
        "name": "القانون المدني رقم 67/1980",
        "name_en": "Civil Code No. 67/1980",
        "sections": {
            "contracts": "الباب الثاني: العقود",
            "obligations": "الباب الأول: الالتزامات",
            "damages": "المواد 227-234",
            "termination": "المواد 157-171"
        }
    },
    "commercial_code": {
        "name": "القانون التجاري رقم 68/1980",
        "name_en": "Commercial Code No. 68/1980",
        "sections": {
            "commercial_contracts": "الباب الثالث",
            "companies": "الباب الرابع",
            "commercial_papers": "الباب الخامس"
        }
    },
    "labor_law": {
        "name": "قانون العمل الكويتي",
        "name_en": "Kuwaiti Labor Law",
        "sections": {
            "employment_contracts": "عقود العمل",
            "worker_rights": "حقوق العمال",
            "termination": "إنهاء الخدمة"
        }
    },
    "civil_procedures": {
        "name": "قانون المرافعات المدنية والتجارية",
        "name_en": "Civil and Commercial Procedures Law",
        "sections": {
            "litigation": "الدعاوى القضائية",
            "arbitration": "التحكيم",
            "enforcement": "التنفيذ"
        }
    }
}

# Legal Terms Dictionary
LEGAL_TERMS = {
    "contract": "عقد",
    "party": "طرف",
    "obligation": "التزام",
    "right": "حق",
    "duty": "واجب",
    "liability": "مسؤولية",
    "warranty": "ضمان",
    "guarantee": "كفالة",
    "compensation": "تعويض",
    "damages": "أضرار",
    "termination": "إنهاء",
    "breach": "إخلال",
    "force_majeure": "قوة قاهرة",
    "arbitration": "تحكيم",
    "jurisdiction": "اختصاص قضائي",
    "governing_law": "القانون الحاكم",
    "amendment": "تعديل",
    "assignment": "تنازل",
    "confidentiality": "سرية",
    "intellectual_property": "ملكية فكرية"
}

# Analysis Categories
ANALYSIS_CATEGORIES = {
    "legal_points": {
        "high": {
            "name": "عالية الأهمية",
            "color": "#e74c3c",
            "icon": "🔴"
        },
        "medium": {
            "name": "متوسطة الأهمية",
            "color": "#f39c12",
            "icon": "🟡"
        },
        "low": {
            "name": "منخفضة الأهمية",
            "color": "#27ae60",
            "icon": "🟢"
        }
    },
    "recommendations": {
        "urgent": {
            "name": "عاجل",
            "color": "#e74c3c",
            "icon": "🚨"
        },
        "important": {
            "name": "مهم",
            "color": "#f39c12",
            "icon": "⚡"
        },
        "optional": {
            "name": "اختياري",
            "color": "#3498db",
            "icon": "💡"
        }
    }
}

# Export Settings
EXPORT_SETTINGS = {
    "pdf": {
        "page_size": "A4",
        "margins": {
            "top": 72,
            "bottom": 18,
            "left": 72,
            "right": 72
        },
        "font_size": {
            "title": 18,
            "heading": 14,
            "subheading": 12,
            "body": 10,
            "reference": 9
        }
    },
    "word": {
        "page_size": {
            "width": 8.27,  # inches
            "height": 11.69  # inches
        },
        "margins": {
            "top": 1,
            "bottom": 1,
            "left": 1,
            "right": 1
        },
        "font_size": {
            "title": 18,
            "heading": 14,
            "subheading": 12,
            "body": 11,
            "reference": 9
        }
    }
}

# Session Settings
SESSION_SETTINGS = {
    "max_history_items": 10,
    "auto_save": True,
    "session_timeout": 3600  # 1 hour in seconds
}

# Performance Settings
PERFORMANCE_SETTINGS = {
    "max_text_length": 50000,  # Maximum characters for analysis
    "chunk_size": 2000,  # Text chunk size for processing
    "max_concurrent_requests": 3,
    "cache_size": 100,
    "memory_limit_mb": 1024
}

# Logging Settings
LOGGING_SETTINGS = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "app.log",
    "max_size_mb": 10,
    "backup_count": 5
}

# Security Settings
SECURITY_SETTINGS = {
    "max_upload_attempts": 5,
    "rate_limit_per_minute": 10,
    "allowed_hosts": ["localhost", "127.0.0.1"],
    "secure_headers": True
}

# Development Settings
DEBUG_MODE = os.getenv("DEBUG", "False").lower() == "true"
DEVELOPMENT_MODE = os.getenv("DEVELOPMENT", "False").lower() == "true"

# Environment Variables
def get_env_var(name: str, default: str = "") -> str:
    """Get environment variable with default value"""
    return os.getenv(name, default)

# Application Paths
BASE_DIR = Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
LOGS_DIR = BASE_DIR / "logs"
CACHE_DIR = BASE_DIR / "cache"

# Create directories if they don't exist
for directory in [UPLOAD_FOLDER, STATIC_DIR, LOGS_DIR, CACHE_DIR]:
    directory.mkdir(exist_ok=True)

# API Endpoints
API_ENDPOINTS = {
    "ollama": {
        "generate": "/api/generate",
        "tags": "/api/tags",
        "pull": "/api/pull"
    },
    "lmstudio": {
        "chat": "/v1/chat/completions",
        "models": "/v1/models"
    }
}

# Error Messages
ERROR_MESSAGES = {
    "file_too_large": "حجم الملف كبير جداً. الحد الأقصى: {max_size} MB",
    "invalid_file_type": "نوع الملف غير مدعوم. الأنواع المدعومة: {types}",
    "ai_connection_failed": "فشل الاتصال بنظام الذكاء الاصطناعي",
    "model_not_found": "النموذج المطلوب غير متاح",
    "analysis_failed": "فشل في تحليل العقد",
    "export_failed": "فشل في تصدير النتائج",
    "file_read_error": "خطأ في قراءة الملف"
}

# Success Messages
SUCCESS_MESSAGES = {
    "file_uploaded": "تم رفع الملف بنجاح",
    "analysis_complete": "تم تحليل العقد بنجاح",
    "export_complete": "تم تصدير النتائج بنجاح",
    "model_loaded": "تم تحميل النموذج بنجاح"
}

# Progress Messages
PROGRESS_MESSAGES = {
    "uploading": "جارٍ رفع الملف...",
    "extracting": "جارٍ استخراج النص...",
    "translating": "جارٍ الترجمة إلى العربية...",
    "analyzing": "جارٍ التحليل القانوني...",
    "generating_recommendations": "جارٍ إعداد التوصيات...",
    "exporting": "جارٍ تصدير النتائج..."
}
