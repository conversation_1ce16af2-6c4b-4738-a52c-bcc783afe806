#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kuwaiti Legal Contract Analysis Application
A comprehensive Streamlit app for analyzing English contracts according to Kuwaiti law
with full Arabic interface and local AI processing.
"""

import streamlit as st
import os
import json
import base64
import time
import requests
from datetime import datetime
from pathlib import Path
import tempfile
import io
import pandas as pd

# Import custom modules
from ai_backend import ContractAnalyzer
from export_utils import PDFExporter, WordExporter
from ui_components import load_custom_css, create_sidebar, create_header, create_footer
from utils import validate_file, extract_text_from_file, save_analysis_history
from auth import UserManager, AuthUI, init_auth, is_authenticated, get_current_user, require_permission
from database import ContractDatabase, ContractUI, ContractType
from risk_assessment import RiskAssessment, RiskUI
from collaboration import CollaborationUI
from advanced_reporting import ReportingUI
from template_library import TemplateUI
from mobile_dashboard import MobileDashboard, AdvancedAnalysisUI
from advanced_ai import SpecializedAnalyzer, AIInsightsGenerator
from monitoring import MonitoringDashboard, get_app_monitor, get_security_monitor
from config_manager import get_config
from security_manager import get_security_manager
from business_intelligence import BIDashboard
from internationalization import get_language_manager, get_localization_ui, t
from contract_history import ContractHistoryManager, ContractHistoryUI
from template_manager import TemplateManager, TemplateUI

# Page configuration
st.set_page_config(
    page_title="محلل العقود القانونية الكويتية - MAXBIT LLC",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Load custom CSS and fonts
load_custom_css()

def initialize_session_state():
    """Initialize session state variables"""
    # Initialize authentication
    init_auth()

    # Initialize existing session state
    if 'analysis_history' not in st.session_state:
        st.session_state.analysis_history = []
    if 'current_analysis' not in st.session_state:
        st.session_state.current_analysis = None
    if 'dark_mode' not in st.session_state:
        st.session_state.dark_mode = False
    if 'selected_model' not in st.session_state:
        st.session_state.selected_model = "law"
    if 'ai_backend' not in st.session_state:
        st.session_state.ai_backend = "lmstudio"

    # Initialize new features
    if 'selected_contract' not in st.session_state:
        st.session_state.selected_contract = None
    if 'edit_contract' not in st.session_state:
        st.session_state.edit_contract = None
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "analyze"

    # Initialize UI components
    if 'contract_db' not in st.session_state:
        st.session_state.contract_db = ContractDatabase()
    if 'contract_ui' not in st.session_state:
        st.session_state.contract_ui = ContractUI(st.session_state.contract_db)
    if 'risk_ui' not in st.session_state:
        st.session_state.risk_ui = RiskUI()
    if 'collaboration_ui' not in st.session_state:
        st.session_state.collaboration_ui = CollaborationUI()
    if 'reporting_ui' not in st.session_state:
        st.session_state.reporting_ui = ReportingUI()
    if 'template_ui' not in st.session_state:
        st.session_state.template_ui = TemplateUI()
    if 'mobile_dashboard' not in st.session_state:
        st.session_state.mobile_dashboard = MobileDashboard()
    if 'advanced_analysis_ui' not in st.session_state:
        st.session_state.advanced_analysis_ui = AdvancedAnalysisUI()
    if 'specialized_analyzer' not in st.session_state:
        st.session_state.specialized_analyzer = SpecializedAnalyzer()
    if 'ai_insights' not in st.session_state:
        st.session_state.ai_insights = AIInsightsGenerator()
    if 'monitoring_dashboard' not in st.session_state:
        st.session_state.monitoring_dashboard = MonitoringDashboard()
    if 'config' not in st.session_state:
        st.session_state.config = get_config()
    if 'security_manager' not in st.session_state:
        st.session_state.security_manager = get_security_manager()
    if 'bi_dashboard' not in st.session_state:
        st.session_state.bi_dashboard = BIDashboard()
    if 'lang_manager' not in st.session_state:
        st.session_state.lang_manager = get_language_manager()
    if 'localization_ui' not in st.session_state:
        st.session_state.localization_ui = get_localization_ui()
    if 'contract_history_manager' not in st.session_state:
        st.session_state.contract_history_manager = ContractHistoryManager()
    if 'contract_history_ui' not in st.session_state:
        st.session_state.contract_history_ui = ContractHistoryUI()
    if 'template_manager' not in st.session_state:
        st.session_state.template_manager = TemplateManager()
    if 'template_ui' not in st.session_state:
        st.session_state.template_ui = TemplateUI()

def render_modern_dashboard():
    """Render the modern legal contract dashboard"""

    # Dashboard header
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">لوحة تحكم العقود القانونية</h1>
        <p class="dashboard-subtitle">تحليل شامل للعقود القانونية الكويتية بتقنيات الذكاء الاصطناعي</p>
    </div>
    """, unsafe_allow_html=True)

    # Get contract statistics
    contract_db = st.session_state.get('contract_db')
    total_contracts = 0
    analysis_history = st.session_state.get('analysis_history', [])
    total_analyses = len(analysis_history)

    # Calculate risk levels
    high_risk_count = 0

    # Try to get contract count safely
    try:
        if contract_db and hasattr(contract_db, 'list_contracts'):
            contracts = contract_db.list_contracts()
            total_contracts = len(contracts)
            for contract in contracts:
                if isinstance(contract, dict) and contract.get('risk_level') == 'عالي':
                    high_risk_count += 1
        elif contract_db and hasattr(contract_db, 'contracts'):
            total_contracts = len(contract_db.contracts)
    except Exception as e:
        total_contracts = 0

    # Metric cards row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="metric-card green">
            <div class="metric-icon green">📄</div>
            <p class="metric-title">إجمالي العقود</p>
            <h2 class="metric-value">{total_contracts}</h2>
            <p class="metric-description">العدد الكلي للعقود المحفوظة في النظام</p>
            <button class="metric-button" onclick="window.location.href='#contracts'">عرض العقود</button>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="metric-card blue">
            <div class="metric-icon blue">🔍</div>
            <p class="metric-title">التحليلات المكتملة</p>
            <h2 class="metric-value">{total_analyses}</h2>
            <p class="metric-description">عدد التحليلات القانونية التي تم إجراؤها</p>
            <button class="metric-button" onclick="window.location.href='#analysis'">تحليل جديد</button>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="metric-card yellow">
            <div class="metric-icon yellow">⚠️</div>
            <p class="metric-title">عقود عالية المخاطر</p>
            <h2 class="metric-value">{high_risk_count}</h2>
            <p class="metric-description">العقود التي تحتاج مراجعة قانونية عاجلة</p>
            <button class="metric-button" onclick="window.location.href='#risks'">مراجعة المخاطر</button>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown("""
        <div class="metric-card purple">
            <div class="metric-icon purple">🤖</div>
            <p class="metric-title">المساعد القانوني الذكي</p>
            <h2 class="metric-value">متاح</h2>
            <p class="metric-description">مساعد ذكي لتحليل العقود وفقاً للقانون الكويتي</p>
            <button class="metric-button" onclick="window.location.href='#ai'">استشارة قانونية</button>
        </div>
        """, unsafe_allow_html=True)

    # Recent activity section
    st.markdown("""
    <div class="activity-section">
        <h2 class="activity-title">النشاطات الأخيرة</h2>
    """, unsafe_allow_html=True)

    if analysis_history:
        # Show recent analyses
        recent_analyses = analysis_history[-5:]  # Last 5 analyses
        for i, analysis in enumerate(reversed(recent_analyses)):
            st.markdown(f"""
            <div style="padding: 1rem; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <strong>تحليل عقد: {analysis.get('contract_type', 'غير محدد')}</strong><br>
                    <small style="color: #6b7280;">تاريخ التحليل: {analysis.get('timestamp', 'غير محدد')}</small>
                </div>
                <div style="color: #10b981;">✅ مكتمل</div>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.markdown("""
        <div class="no-activity">
            <p>لا توجد تحليلات حديثة. ابدأ بتحليل عقد جديد!</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("</div>", unsafe_allow_html=True)

def render_maxbit_footer():
    """Render MAXBIT footer with company information"""
    st.markdown("""
    <div class="maxbit-footer">
        <div class="footer-content">
            <div class="footer-section">
                <div class="footer-logo">
                    <div class="footer-logo-icon">⚖️</div>
                    <div class="footer-brand">
                        <div class="footer-brand-title">MAXBIT Legal</div>
                        <div class="footer-brand-subtitle">محلل العقود القانونية الكويتية</div>
                    </div>
                </div>
            </div>
            <div class="footer-section">
                <h4>معلومات الشركة</h4>
                <p><strong>MAXBIT LLC</strong></p>
                <p>شركة متخصصة في تطوير الحلول التقنية القانونية</p>
                <p>الكويت - دولة الكويت</p>
            </div>
            <div class="footer-section">
                <h4>التواصل</h4>
                <p>📧 <EMAIL></p>
                <p>📞 +1 626 509 0918</p>
                <p>🌐 maxbit.net</p>
            </div>
            <div class="footer-section">
                <h4>الخدمات</h4>
                <p>• تحليل العقود القانونية</p>
                <p>• الذكاء الاصطناعي القانوني</p>
                <p>• الاستشارات التقنية</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>© 2025 MAXBIT LLC. جميع الحقوق محفوظة | تم التطوير بواسطة فريق ماكس بت</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

def render_modern_sidebar():
    """Render the modern sidebar with navigation"""

    # Sidebar styling
    st.markdown("""
    <style>
    .sidebar-logo {
        display: flex;
        align-items: center;
        padding: 1.5rem 0;
        color: white;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        margin-bottom: 1rem;
    }

    .sidebar-logo-icon {
        background: linear-gradient(135deg, #4ade80, #22c55e);
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
    }

    .sidebar-brand {
        flex: 1;
    }

    .brand-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.25rem;
    }

    .brand-subtitle {
        font-size: 0.8rem;
        color: rgba(255,255,255,0.8);
        font-weight: 400;
    }

    .nav-section {
        margin: 1.5rem 0;
    }

    .nav-section-title {
        color: #fbbf24;
        font-size: 0.8rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin: 1rem 0 0.5rem 0;
        padding: 0.5rem 1rem;
        background: rgba(251, 191, 36, 0.1);
        border-left: 3px solid #fbbf24;
        border-radius: 6px;
        text-align: center;
    }

    .nav-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        border-radius: 6px;
        margin-bottom: 0.25rem;
        cursor: pointer;
    }

    .nav-item:hover {
        background: rgba(255,255,255,0.1);
        color: white;
    }

    .nav-item.active {
        background: rgba(59,130,246,0.3);
        color: white;
    }

    .nav-icon {
        margin-right: 0.75rem;
        width: 16px;
    }
    </style>
    """, unsafe_allow_html=True)

    # Logo and title with MAXBIT branding
    st.markdown("""
    <div class="sidebar-logo">
        <div class="sidebar-logo-icon">⚖️</div>
        <div class="sidebar-brand">
            <div class="brand-title">MAXBIT Legal</div>
            <div class="brand-subtitle">محلل العقود القانونية الكويتية</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Navigation sections
    st.markdown("---")

    # Overview section
    st.markdown('<div class="nav-section-title">نظرة عامة</div>', unsafe_allow_html=True)

    if st.button("📊 لوحة التحكم", key="nav_dashboard", use_container_width=True):
        st.session_state.current_page = "dashboard"
        st.rerun()

    if st.button("📈 الإحصائيات", key="nav_statistics", use_container_width=True):
        st.session_state.current_page = "statistics"
        st.rerun()

    # Core functions section
    st.markdown('<div class="nav-section-title">الوظائف الأساسية</div>', unsafe_allow_html=True)

    if st.button("📄 تحليل عقد جديد", key="nav_analyze", use_container_width=True):
        st.session_state.current_page = "analyze"
        st.rerun()

    if st.button("📊 قاعدة البيانات", key="nav_database", use_container_width=True):
        st.session_state.current_page = "database"
        st.rerun()

    if st.button("🎯 تحليل المخاطر", key="nav_risk_analysis", use_container_width=True):
        st.session_state.current_page = "risk_analysis"
        st.rerun()

    if st.button("📚 مكتبة القوالب", key="nav_templates", use_container_width=True):
        st.session_state.current_page = "templates"
        st.rerun()

    # Advanced features section
    st.markdown('<div class="nav-section-title">الميزات المتقدمة</div>', unsafe_allow_html=True)

    if st.button("💬 التعاون والتعليقات", key="nav_collaboration", use_container_width=True):
        st.session_state.current_page = "collaboration"
        st.rerun()

    if st.button("📊 التقارير المتقدمة", key="nav_advanced_reports", use_container_width=True):
        st.session_state.current_page = "advanced_reports"
        st.rerun()

    if st.button("🤖 التحليل المتقدم", key="nav_advanced_analysis", use_container_width=True):
        st.session_state.current_page = "advanced_analysis"
        st.rerun()

    if st.button("📱 لوحة الجوال", key="nav_mobile", use_container_width=True):
        st.session_state.current_page = "mobile"
        st.rerun()

    # AI & Monitoring section
    st.markdown('<div class="nav-section-title">الذكاء الاصطناعي والمراقبة</div>', unsafe_allow_html=True)

    if st.button("🤖 رؤى الذكاء الاصطناعي", key="nav_ai_insights", use_container_width=True):
        st.session_state.current_page = "ai_insights"
        st.rerun()

    if st.button("📊 مراقبة النظام", key="nav_monitoring", use_container_width=True):
        st.session_state.current_page = "monitoring"
        st.rerun()

    if st.button("⚙️ الإعدادات", key="nav_settings", use_container_width=True):
        st.session_state.current_page = "settings"
        st.rerun()

def main():
    """Main application function"""
    initialize_session_state()

    # Check authentication
    if not is_authenticated():
        user_manager = UserManager()
        auth_ui = AuthUI(user_manager)
        auth_ui.login_form()
        return

    # Modern dashboard styling with Arabic support
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {visibility: hidden;}

    /* Main app styling with RTL support */
    .stApp {
        background: #f8fafc;
        font-family: 'Noto Sans Arabic', 'Inter', sans-serif;
        direction: rtl;
        text-align: right;
    }

    /* Fix Streamlit components for RTL */
    .stButton > button {
        direction: rtl;
        text-align: center;
    }

    .stTextInput > div > div > input {
        direction: rtl;
        text-align: right;
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #1f2937 0%, #374151 100%) !important;
        color: white !important;
    }

    .css-1d391kg .css-1v0mbdj {
        color: white !important;
    }

    /* Additional sidebar selectors for better coverage */
    .stSidebar > div:first-child {
        background: linear-gradient(180deg, #1f2937 0%, #374151 100%) !important;
    }

    [data-testid="stSidebar"] {
        background: linear-gradient(180deg, #1f2937 0%, #374151 100%) !important;
    }

    [data-testid="stSidebar"] > div:first-child {
        background: linear-gradient(180deg, #1f2937 0%, #374151 100%) !important;
    }

    [data-testid="stSidebar"] .css-1d391kg {
        background: linear-gradient(180deg, #1f2937 0%, #374151 100%) !important;
    }

    /* Sidebar button styling */
    [data-testid="stSidebar"] .stButton > button {
        background: rgba(59, 130, 246, 0.8) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        margin-bottom: 0.5rem !important;
    }

    [data-testid="stSidebar"] .stButton > button:hover {
        background: rgba(59, 130, 246, 1) !important;
        border-color: rgba(255, 255, 255, 0.4) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    [data-testid="stSidebar"] .stButton > button:active {
        background: rgba(37, 99, 235, 1) !important;
        transform: translateY(0) !important;
    }

    /* Main content area */
    .main .block-container {
        padding: 2rem 3rem;
        max-width: none;
    }

    /* Dashboard header */
    .dashboard-header {
        background: white;
        padding: 1.5rem 2rem;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 4px solid #1e40af;
    }

    .dashboard-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .dashboard-subtitle {
        color: #6b7280;
        margin: 0.25rem 0 0 0;
        font-size: 0.95rem;
    }

    /* Ultra-Enhanced Metric cards */
    .metric-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
        padding: 2.5rem 2rem;
        border-radius: 24px;
        box-shadow:
            0 20px 40px rgba(0,0,0,0.08),
            0 8px 16px rgba(0,0,0,0.04),
            inset 0 1px 0 rgba(255,255,255,0.9);
        border: 1px solid rgba(255,255,255,0.3);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
    }

    .metric-card:hover {
        transform: translateY(-12px) scale(1.02);
        box-shadow:
            0 32px 64px rgba(0,0,0,0.12),
            0 16px 32px rgba(0,0,0,0.08),
            inset 0 1px 0 rgba(255,255,255,0.9);
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--card-color), var(--card-color-light), var(--card-color));
        border-radius: 24px 24px 0 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .metric-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, var(--card-color-alpha) 0%, transparent 70%);
        pointer-events: none;
        transition: all 0.4s ease;
        opacity: 0.3;
    }

    .metric-card:hover::after {
        top: -30%;
        right: -30%;
        opacity: 0.5;
    }

    .metric-card.green {
        --card-color: #10b981;
        --card-color-light: #34d399;
        --card-color-alpha: rgba(16, 185, 129, 0.1);
    }
    .metric-card.blue {
        --card-color: #3b82f6;
        --card-color-light: #60a5fa;
        --card-color-alpha: rgba(59, 130, 246, 0.1);
    }
    .metric-card.red {
        --card-color: #ef4444;
        --card-color-light: #f87171;
        --card-color-alpha: rgba(239, 68, 68, 0.1);
    }
    .metric-card.yellow {
        --card-color: #f59e0b;
        --card-color-light: #fbbf24;
        --card-color-alpha: rgba(245, 158, 11, 0.1);
    }
    .metric-card.purple {
        --card-color: #8b5cf6;
        --card-color-light: #a78bfa;
        --card-color-alpha: rgba(139, 92, 246, 0.1);
    }

    .metric-icon {
        width: 70px;
        height: 70px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
        box-shadow:
            0 12px 24px rgba(0,0,0,0.15),
            0 6px 12px rgba(0,0,0,0.1),
            inset 0 1px 0 rgba(255,255,255,0.2);
        position: relative;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .metric-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        border-radius: 20px;
        background: radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        pointer-events: none;
        transition: all 0.3s ease;
    }

    .metric-icon::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
        transform: rotate(45deg);
        transition: all 0.6s ease;
        opacity: 0;
    }

    .metric-card:hover .metric-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow:
            0 16px 32px rgba(0,0,0,0.2),
            0 8px 16px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.3);
    }

    .metric-card:hover .metric-icon::before {
        animation: shimmer 1.5s ease-in-out;
        opacity: 1;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .metric-icon.green {
        background: linear-gradient(135deg, #10b981, #34d399);
    }
    .metric-icon.blue {
        background: linear-gradient(135deg, #3b82f6, #60a5fa);
    }
    .metric-icon.red {
        background: linear-gradient(135deg, #ef4444, #f87171);
    }
    .metric-icon.yellow {
        background: linear-gradient(135deg, #f59e0b, #fbbf24);
    }
    .metric-icon.purple {
        background: linear-gradient(135deg, #8b5cf6, #a78bfa);
    }

    .metric-title {
        font-size: 1.1rem;
        color: #6b7280;
        font-weight: 700;
        margin: 0 0 0.75rem 0;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        transition: all 0.3s ease;
    }

    .metric-value {
        font-size: 3rem;
        font-weight: 900;
        color: #1f2937;
        margin: 0.75rem 0;
        background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all 0.3s ease;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .metric-description {
        font-size: 0.95rem;
        color: #9ca3af;
        font-weight: 500;
        line-height: 1.5;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .metric-card:hover .metric-title {
        color: #4b5563;
        transform: translateY(-2px);
    }

    .metric-card:hover .metric-value {
        transform: scale(1.05);
        text-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .metric-card:hover .metric-description {
        color: #6b7280;
    }

    /* Enhanced Dashboard Cards */
    .dashboard-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08), 0 1px 8px rgba(0,0,0,0.04);
        border: 1px solid rgba(255,255,255,0.8);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.12), 0 5px 15px rgba(0,0,0,0.08);
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    /* Contract Cards */
    .contract-card {
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .contract-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        border-color: #d1d5db;
    }

    .contract-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .contract-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
        line-height: 1.3;
    }

    .contract-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-analyzed {
        background: linear-gradient(135deg, #d1fae5, #a7f3d0);
        color: #065f46;
    }

    .status-pending {
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        color: #92400e;
    }

    .status-review {
        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        color: #1e40af;
    }

    /* Action Buttons */
    .action-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .action-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .action-button.secondary {
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        color: #374151;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .action-button.danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }
        margin: 0;
    }

    .metric-button {
        background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow:
            0 4px 12px rgba(0,0,0,0.15),
            0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .metric-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: all 0.5s ease;
    }

    .metric-button:hover {
        transform: translateY(-2px);
        box-shadow:
            0 8px 20px rgba(0,0,0,0.2),
            0 4px 8px rgba(0,0,0,0.15);
    }

    .metric-button:hover::before {
        left: 100%;
    }

    .metric-button:active {
        transform: translateY(0);
        box-shadow:
            0 4px 12px rgba(0,0,0,0.15),
            0 2px 4px rgba(0,0,0,0.1);
    }

    /* Recent activity section */
    .activity-section {
        background: white;
        padding: 1.5rem 2rem;
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }

    .activity-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 1rem 0;
    }

    .no-activity {
        text-align: center;
        color: #9ca3af;
        font-style: italic;
        padding: 2rem;
    }

    /* MAXBIT Footer */
    .maxbit-footer {
        background: linear-gradient(135deg, #1e40af, #3b82f6);
        color: white;
        margin-top: 4rem;
        padding: 3rem 2rem 1rem;
        border-radius: 12px 12px 0 0;
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .footer-section h4 {
        color: #fbbf24;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        border-bottom: 2px solid #fbbf24;
        padding-bottom: 0.5rem;
    }

    .footer-section p {
        margin: 0.5rem 0;
        color: rgba(255,255,255,0.9);
        font-size: 0.9rem;
    }

    .footer-logo {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .footer-logo-icon {
        background: linear-gradient(135deg, #4ade80, #22c55e);
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(74, 222, 128, 0.3);
    }

    .footer-brand-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: white;
        margin-bottom: 0.25rem;
    }

    .footer-brand-subtitle {
        font-size: 0.9rem;
        color: rgba(255,255,255,0.8);
    }

    .footer-bottom {
        border-top: 1px solid rgba(255,255,255,0.2);
        margin-top: 2rem;
        padding-top: 1rem;
        text-align: center;
        color: rgba(255,255,255,0.8);
        font-size: 0.85rem;
    }
    </style>
    """, unsafe_allow_html=True)

    # Sidebar navigation
    with st.sidebar:
        render_modern_sidebar()

        # User info at bottom
        st.markdown("---")
        current_user = get_current_user()
        if current_user:
            st.markdown(f"**المستخدم:** {current_user.get('username', 'غير معروف')}")
            st.markdown(f"**الدور:** {current_user.get('role', 'مستخدم')}")

            if st.button("تسجيل الخروج", type="secondary", use_container_width=True):
                user_manager = UserManager()
                auth_ui = AuthUI(user_manager)
                auth_ui.logout()
                st.rerun()

    # Main content area - restore original functionality
    current_page = st.session_state.get('current_page', 'dashboard')
    current_user = get_current_user()

    if current_page == "dashboard":
        render_modern_dashboard()
    else:
        # Use the original display_page_content function for all other pages
        display_page_content(current_user)

    # Add MAXBIT footer
    render_maxbit_footer()

def create_navigation(current_user):
    """Create navigation menu"""
    st.markdown("### 🧭 التنقل")

    # Main navigation row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("📄 تحليل عقد جديد", use_container_width=True):
            st.session_state.current_page = "analyze"
            st.rerun()

    with col2:
        if st.button("📊 قاعدة البيانات", use_container_width=True):
            st.session_state.current_page = "database"
            st.rerun()

    with col3:
        if st.button("📈 الإحصائيات", use_container_width=True):
            st.session_state.current_page = "statistics"
            st.rerun()

    with col4:
        if st.button("📚 مكتبة القوالب", use_container_width=True):
            st.session_state.current_page = "templates"
            st.rerun()

    # Second navigation row
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("📜 تاريخ التحليل", use_container_width=True):
            st.session_state.current_page = "history"
            st.rerun()

    with col2:
        if st.button("🎯 تحليل المخاطر", use_container_width=True):
            st.session_state.current_page = "risk_analysis"
            st.rerun()

    with col3:
        if st.button("💬 التعاون والتعليقات", use_container_width=True):
            st.session_state.current_page = "collaboration"
            st.rerun()

    with col4:
        if st.button("📊 التقارير المتقدمة", use_container_width=True):
            st.session_state.current_page = "advanced_reports"
            st.rerun()

    # Advanced features row
    st.markdown("#### 🚀 الميزات المتقدمة")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🤖 التحليل المتقدم", use_container_width=True):
            st.session_state.current_page = "advanced_analysis"
            st.rerun()

    with col2:
        if st.button("🔍 البحث المتقدم", use_container_width=True):
            st.session_state.current_page = "advanced_search"
            st.rerun()

    with col3:
        if st.button("⚙️ الإعدادات", use_container_width=True):
            st.session_state.current_page = "settings"
            st.rerun()

    with col4:
        if st.button("📱 لوحة المراقبة", use_container_width=True):
            st.session_state.current_page = "monitoring"
            st.rerun()

    # AI & Mobile features row
    st.markdown("#### 🤖 الذكاء الاصطناعي والجوال")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("📱 لوحة التحكم المحمولة", use_container_width=True):
            st.session_state.current_page = "mobile_dashboard"
            st.rerun()

    with col2:
        if st.button("🔌 واجهة برمجة التطبيقات", use_container_width=True):
            st.session_state.current_page = "api_docs"
            st.rerun()

    with col3:
        if st.button("📈 تحليلات الذكاء الاصطناعي", use_container_width=True):
            st.session_state.current_page = "ai_insights"
            st.rerun()

    with col4:
        if st.button("📊 مراقبة النظام", use_container_width=True):
            st.session_state.current_page = "monitoring"
            st.rerun()

    # Admin features row (for admin users only)
    if current_user["role"] == "admin":
        st.markdown("#### 👑 إدارة النظام")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("👥 إدارة المستخدمين", use_container_width=True):
                st.session_state.current_page = "admin"
                st.rerun()

        with col2:
            if st.button("⚙️ إعدادات النظام", use_container_width=True):
                st.session_state.current_page = "system_config"
                st.rerun()

        with col3:
            if st.button("🔧 أدوات الصيانة", use_container_width=True):
                st.session_state.current_page = "maintenance"
                st.rerun()

        with col4:
            if st.button("📋 سجلات النظام", use_container_width=True):
                st.session_state.current_page = "system_logs"
                st.rerun()

    # Enterprise features row
    st.markdown("#### 🏢 الميزات المؤسسية")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🔒 الأمان المتقدم", use_container_width=True):
            st.session_state.current_page = "security_dashboard"
            st.rerun()

    with col2:
        if st.button("📈 ذكاء الأعمال", use_container_width=True):
            st.session_state.current_page = "business_intelligence"
            st.rerun()

    with col3:
        if st.button("🌐 اللغات المتعددة", use_container_width=True):
            st.session_state.current_page = "language_settings"
            st.rerun()

    with col4:
        if st.button("📊 التحليل التنبؤي", use_container_width=True):
            st.session_state.current_page = "predictive_analytics"
            st.rerun()

def display_page_content(current_user):
    """Display content based on current page"""
    page = st.session_state.get("current_page", "analyze")

    if page == "analyze":
        display_analyze_page()
    elif page == "database":
        display_database_page(current_user)
    elif page == "statistics":
        display_statistics_page(current_user)
    elif page == "templates":
        display_templates_page(current_user)
    elif page == "history":
        display_history_page(current_user)
    elif page == "collaboration":
        display_collaboration_page(current_user)
    elif page == "advanced_reports":
        display_advanced_reports_page(current_user)
    elif page == "risk_analysis":
        display_risk_analysis_page(current_user)
    elif page == "advanced_analysis":
        display_advanced_analysis_page(current_user)
    elif page == "mobile_dashboard":
        display_mobile_dashboard_page(current_user)
    elif page == "api_docs":
        display_api_docs_page()
    elif page == "ai_insights":
        display_ai_insights_page(current_user)
    elif page == "monitoring":
        display_monitoring_page(current_user)
    elif page == "settings":
        display_settings_page(current_user)
    elif page == "admin" and current_user["role"] == "admin":
        display_admin_page()
    elif page == "system_config" and current_user["role"] == "admin":
        display_system_config_page()
    elif page == "maintenance" and current_user["role"] == "admin":
        display_maintenance_page()
    elif page == "system_logs" and current_user["role"] == "admin":
        display_system_logs_page()
    elif page == "security_dashboard":
        display_security_dashboard_page(current_user)
    elif page == "business_intelligence":
        display_business_intelligence_page(current_user)
    elif page == "language_settings":
        display_language_settings_page()
    elif page == "predictive_analytics":
        display_predictive_analytics_page(current_user)
    else:
        display_analyze_page()

def display_analyze_page():
    """Display contract analysis page"""
    st.markdown("### 📄 رفع ملف العقد")

    uploaded_file = st.file_uploader(
        "اختر ملف العقد للتحليل",
        type=['txt', 'doc', 'docx', 'pdf'],
        help="يدعم التطبيق ملفات TXT و DOC و DOCX و PDF"
    )

    if uploaded_file is not None:
        # Validate file
        if validate_file(uploaded_file):
            st.success(f"✅ تم رفع الملف بنجاح: {uploaded_file.name}")

            # Extract text from file
            with st.spinner("🔍 جارٍ استخراج النص من الملف..."):
                contract_text = extract_text_from_file(uploaded_file)

            if contract_text:
                st.success("✅ تم استخراج النص بنجاح")

                # Contract type selection
                st.markdown("### 📋 نوع العقد")
                contract_type = st.selectbox(
                    "اختر نوع العقد:",
                    options=[ContractType.EMPLOYMENT, ContractType.COMMERCIAL,
                            ContractType.REAL_ESTATE, ContractType.SERVICE,
                            ContractType.PARTNERSHIP, ContractType.OTHER],
                    format_func=lambda x: {
                        ContractType.EMPLOYMENT: "عقد عمل",
                        ContractType.COMMERCIAL: "عقد تجاري",
                        ContractType.REAL_ESTATE: "عقد عقاري",
                        ContractType.SERVICE: "عقد خدمة",
                        ContractType.PARTNERSHIP: "عقد شراكة",
                        ContractType.OTHER: "أخرى"
                    }.get(x, x)
                )

                # Analyze button
                if st.button("🔍 تحليل العقد", type="primary", use_container_width=True):
                    analysis = analyze_contract(contract_text, uploaded_file.name)
                    if analysis:
                        # Save to database
                        db = ContractDatabase()
                        current_user = get_current_user()
                        contract_id = db.save_contract(
                            uploaded_file.name,
                            contract_text,
                            analysis,
                            current_user["id"],
                            contract_type
                        )
                        st.success(f"✅ تم حفظ العقد في قاعدة البيانات (ID: {contract_id[:8]})")
            else:
                st.error("❌ فشل في استخراج النص من الملف")
        else:
            st.error("❌ نوع الملف غير مدعوم")

    # Display analysis results if available
    if st.session_state.current_analysis:
        display_analysis_results()

def display_database_page(current_user):
    """Display contract database page"""
    db = ContractDatabase()
    contract_ui = ContractUI(db)

    # Show user's contracts only (unless admin)
    user_id = None if current_user["role"] == "admin" else current_user["id"]
    contract_ui.display_contract_list(user_id)

def display_statistics_page(current_user):
    """Display statistics page"""
    db = ContractDatabase()
    contract_ui = ContractUI(db)

    # Show user's statistics only (unless admin)
    user_id = None if current_user["role"] == "admin" else current_user["id"]
    contract_ui.display_statistics(user_id)

def display_admin_page():
    """Display admin page"""
    st.markdown("### 👥 إدارة المستخدمين")

    user_manager = UserManager()

    # Add new user
    with st.expander("➕ إضافة مستخدم جديد"):
        with st.form("add_user_form"):
            col1, col2 = st.columns(2)

            with col1:
                new_username = st.text_input("اسم المستخدم")
                new_email = st.text_input("البريد الإلكتروني")

            with col2:
                new_full_name = st.text_input("الاسم الكامل")
                new_role = st.selectbox("الدور", ["lawyer", "paralegal", "client"])

            new_password = st.text_input("كلمة المرور", type="password")

            if st.form_submit_button("إضافة المستخدم"):
                if new_username and new_password and new_email and new_full_name:
                    current_user = get_current_user()
                    if user_manager.create_user(new_username, new_password, new_email,
                                              new_full_name, new_role, current_user["username"]):
                        st.success("✅ تم إضافة المستخدم بنجاح")
                        st.rerun()
                    else:
                        st.error("❌ اسم المستخدم موجود بالفعل")
                else:
                    st.error("❌ يرجى ملء جميع الحقول")

    # List users
    st.markdown("#### 📋 قائمة المستخدمين")
    users = user_manager.list_users()

    for user in users:
        with st.expander(f"👤 {user['full_name']} ({user['username']})"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown(f"**البريد:** {user['email']}")
                st.markdown(f"**الدور:** {user['role']}")
                st.markdown(f"**تاريخ الإنشاء:** {user['created_at'][:10]}")

            with col2:
                if user['username'] != 'admin':
                    if st.button(f"حذف المستخدم", key=f"delete_{user['username']}"):
                        if user_manager.delete_user(user['username']):
                            st.success("تم حذف المستخدم")
                            st.rerun()

def analyze_contract(contract_text, filename):
    """Analyze the contract using AI backend"""
    try:
        # Initialize analyzer
        analyzer = ContractAnalyzer(
            backend=st.session_state.ai_backend,
            model=st.session_state.selected_model
        )
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Stage 1: Translation
        status_text.text("🔄 جارٍ الترجمة إلى العربية... 10%")
        progress_bar.progress(10)
        
        translation = analyzer.translate_to_arabic(contract_text)
        
        # Stage 2: Legal Analysis
        status_text.text("⚖️ جارٍ التحليل القانوني... 50%")
        progress_bar.progress(50)
        
        legal_points = analyzer.analyze_legal_points(contract_text)
        
        # Stage 3: Recommendations
        status_text.text("💡 جارٍ إعداد التوصيات... 80%")
        progress_bar.progress(80)
        
        recommendations = analyzer.generate_recommendations(contract_text)
        
        # Complete analysis
        status_text.text("✅ تم التحليل بنجاح! 100%")
        progress_bar.progress(100)
        
        # Store analysis results
        analysis_result = {
            'filename': filename,
            'timestamp': datetime.now().isoformat(),
            'original_text': contract_text,
            'translation': translation,
            'legal_points': legal_points,
            'recommendations': recommendations,
            'risk_score': 0.0  # Will be calculated by risk assessment
        }

        st.session_state.current_analysis = analysis_result
        save_analysis_history(analysis_result)

        # Save to database and history
        current_user = get_current_user()
        if current_user:
            # Save contract to database
            db = ContractDatabase()
            contract_id = db.add_contract(
                filename=filename,
                original_text=contract_text,
                analysis_result=analysis_result,
                contract_type="general",  # Can be enhanced later
                user_id=current_user['id']
            )

            # Save to history
            if contract_id:
                history_manager = st.session_state.contract_history_manager
                history_manager.save_analysis_to_history(
                    contract_id=contract_id,
                    analysis_data=analysis_result,
                    user_id=current_user['id'],
                    action_type="analysis",
                    action_description=f"تحليل العقد: {filename}"
                )
        
        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()
        
        st.success("🎉 تم تحليل العقد بنجاح!")
        
    except Exception as e:
        st.error(f"❌ حدث خطأ أثناء التحليل: {str(e)}")

def display_analysis_results():
    """Display analysis results in tabs"""
    st.markdown("## 📊 نتائج التحليل")

    analysis = st.session_state.current_analysis

    # Create tabs with risk assessment
    tab1, tab2, tab3, tab4 = st.tabs(["🔤 الترجمة", "⚖️ النقاط القانونية", "💡 التوصيات", "🎯 تقييم المخاطر"])

    with tab1:
        display_translation_tab(analysis['translation'])

    with tab2:
        display_legal_points_tab(analysis['legal_points'])

    with tab3:
        display_recommendations_tab(analysis['recommendations'])

    with tab4:
        # Display enhanced risk assessment
        risk_ui = RiskUI()
        risk_ui.display_risk_dashboard(analysis)
    
    # Export buttons
    st.markdown("### 📥 تصدير النتائج")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📄 تصدير PDF", use_container_width=True):
            export_to_pdf(analysis)
    
    with col2:
        if st.button("📝 تصدير Word", use_container_width=True):
            export_to_word(analysis)
    
    with col3:
        if st.button("💾 حفظ JSON", use_container_width=True):
            export_to_json(analysis)

def display_translation_tab(translation):
    """Display translation results"""
    st.markdown("### الترجمة الكاملة للعقد")
    
    if translation:
        # Highlight legal terms
        highlighted_translation = highlight_legal_terms(translation)
        st.markdown(
            f'<div class="translation-content">{highlighted_translation}</div>',
            unsafe_allow_html=True
        )
    else:
        st.warning("لا توجد ترجمة متاحة")

def display_legal_points_tab(legal_points):
    """Display legal analysis points"""
    st.markdown("### النقاط القانونية حسب القانون الكويتي")

    # Safety check: ensure legal_points is a list
    if isinstance(legal_points, str):
        st.error("خطأ في تحليل النقاط القانونية - تم إرجاع نص بدلاً من قائمة")
        st.text_area("النص المُرجع:", legal_points, height=200)
        return

    if legal_points and isinstance(legal_points, list):
        # Categorize by importance
        high_priority = [p for p in legal_points if isinstance(p, dict) and p.get('priority') == 'high']
        medium_priority = [p for p in legal_points if isinstance(p, dict) and p.get('priority') == 'medium']
        low_priority = [p for p in legal_points if isinstance(p, dict) and p.get('priority') == 'low']
        
        # Display high priority points
        if high_priority:
            st.markdown("#### 🔴 نقاط عالية الأهمية")
            for point in high_priority:
                with st.expander(f"⚠️ {point['title']}"):
                    st.markdown(point['description'])
                    if 'law_reference' in point:
                        st.info(f"المرجع القانوني: {point['law_reference']}")
        
        # Display medium priority points
        if medium_priority:
            st.markdown("#### 🟡 نقاط متوسطة الأهمية")
            for point in medium_priority:
                with st.expander(f"⚡ {point['title']}"):
                    st.markdown(point['description'])
                    if 'law_reference' in point:
                        st.info(f"المرجع القانوني: {point['law_reference']}")
        
        # Display low priority points
        if low_priority:
            st.markdown("#### 🟢 نقاط منخفضة الأهمية")
            for point in low_priority:
                with st.expander(f"ℹ️ {point['title']}"):
                    st.markdown(point['description'])
                    if 'law_reference' in point:
                        st.info(f"المرجع القانوني: {point['law_reference']}")
    else:
        st.warning("لا توجد نقاط قانونية متاحة")

def display_recommendations_tab(recommendations):
    """Display recommendations"""
    st.markdown("### التوصيات لتحسين العقد")

    # Safety check: ensure recommendations is a list
    if isinstance(recommendations, str):
        st.error("خطأ في تحليل التوصيات - تم إرجاع نص بدلاً من قائمة")
        st.text_area("النص المُرجع:", recommendations, height=200)
        return

    if recommendations and isinstance(recommendations, list):
        # Categorize by urgency
        urgent = [r for r in recommendations if isinstance(r, dict) and r.get('urgency') == 'urgent']
        important = [r for r in recommendations if isinstance(r, dict) and r.get('urgency') == 'important']
        optional = [r for r in recommendations if isinstance(r, dict) and r.get('urgency') == 'optional']
        
        # Display urgent recommendations
        if urgent:
            st.markdown("#### 🚨 توصيات عاجلة")
            for rec in urgent:
                st.error(f"**{rec['title']}**")
                st.markdown(rec['description'])
                st.markdown("---")
        
        # Display important recommendations
        if important:
            st.markdown("#### ⚡ توصيات مهمة")
            for rec in important:
                st.warning(f"**{rec['title']}**")
                st.markdown(rec['description'])
                st.markdown("---")
        
        # Display optional recommendations
        if optional:
            st.markdown("#### 💡 توصيات اختيارية")
            for rec in optional:
                st.info(f"**{rec['title']}**")
                st.markdown(rec['description'])
                st.markdown("---")
    else:
        st.warning("لا توجد توصيات متاحة")

def highlight_legal_terms(text):
    """Highlight legal terms in Arabic text"""
    legal_terms = [
        "عقد", "طرف", "التزام", "حق", "واجب", "مسؤولية", "ضمان", "تعويض",
        "فسخ", "إنهاء", "تجديد", "تعديل", "شرط", "بند", "مادة", "قانون"
    ]
    
    highlighted_text = text
    for term in legal_terms:
        highlighted_text = highlighted_text.replace(
            term, f'<span class="legal-term">{term}</span>'
        )
    
    return highlighted_text

def export_to_pdf(analysis):
    """Export analysis to PDF"""
    try:
        exporter = PDFExporter()
        pdf_buffer = exporter.create_pdf(analysis)
        
        st.download_button(
            label="📄 تحميل PDF",
            data=pdf_buffer,
            file_name=f"contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            mime="application/pdf"
        )
    except Exception as e:
        st.error(f"خطأ في تصدير PDF: {str(e)}")

def export_to_word(analysis):
    """Export analysis to Word document"""
    try:
        exporter = WordExporter()
        doc_buffer = exporter.create_document(analysis)
        
        st.download_button(
            label="📝 تحميل Word",
            data=doc_buffer,
            file_name=f"contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx",
            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
    except Exception as e:
        st.error(f"خطأ في تصدير Word: {str(e)}")

def export_to_json(analysis):
    """Export analysis to JSON"""
    try:
        json_str = json.dumps(analysis, ensure_ascii=False, indent=2)
        
        st.download_button(
            label="💾 تحميل JSON",
            data=json_str,
            file_name=f"contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
    except Exception as e:
        st.error(f"خطأ في تصدير JSON: {str(e)}")









def display_mobile_dashboard_page(current_user):
    """Display mobile dashboard page"""
    st.session_state.mobile_dashboard.display_mobile_dashboard(current_user)

def display_api_docs_page():
    """Display API documentation page"""
    st.markdown("### 🔌 واجهة برمجة التطبيقات (API)")

    st.markdown("""
    #### 📚 مقدمة عن API

    توفر منصة تحليل العقود القانونية الكويتية واجهة برمجة تطبيقات شاملة تتيح للمطورين دمج
    قدرات تحليل العقود في تطبيقاتهم الخاصة.

    **الميزات الرئيسية:**
    - 🔐 مصادقة آمنة باستخدام JWT
    - 📄 تحليل العقود النصية والملفات
    - 💬 نظام التعليقات والتعاون
    - 📊 التقارير والإحصائيات
    - 📚 مكتبة القوالب
    """)

    # API Server Status
    st.markdown("#### 🚀 حالة خادم API")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔄 فحص حالة API"):
            try:
                import requests
                response = requests.get("http://localhost:8000/api/v1/health", timeout=5)
                if response.status_code == 200:
                    st.success("✅ خادم API يعمل بشكل طبيعي")
                    health_data = response.json()
                    st.json(health_data)
                else:
                    st.error("❌ خادم API لا يستجيب")
            except Exception as e:
                st.error(f"❌ خطأ في الاتصال بخادم API: {str(e)}")

    with col2:
        if st.button("🚀 تشغيل خادم API"):
            st.info("💡 لتشغيل خادم API، استخدم الأمر التالي في Terminal:")
            st.code("python api_server.py", language="bash")

    # API Documentation
    st.markdown("#### 📖 وثائق API")

    st.markdown("""
    **عنوان API الأساسي:** `http://localhost:8000/api/v1`

    **المصادقة:** Bearer Token (JWT)

    **نقاط النهاية الرئيسية:**
    """)

    # Endpoints table
    endpoints_data = [
        ["POST", "/auth/login", "تسجيل الدخول والحصول على رمز الوصول"],
        ["POST", "/contracts/analyze", "تحليل نص العقد"],
        ["POST", "/contracts/upload", "رفع وتحليل ملف العقد"],
        ["GET", "/contracts", "قائمة العقود"],
        ["GET", "/contracts/{id}", "تفاصيل عقد محدد"],
        ["POST", "/contracts/{id}/comments", "إضافة تعليق"],
        ["GET", "/templates", "قائمة القوالب"],
        ["POST", "/templates/{id}/generate", "إنشاء عقد من قالب"],
        ["GET", "/reports/executive", "التقرير التنفيذي"],
        ["GET", "/health", "فحص حالة النظام"]
    ]

    import pandas as pd
    df = pd.DataFrame(endpoints_data, columns=["الطريقة", "المسار", "الوصف"])
    st.dataframe(df, use_container_width=True)

    # Example usage
    st.markdown("#### 💻 مثال على الاستخدام")

    st.markdown("**1. تسجيل الدخول:**")
    st.code("""
import requests

# تسجيل الدخول
login_data = {
    "username": "admin",
    "password": "admin123"
}

response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    json=login_data
)

token = response.json()["access_token"]
    """, language="python")

    st.markdown("**2. تحليل عقد:**")
    st.code("""
# تحليل عقد
headers = {"Authorization": f"Bearer {token}"}
contract_data = {
    "contract_text": "نص العقد هنا...",
    "title": "عقد عمل",
    "contract_type": "employment"
}

response = requests.post(
    "http://localhost:8000/api/v1/contracts/analyze",
    json=contract_data,
    headers=headers
)

analysis_result = response.json()
    """, language="python")

    # SDK Information
    st.markdown("#### 🛠️ SDK للمطورين")

    st.markdown("""
    يمكنك استخدام SDK المدمج لتسهيل التطوير:
    """)

    st.code("""
from api_client import ContractAnalysisSDK

# إنشاء SDK
sdk = ContractAnalysisSDK()

# تسجيل الدخول
sdk.login("admin", "admin123")

# تحليل عقد
result = sdk.analyze_contract(
    "نص العقد...",
    "عقد عمل",
    "employment"
)

print(f"درجة المخاطر: {result['data']['risk_score']}")
    """, language="python")



def display_system_config_page():
    """Display system configuration page"""
    st.markdown("### ⚙️ إعدادات النظام")

    config = st.session_state.config

    # Configuration validation
    st.markdown("#### 🔍 فحص التكوين")
    validation_result = config.validate_config()

    if validation_result["valid"]:
        st.success("✅ التكوين صحيح")
    else:
        st.error("❌ يوجد مشاكل في التكوين")
        for issue in validation_result["issues"]:
            st.error(f"• {issue}")

    if validation_result["warnings"]:
        st.warning("⚠️ تحذيرات:")
        for warning in validation_result["warnings"]:
            st.warning(f"• {warning}")

    # Environment info
    st.markdown("#### 🌍 معلومات البيئة")
    col1, col2 = st.columns(2)

    with col1:
        st.info(f"**البيئة:** {config.app.environment}")
        st.info(f"**وضع التطوير:** {'نعم' if config.app.debug else 'لا'}")
        st.info(f"**المضيف:** {config.app.host}:{config.app.port}")

    with col2:
        st.info(f"**قاعدة البيانات:** {config.database.host}:{config.database.port}")
        st.info(f"**Redis:** {config.redis.host}:{config.redis.port}")
        st.info(f"**AI Backend:** {config.ai.backend}")

    # Configuration editor
    st.markdown("#### ✏️ تحرير التكوين")

    with st.expander("إعدادات التطبيق"):
        new_environment = st.selectbox(
            "البيئة",
            options=["development", "staging", "production"],
            index=["development", "staging", "production"].index(config.app.environment)
        )

        new_debug = st.checkbox("وضع التطوير", value=config.app.debug)
        new_max_file_size = st.number_input(
            "الحد الأقصى لحجم الملف (MB)",
            min_value=1,
            max_value=1000,
            value=config.app.max_file_size_mb
        )

        if st.button("حفظ إعدادات التطبيق"):
            config.app.environment = new_environment
            config.app.debug = new_debug
            config.app.max_file_size_mb = new_max_file_size
            config.save_config()
            st.success("تم حفظ الإعدادات")
            st.rerun()

    with st.expander("إعدادات الأمان"):
        new_token_expire = st.number_input(
            "مدة انتهاء الرمز المميز (دقائق)",
            min_value=5,
            max_value=1440,
            value=config.security.access_token_expire_minutes
        )

        new_password_min_length = st.number_input(
            "الحد الأدنى لطول كلمة المرور",
            min_value=4,
            max_value=50,
            value=config.security.password_min_length
        )

        new_max_login_attempts = st.number_input(
            "الحد الأقصى لمحاولات تسجيل الدخول",
            min_value=1,
            max_value=20,
            value=config.security.max_login_attempts
        )

        if st.button("حفظ إعدادات الأمان"):
            config.security.access_token_expire_minutes = new_token_expire
            config.security.password_min_length = new_password_min_length
            config.security.max_login_attempts = new_max_login_attempts
            config.save_config()
            st.success("تم حفظ إعدادات الأمان")
            st.rerun()

    with st.expander("إعدادات الذكاء الاصطناعي"):
        col1, col2 = st.columns(2)

        with col1:
            new_ai_backend = st.selectbox(
                "نوع الخلفية",
                options=["lmstudio", "ollama", "openai"],
                index=["lmstudio", "ollama", "openai"].index(config.ai.backend),
                key="system_backend"
            )

            new_ai_base_url = st.text_input(
                "رابط الخدمة",
                value=config.ai.base_url,
                key="system_base_url"
            )

            new_ai_timeout = st.number_input(
                "مهلة الاستجابة (ثواني)",
                min_value=30,
                max_value=600,
                value=config.ai.timeout,
                key="system_timeout"
            )

        with col2:
            if st.button("🔍 جلب النماذج المتاحة", key="system_fetch_models"):
                with st.spinner("جاري جلب النماذج..."):
                    available_models = fetch_available_models(new_ai_backend, new_ai_base_url)
                    if available_models:
                        st.session_state.system_available_models = available_models
                        st.success(f"تم العثور على {len(available_models)} نموذج")
                        for model in available_models:
                            st.write(f"• {model}")
                    else:
                        st.error("فشل في جلب النماذج")

            # Model selection
            available_models = st.session_state.get('system_available_models', [])
            if not available_models:
                available_models = fetch_available_models(new_ai_backend, new_ai_base_url)

            if available_models:
                current_model_index = 0
                if config.ai.model_name in available_models:
                    current_model_index = available_models.index(config.ai.model_name)

                new_ai_model = st.selectbox(
                    "النموذج المختار",
                    options=available_models,
                    index=current_model_index,
                    key="system_model"
                )
            else:
                new_ai_model = st.text_input(
                    "اسم النموذج",
                    value=config.ai.model_name,
                    key="system_model_text"
                )

        if st.button("حفظ إعدادات الذكاء الاصطناعي", key="system_save_ai"):
            config.ai.backend = new_ai_backend
            config.ai.base_url = new_ai_base_url
            config.ai.model_name = new_ai_model
            config.ai.timeout = new_ai_timeout
            config.save_config()

            # Update analyzer
            st.session_state.analyzer = ContractAnalyzer(backend=new_ai_backend, model=new_ai_model)

            st.success("تم حفظ إعدادات الذكاء الاصطناعي")
            st.rerun()

def display_maintenance_page():
    """Display system maintenance page"""
    st.markdown("### 🔧 أدوات الصيانة")

    # System status
    st.markdown("#### 📊 حالة النظام")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔄 إعادة تشغيل التطبيق"):
            st.warning("⚠️ سيتم إعادة تشغيل التطبيق...")
            st.info("💡 استخدم: streamlit run app.py")

    with col2:
        if st.button("🗑️ مسح الذاكرة المؤقتة"):
            st.cache_data.clear()
            st.cache_resource.clear()
            st.success("✅ تم مسح الذاكرة المؤقتة")

    with col3:
        if st.button("📊 تحديث الإحصائيات"):
            st.success("✅ تم تحديث الإحصائيات")

    # Database maintenance
    st.markdown("#### 🗄️ صيانة قاعدة البيانات")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔧 تحسين قاعدة البيانات"):
            st.info("💡 تحسين قاعدة البيانات...")
            # Here you would implement database optimization
            st.success("✅ تم تحسين قاعدة البيانات")

    with col2:
        if st.button("💾 نسخ احتياطي"):
            st.info("💡 إنشاء نسخة احتياطية...")
            # Here you would implement backup functionality
            st.success("✅ تم إنشاء النسخة الاحتياطية")

    # File cleanup
    st.markdown("#### 📁 تنظيف الملفات")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🗑️ حذف الملفات المؤقتة"):
            st.info("💡 حذف الملفات المؤقتة...")
            # Here you would implement temp file cleanup
            st.success("✅ تم حذف الملفات المؤقتة")

    with col2:
        if st.button("📋 تنظيف السجلات القديمة"):
            st.info("💡 تنظيف السجلات القديمة...")
            # Here you would implement log cleanup
            st.success("✅ تم تنظيف السجلات القديمة")

    # System information
    st.markdown("#### ℹ️ معلومات النظام")

    import platform
    import sys

    system_info = {
        "نظام التشغيل": platform.system(),
        "إصدار النظام": platform.release(),
        "المعمارية": platform.machine(),
        "Python": sys.version.split()[0],
        "Streamlit": st.__version__
    }

    for key, value in system_info.items():
        st.info(f"**{key}:** {value}")

def display_system_logs_page():
    """Display system logs page"""
    st.markdown("### 📋 سجلات النظام")

    import os
    from pathlib import Path

    # Log files
    log_files = {
        "app.log": "سجل التطبيق",
        "security.log": "سجل الأمان",
        "performance.log": "سجل الأداء",
        "api.log": "سجل API"
    }

    # Log file selector
    selected_log = st.selectbox(
        "اختر ملف السجل",
        options=list(log_files.keys()),
        format_func=lambda x: log_files[x]
    )

    log_path = Path("logs") / selected_log

    if log_path.exists():
        # Log controls
        col1, col2, col3 = st.columns(3)

        with col1:
            lines_to_show = st.number_input(
                "عدد الأسطر",
                min_value=10,
                max_value=1000,
                value=100
            )

        with col2:
            if st.button("🔄 تحديث"):
                st.rerun()

        with col3:
            if st.button("📥 تحميل السجل"):
                with open(log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                st.download_button(
                    label="تحميل",
                    data=log_content,
                    file_name=selected_log,
                    mime="text/plain"
                )

        # Display log content
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-lines_to_show:]
                log_content = ''.join(recent_lines)

            st.text_area(
                f"محتوى {log_files[selected_log]}",
                log_content,
                height=500
            )

        except Exception as e:
            st.error(f"خطأ في قراءة ملف السجل: {e}")

    else:
        st.warning(f"ملف السجل غير موجود: {log_path}")

    # Log statistics
    st.markdown("#### 📊 إحصائيات السجلات")

    log_stats = {}
    for log_file in log_files.keys():
        log_path = Path("logs") / log_file
        if log_path.exists():
            stat = log_path.stat()
            log_stats[log_files[log_file]] = {
                "الحجم": f"{stat.st_size / 1024:.1f} KB",
                "آخر تعديل": datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
            }

    if log_stats:
        import pandas as pd
        df = pd.DataFrame(log_stats).T
        st.dataframe(df, use_container_width=True)

def display_security_dashboard_page(current_user):
    """Display advanced security dashboard"""
    st.markdown("### 🔒 لوحة الأمان المتقدم")

    security_manager = st.session_state.security_manager
    dashboard_data = security_manager.get_security_dashboard_data()

    # Security score
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        score = dashboard_data["security_score"]
        color = "green" if score >= 80 else "orange" if score >= 60 else "red"
        st.metric("نقاط الأمان", f"{score}/100", delta=None)
        st.markdown(f"<div style='color: {color}'>{'ممتاز' if score >= 80 else 'جيد' if score >= 60 else 'يحتاج تحسين'}</div>", unsafe_allow_html=True)

    with col2:
        st.metric("عناوين IP المحجوبة", dashboard_data["blocked_ips"])

    with col3:
        st.metric("الجلسات النشطة", dashboard_data["active_sessions"])

    with col4:
        recent_events_count = len(dashboard_data["recent_events"])
        st.metric("الأحداث الأمنية (24 ساعة)", recent_events_count)

    # Recent security events
    if dashboard_data["recent_events"]:
        st.markdown("#### 🚨 الأحداث الأمنية الأخيرة")

        events_df = pd.DataFrame(dashboard_data["recent_events"],
                               columns=["نوع الحدث", "الخطورة", "العدد"])
        st.dataframe(events_df, use_container_width=True)

    # Audit summary
    if dashboard_data["audit_summary"]:
        st.markdown("#### 📋 ملخص سجل التدقيق")

        audit_df = pd.DataFrame(dashboard_data["audit_summary"],
                              columns=["الإجراء", "العدد"])
        st.dataframe(audit_df, use_container_width=True)

    # Security controls
    if current_user["role"] == "admin":
        st.markdown("#### ⚙️ إعدادات الأمان")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 إعادة تعيين محاولات تسجيل الدخول الفاشلة"):
                # This would reset failed login attempts
                st.success("تم إعادة تعيين محاولات تسجيل الدخول الفاشلة")

        with col2:
            if st.button("📊 تصدير سجل الأمان"):
                # This would export security logs
                st.info("سيتم تصدير سجل الأمان...")

def display_business_intelligence_page(current_user):
    """Display business intelligence dashboard"""
    st.session_state.bi_dashboard.display_executive_dashboard()

def display_language_settings_page():
    """Display language settings page"""
    st.markdown("### 🌐 إعدادات اللغة")

    lang_manager = st.session_state.lang_manager
    localization_ui = st.session_state.localization_ui

    # Current language info
    current_lang = lang_manager.current_language
    available_languages = lang_manager.get_available_languages()

    st.info(f"**اللغة الحالية:** {available_languages.get(current_lang, current_lang)}")

    # Language selector
    st.markdown("#### اختيار اللغة")

    selected_lang = st.selectbox(
        "اختر اللغة",
        options=list(available_languages.keys()),
        format_func=lambda x: available_languages[x],
        index=list(available_languages.keys()).index(current_lang)
    )

    if selected_lang != current_lang:
        if st.button("تطبيق اللغة الجديدة"):
            lang_manager.set_language(selected_lang)
            st.success(f"تم تغيير اللغة إلى {available_languages[selected_lang]}")
            st.rerun()

    # RTL/LTR info
    st.markdown("#### معلومات الاتجاه")

    if lang_manager.is_rtl():
        st.info("🔄 اللغة الحالية تستخدم الكتابة من اليمين إلى اليسار (RTL)")
    else:
        st.info("🔄 اللغة الحالية تستخدم الكتابة من اليسار إلى اليمين (LTR)")

    # Translation status
    st.markdown("#### حالة الترجمة")

    translation_stats = {
        "العربية": "100% مكتملة",
        "English": "100% مكتملة"
    }

    for lang, status in translation_stats.items():
        st.markdown(f"• **{lang}:** {status}")

def display_predictive_analytics_page(current_user):
    """Display predictive analytics page"""
    st.markdown("### 🔮 التحليل التنبؤي")

    # Display predictive analytics from BI dashboard
    st.session_state.bi_dashboard.display_predictive_analytics()

    # Additional predictive features
    st.markdown("#### 📊 تحليلات إضافية")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("##### 🎯 توقعات المخاطر")

        # Mock risk prediction data
        risk_predictions = {
            "الأسبوع القادم": "متوسط (35%)",
            "الشهر القادم": "منخفض (28%)",
            "الربع القادم": "منخفض (25%)"
        }

        for period, prediction in risk_predictions.items():
            st.info(f"**{period}:** {prediction}")

    with col2:
        st.markdown("##### 📈 توقعات النمو")

        growth_predictions = {
            "نمو المستخدمين": "+15% شهرياً",
            "نمو العقود": "+22% شهرياً",
            "تحسن الامتثال": "+8% شهرياً"
        }

        for metric, prediction in growth_predictions.items():
            st.success(f"**{metric}:** {prediction}")

    # ROI Analysis
    st.markdown("#### 💰 تحليل العائد على الاستثمار")
    st.session_state.bi_dashboard.display_roi_analysis()

def display_contract_analysis_page(current_user):
    """Display contract analysis page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📄 تحليل عقد جديد</h1>
        <p class="dashboard-subtitle">قم برفع عقد لتحليله وفقاً للقانون الكويتي</p>
    </div>
    """, unsafe_allow_html=True)

    # Use the original contract analysis functionality
    display_analyze_page()

def display_database_page(current_user):
    """Display database page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📊 قاعدة بيانات العقود</h1>
        <p class="dashboard-subtitle">إدارة وعرض جميع العقود المحفوظة</p>
    </div>
    """, unsafe_allow_html=True)

    # Use the original database functionality
    st.session_state.contract_ui.display_contract_list()

def display_statistics_page(current_user):
    """Display statistics page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📈 الإحصائيات والتقارير</h1>
        <p class="dashboard-subtitle">تحليل شامل لبيانات العقود والأداء</p>
    </div>
    """, unsafe_allow_html=True)

    # Use the original BI dashboard
    st.session_state.bi_dashboard.display_executive_dashboard()

def display_templates_page(current_user):
    """Display templates page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📚 مكتبة القوالب</h1>
        <p class="dashboard-subtitle">قوالب العقود القانونية المعتمدة</p>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.template_ui.display_template_library(current_user)

def display_history_page(current_user):
    """Display contract analysis history page"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📜 تاريخ تحليل العقود</h1>
        <p class="dashboard-subtitle">تتبع جميع عمليات التحليل والتعديلات</p>
    </div>
    """, unsafe_allow_html=True)

    # Display user's analysis history dashboard
    st.session_state.contract_history_ui.display_user_history_dashboard(current_user['id'])

    # Option to view specific contract history
    st.markdown("---")
    st.markdown("### 🔍 تاريخ عقد محدد")

    # Get user's contracts for selection
    db = ContractDatabase()
    contracts = db.list_contracts(user_id=current_user["id"] if current_user["role"] != "admin" else None)

    if contracts:
        contract_options = {f"{c.get('filename', 'عقد غير معروف')} ({c.get('created_at', '')[:10]})": c['id'] for c in contracts}
        selected_contract_name = st.selectbox("اختر العقد لعرض تاريخه", [""] + list(contract_options.keys()))

        if selected_contract_name:
            contract_id = contract_options[selected_contract_name]
            st.session_state.contract_history_ui.display_contract_history(contract_id)
    else:
        st.info("لا توجد عقود متاحة لعرض تاريخها")

def display_collaboration_page(current_user):
    """Display collaboration page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">💬 التعاون والتعليقات</h1>
        <p class="dashboard-subtitle">تعاون مع الفريق وإضافة التعليقات</p>
    </div>
    """, unsafe_allow_html=True)

    # Contract selector for collaboration
    db = ContractDatabase()
    contracts = db.list_contracts(user_id=current_user["id"] if current_user["role"] != "admin" else None)

    if contracts:
        contract_options = {f"{c.get('filename', 'عقد غير معروف')} ({c.get('created_at', '')[:10]})": c['id'] for c in contracts}
        selected_contract_name = st.selectbox("اختر العقد للتعاون", list(contract_options.keys()))

        if selected_contract_name:
            contract_id = contract_options[selected_contract_name]
            st.session_state.collaboration_ui.display_comments_section(contract_id, current_user)
    else:
        st.info("لا توجد عقود متاحة للتعاون")

def display_advanced_reports_page(current_user):
    """Display advanced reports page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📊 التقارير المتقدمة</h1>
        <p class="dashboard-subtitle">تقارير تفصيلية ومتقدمة للعقود</p>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.reporting_ui.display_reports_dashboard(current_user)

def display_risk_analysis_page(current_user):
    """Display risk analysis page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">🎯 تحليل المخاطر</h1>
        <p class="dashboard-subtitle">تقييم وتحليل مخاطر العقود القانونية</p>
    </div>
    """, unsafe_allow_html=True)

    # Get a sample analysis or create a default one
    sample_analysis = {
        'risk_score': 7.5,
        'legal_points': [
            {'title': 'نقطة قانونية مهمة', 'priority': 'high'},
            {'title': 'بند يحتاج مراجعة', 'priority': 'medium'}
        ],
        'recommendations': [
            {'title': 'توصية للتحسين', 'urgency': 'important'}
        ]
    }

    st.session_state.risk_ui.display_risk_dashboard(sample_analysis)

def display_advanced_analysis_page(current_user):
    """Display advanced analysis page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">🤖 التحليل المتقدم</h1>
        <p class="dashboard-subtitle">تحليل متقدم بتقنيات الذكاء الاصطناعي</p>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.advanced_analysis_ui.display_advanced_analysis_page(current_user)

def display_mobile_page(current_user):
    """Display mobile page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📱 لوحة الجوال</h1>
        <p class="dashboard-subtitle">واجهة محسنة للأجهزة المحمولة</p>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.mobile_dashboard.display_mobile_dashboard(current_user)

def display_ai_insights_page(current_user):
    """Display AI insights page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">🤖 رؤى الذكاء الاصطناعي</h1>
        <p class="dashboard-subtitle">رؤى وتحليلات ذكية للعقود</p>
    </div>
    """, unsafe_allow_html=True)

    # Get contracts for AI insights
    db = ContractDatabase()
    contracts = db.list_contracts(user_id=current_user["id"] if current_user["role"] != "admin" else None)

    if not contracts:
        st.info("لا توجد عقود متاحة لتحليل الذكاء الاصطناعي")
        return

    # Contract selector
    contract_options = {f"{c.get('filename', 'عقد غير معروف')} ({c.get('created_at', '')[:10]})": c for c in contracts}
    selected_contract_name = st.selectbox("اختر العقد للتحليل", list(contract_options.keys()))

    if selected_contract_name:
        selected_contract = contract_options[selected_contract_name]

        # Display AI insights for selected contract
        st.markdown("### 📊 تحليل الذكاء الاصطناعي")

        # Mock analysis data for demonstration
        mock_analysis = {
            "risk_assessment": {
                "risk_level": "medium",
                "risk_score": 65,
                "identified_risks": [
                    {"risk": "unclear_termination", "severity": "high", "risk_name": "إجراءات الإنهاء غير واضحة", "description": "لا توجد إجراءات واضحة لإنهاء العقد"}
                ]
            },
            "compliance_check": {
                "overall_score": 78,
                "issues": [
                    {"severity": "medium", "issue": "missing_clause", "description": "بند القوة القاهرة مفقود"}
                ]
            },
            "specialized_insights": {
                "completeness_score": 82
            }
        }

        # Generate and display insights
        risk_insights = st.session_state.ai_insights.generate_risk_insights(mock_analysis)
        executive_summary = st.session_state.ai_insights.generate_executive_summary(mock_analysis)

        # Display executive summary
        st.markdown("#### 📋 الملخص التنفيذي")
        st.markdown(executive_summary)

        # Display risk insights
        st.markdown("#### 🎯 تحليل المخاطر")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("مستوى المخاطر", risk_insights["overall_risk_level"].title())

        with col2:
            st.metric("نقاط المخاطر", f"{risk_insights['risk_score']}/100")

        with col3:
            st.metric("المخاطر الحرجة", len(risk_insights["critical_risks"]))

        # Display critical risks
        if risk_insights["critical_risks"]:
            st.markdown("##### 🚨 المخاطر الحرجة")
            for risk in risk_insights["critical_risks"]:
                st.warning(f"**{risk['risk_name']}**: {risk['description']}")

        # Display mitigation strategies
        if risk_insights["mitigation_strategies"]:
            st.markdown("##### 🛡️ استراتيجيات التخفيف")
            for strategy in risk_insights["mitigation_strategies"]:
                st.info(f"**{strategy['strategy']}**: {strategy['action']}")

    # AI Performance Metrics
    st.markdown("### 📊 مؤشرات أداء الذكاء الاصطناعي")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("دقة التحليل", "94.5%", delta="2.1%")

    with col2:
        st.metric("سرعة المعالجة", "87.2%", delta="1.8%")

    with col3:
        st.metric("اكتشاف المخاطر", "91.8%", delta="3.2%")

    with col4:
        st.metric("الامتثال القانوني", "96.3%", delta="1.5%")

def display_monitoring_page(current_user):
    """Display monitoring page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">📊 مراقبة النظام</h1>
        <p class="dashboard-subtitle">مراقبة أداء النظام والأمان</p>
    </div>
    """, unsafe_allow_html=True)

    st.session_state.monitoring_dashboard.display_monitoring_dashboard()

def fetch_available_models(backend: str, base_url: str) -> list:
    """Fetch available models from AI backend"""
    try:
        if backend == "lmstudio":
            print(f"Fetching models from LM Studio: {base_url}/v1/models")
            response = requests.get(f"{base_url}/v1/models", timeout=5)
            print(f"LM Studio response status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                models = [model['id'] for model in data.get('data', [])]
                print(f"LM Studio models found: {models}")
                return models
        elif backend == "ollama":
            print(f"Fetching models from Ollama: {base_url}/api/tags")
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            print(f"Ollama response status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                print(f"Ollama models found: {models}")
                if not models:  # If no models in Ollama, return suggested models
                    return ["llama3.1:8b", "llama3.1:13b", "llama3:8b", "mistral:7b", "codellama:7b"]
                return models
    except Exception as e:
        print(f"Error fetching models from {backend}: {e}")

    # Return default models if fetching fails
    if backend == "ollama":
        return ["llama3.1:8b", "llama3.1:13b", "llama3:8b", "mistral:7b", "codellama:7b"]
    elif backend == "lmstudio":
        return ["llama-3.1-8b-instruct", "llama-3-8b-instruct", "mistral-7b-instruct"]
    else:
        return ["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"]

def display_settings_page(current_user):
    """Display settings page with modern styling"""
    st.markdown("""
    <div class="dashboard-header">
        <h1 class="dashboard-title">⚙️ الإعدادات</h1>
        <p class="dashboard-subtitle">إعدادات النظام والمستخدم</p>
    </div>
    """, unsafe_allow_html=True)

    # Settings tabs
    tab1, tab2, tab3, tab4 = st.tabs(["👤 إعدادات المستخدم", "🤖 الذكاء الاصطناعي", "🔒 الأمان", "⚙️ النظام"])

    with tab1:
        display_user_settings(current_user)

    with tab2:
        display_ai_settings()

    with tab3:
        display_security_settings(current_user)

    with tab4:
        if current_user["role"] == "admin":
            display_system_config_page()
        else:
            st.warning("🔒 هذا القسم متاح للمديرين فقط")

def display_user_settings(current_user):
    """Display user-specific settings"""
    st.markdown("### 👤 إعدادات المستخدم")

    # User profile section
    st.markdown("#### 📝 الملف الشخصي")

    col1, col2 = st.columns(2)

    with col1:
        new_username = st.text_input("اسم المستخدم", value=current_user.get("username", ""))
        new_email = st.text_input("البريد الإلكتروني", value=current_user.get("email", ""))

    with col2:
        new_full_name = st.text_input("الاسم الكامل", value=current_user.get("full_name", ""))
        new_department = st.text_input("القسم", value=current_user.get("department", ""))

    # Language and display preferences
    st.markdown("#### 🌐 تفضيلات العرض")

    col1, col2 = st.columns(2)

    with col1:
        language = st.selectbox(
            "اللغة",
            options=["العربية", "English"],
            index=0
        )

        theme = st.selectbox(
            "المظهر",
            options=["فاتح", "داكن", "تلقائي"],
            index=0
        )

    with col2:
        timezone = st.selectbox(
            "المنطقة الزمنية",
            options=["Asia/Kuwait", "UTC", "Asia/Riyadh"],
            index=0
        )

        date_format = st.selectbox(
            "تنسيق التاريخ",
            options=["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"],
            index=0
        )

    # Notification preferences
    st.markdown("#### 🔔 إعدادات التنبيهات")

    col1, col2 = st.columns(2)

    with col1:
        email_notifications = st.checkbox("تنبيهات البريد الإلكتروني", value=True)
        analysis_complete = st.checkbox("إشعار عند اكتمال التحليل", value=True)

    with col2:
        high_risk_alerts = st.checkbox("تنبيهات المخاطر العالية", value=True)
        weekly_reports = st.checkbox("التقارير الأسبوعية", value=False)

    if st.button("💾 حفظ إعدادات المستخدم", type="primary"):
        st.success("✅ تم حفظ إعدادات المستخدم بنجاح")

def display_ai_settings():
    """Display AI-specific settings"""
    st.markdown("### 🤖 إعدادات الذكاء الاصطناعي")

    # Get current config
    config = st.session_state.config

    # Backend selection
    st.markdown("#### 🔧 إعدادات الخلفية")

    # Show backend status
    st.markdown("##### 📡 حالة الخدمات")

    status_col1, status_col2, status_col3 = st.columns(3)

    with status_col1:
        # Test LM Studio
        try:
            import requests
            response = requests.get("http://localhost:1234/v1/models", timeout=2)
            if response.status_code == 200:
                st.success("🟢 LM Studio متصل")
            else:
                st.error("🔴 LM Studio غير متاح")
        except:
            st.error("🔴 LM Studio غير متاح")

    with status_col2:
        # Test Ollama
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=2)
            if response.status_code == 200:
                st.success("🟢 Ollama متصل")
            else:
                st.error("🔴 Ollama غير متاح")
        except:
            st.error("🔴 Ollama غير متاح")

    with status_col3:
        # Show help button
        if st.button("❓ مساعدة الإعداد"):
            st.info("""
            **لتشغيل LM Studio:**
            1. افتح تطبيق LM Studio
            2. اذهب إلى Local Server
            3. اختر نموذج واضغط Start Server
            4. تأكد من المنفذ 1234

            **لتشغيل Ollama:**
            1. قم بتثبيت Ollama من ollama.ai
            2. شغل الأمر: `ollama serve`
            3. لتحميل نموذج: `ollama pull llama3.1`
            """)

    col1, col2 = st.columns(2)

    with col1:
        current_backend = config.ai.backend
        new_backend = st.selectbox(
            "نوع الخلفية",
            options=["lmstudio", "ollama", "openai"],
            index=["lmstudio", "ollama", "openai"].index(current_backend),
            help="اختر نوع خدمة الذكاء الاصطناعي"
        )

        # Update base URL based on backend selection
        if new_backend == "lmstudio":
            default_url = "http://localhost:1234"
        elif new_backend == "ollama":
            default_url = "http://localhost:11434"
        else:
            default_url = "https://api.openai.com/v1"

        new_base_url = st.text_input(
            "رابط الخدمة",
            value=config.ai.base_url if config.ai.base_url else default_url,
            help="رابط خدمة الذكاء الاصطناعي"
        )

    with col2:
        # Test connection and get models
        if st.button("🔍 اختبار الاتصال وجلب النماذج"):
            with st.spinner("جاري اختبار الاتصال..."):
                available_models = fetch_available_models(new_backend, new_base_url)

                if available_models:
                    st.session_state.available_models = available_models
                    st.success(f"✅ تم الاتصال بنجاح! تم العثور على {len(available_models)} نموذج متاح")

                    # Display available models
                    with st.expander("النماذج المتاحة"):
                        for model in available_models:
                            st.write(f"• {model}")
                else:
                    st.error("❌ فشل في الاتصال أو لم يتم العثور على نماذج! تأكد من تشغيل الخدمة")

    # Model selection
    st.markdown("#### 🧠 اختيار النموذج")

    # Get available models from session state or fetch them
    available_models = st.session_state.get('available_models', [])

    if not available_models:
        # Try to fetch models automatically
        available_models = fetch_available_models(new_backend, new_base_url)
        if available_models:
            st.session_state.available_models = available_models

    col1, col2 = st.columns(2)

    with col1:
        current_model = config.ai.model_name
        if current_model in available_models:
            model_index = available_models.index(current_model)
        else:
            model_index = 0

        new_model = st.selectbox(
            "النموذج المختار",
            options=available_models,
            index=model_index,
            help="اختر النموذج المراد استخدامه للتحليل"
        )

    with col2:
        new_timeout = st.number_input(
            "مهلة الاستجابة (ثواني)",
            min_value=30,
            max_value=600,
            value=config.ai.timeout,
            help="الحد الأقصى لانتظار استجابة النموذج"
        )

    # Analysis preferences
    st.markdown("#### 🔍 تفضيلات التحليل")

    col1, col2 = st.columns(2)

    with col1:
        new_temperature = st.slider(
            "درجة الإبداع (Temperature)",
            min_value=0.0,
            max_value=1.0,
            value=config.ai.temperature,
            step=0.1,
            help="قيم أقل = إجابات أكثر دقة، قيم أعلى = إجابات أكثر إبداعاً"
        )

        new_max_tokens = st.number_input(
            "الحد الأقصى للرموز",
            min_value=500,
            max_value=8000,
            value=config.ai.max_tokens,
            help="الحد الأقصى لطول الاستجابة"
        )

    with col2:
        analysis_depth = st.selectbox(
            "عمق التحليل",
            options=["سريع", "متوسط", "مفصل"],
            index=1,
            help="مستوى التفصيل في التحليل"
        )

        auto_analysis = st.checkbox(
            "التحليل التلقائي عند الرفع",
            value=True,
            help="بدء التحليل تلقائياً عند رفع العقد"
        )

    # Performance settings
    st.markdown("#### ⚡ إعدادات الأداء")

    col1, col2 = st.columns(2)

    with col1:
        risk_sensitivity = st.selectbox(
            "حساسية كشف المخاطر",
            options=["منخفضة", "متوسطة", "عالية"],
            index=1,
            help="مستوى الحساسية في اكتشاف المخاطر القانونية"
        )

    with col2:
        include_suggestions = st.checkbox(
            "تضمين اقتراحات التحسين",
            value=True,
            help="إضافة توصيات لتحسين العقد"
        )

    # Save settings
    st.markdown("---")

    col1, col2, col3 = st.columns([1, 1, 1])

    with col2:
        if st.button("💾 حفظ إعدادات الذكاء الاصطناعي", type="primary", use_container_width=True):
            # Update config
            config.ai.backend = new_backend
            config.ai.base_url = new_base_url
            config.ai.model_name = new_model
            config.ai.timeout = new_timeout
            config.ai.temperature = new_temperature
            config.ai.max_tokens = new_max_tokens

            # Save config
            try:
                config.save_config()

                # Update the analyzer in session state
                st.session_state.analyzer = ContractAnalyzer(backend=new_backend, model=new_model)

                st.success("✅ تم حفظ إعدادات الذكاء الاصطناعي بنجاح!")
                st.info("🔄 سيتم تطبيق الإعدادات الجديدة في التحليل القادم")

                # Clear available models cache to refresh on next test
                if 'available_models' in st.session_state:
                    del st.session_state.available_models

                time.sleep(1)
                st.rerun()

            except Exception as e:
                st.error(f"❌ خطأ في حفظ الإعدادات: {str(e)}")

    # Current settings display
    st.markdown("#### 📊 الإعدادات الحالية")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.info(f"**الخلفية:** {config.ai.backend}")
        st.info(f"**النموذج:** {config.ai.model_name}")

    with col2:
        st.info(f"**المهلة الزمنية:** {config.ai.timeout}s")
        st.info(f"**درجة الإبداع:** {config.ai.temperature}")

    with col3:
        st.info(f"**الحد الأقصى للرموز:** {config.ai.max_tokens}")
        st.info(f"**الرابط:** {config.ai.base_url}")

def display_security_settings(current_user):
    """Display security settings"""
    st.markdown("### 🔒 إعدادات الأمان")

    # Password change
    st.markdown("#### 🔑 تغيير كلمة المرور")

    col1, col2 = st.columns(2)

    with col1:
        current_password = st.text_input("كلمة المرور الحالية", type="password")
        new_password = st.text_input("كلمة المرور الجديدة", type="password")

    with col2:
        confirm_password = st.text_input("تأكيد كلمة المرور الجديدة", type="password")

        if st.button("🔄 تغيير كلمة المرور"):
            if new_password == confirm_password:
                st.success("✅ تم تغيير كلمة المرور بنجاح")
            else:
                st.error("❌ كلمات المرور غير متطابقة")

    # Two-factor authentication
    st.markdown("#### 🛡️ المصادقة الثنائية")

    col1, col2 = st.columns(2)

    with col1:
        enable_2fa = st.checkbox("تفعيل المصادقة الثنائية", value=False)

        if enable_2fa:
            st.info("📱 سيتم إرسال رمز التحقق إلى هاتفك المحمول")

    with col2:
        if enable_2fa:
            phone_number = st.text_input("رقم الهاتف المحمول", placeholder="+965XXXXXXXX")

    # Session management
    st.markdown("#### 🕐 إدارة الجلسات")

    col1, col2 = st.columns(2)

    with col1:
        auto_logout = st.selectbox(
            "تسجيل الخروج التلقائي",
            options=["30 دقيقة", "1 ساعة", "4 ساعات", "8 ساعات", "لا يوجد"],
            index=1
        )

    with col2:
        if st.button("🚪 تسجيل الخروج من جميع الأجهزة"):
            st.warning("⚠️ سيتم تسجيل خروجك من جميع الأجهزة")

    # Activity log
    st.markdown("#### 📋 سجل النشاط")

    if st.button("📊 عرض سجل النشاط"):
        st.info("📅 آخر تسجيل دخول: اليوم 09:30 صباحاً")
        st.info("🌐 عنوان IP: *************")
        st.info("💻 المتصفح: Chrome 120.0")

    if st.button("💾 حفظ إعدادات الأمان", type="primary"):
        st.success("✅ تم حفظ إعدادات الأمان بنجاح")

if __name__ == "__main__":
    main()
