"""
Enhanced Risk Assessment System
Developed by MAXBIT LLC © 2025
"""

import json
from typing import Dict, List, Any, Tuple
from datetime import datetime
import streamlit as st

class RiskLevel:
    """Risk level definitions"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RiskCategory:
    """Risk category definitions"""
    LEGAL_COMPLIANCE = "legal_compliance"
    FINANCIAL = "financial"
    OPERATIONAL = "operational"
    REGULATORY = "regulatory"
    CONTRACTUAL = "contractual"

class RiskAssessment:
    """Enhanced risk assessment system"""
    
    def __init__(self):
        self.risk_weights = {
            RiskLevel.LOW: 1,
            RiskLevel.MEDIUM: 3,
            RiskLevel.HIGH: 7,
            RiskLevel.CRITICAL: 10
        }
        
        self.category_weights = {
            RiskCategory.LEGAL_COMPLIANCE: 1.5,
            RiskCategory.FINANCIAL: 1.3,
            RiskCategory.REGULATORY: 1.4,
            RiskCategory.CONTRACTUAL: 1.2,
            RiskCategory.OPERATIONAL: 1.0
        }
    
    def calculate_comprehensive_risk_score(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive risk score with detailed breakdown"""
        
        legal_points = analysis.get('legal_points', [])
        recommendations = analysis.get('recommendations', [])
        
        # Initialize risk assessment
        risk_assessment = {
            "overall_score": 0.0,
            "risk_level": RiskLevel.LOW,
            "category_scores": {},
            "risk_factors": [],
            "mitigation_suggestions": [],
            "compliance_issues": [],
            "financial_risks": [],
            "detailed_breakdown": {}
        }
        
        if not legal_points:
            return risk_assessment
        
        # Analyze legal points for risk factors
        category_risks = {category: [] for category in self.category_weights.keys()}
        
        for point in legal_points:
            risk_factor = self._analyze_legal_point_risk(point)
            if risk_factor:
                category = risk_factor["category"]
                category_risks[category].append(risk_factor)
                risk_assessment["risk_factors"].append(risk_factor)
        
        # Calculate category scores
        total_weighted_score = 0
        total_weight = 0
        
        for category, risks in category_risks.items():
            if risks:
                category_score = self._calculate_category_score(risks)
                weight = self.category_weights[category]
                
                risk_assessment["category_scores"][category] = {
                    "score": category_score,
                    "risk_count": len(risks),
                    "weight": weight
                }
                
                total_weighted_score += category_score * weight
                total_weight += weight
        
        # Calculate overall score
        if total_weight > 0:
            risk_assessment["overall_score"] = min(total_weighted_score / total_weight, 10.0)
        
        # Determine risk level
        risk_assessment["risk_level"] = self._determine_risk_level(risk_assessment["overall_score"])
        
        # Generate mitigation suggestions
        risk_assessment["mitigation_suggestions"] = self._generate_mitigation_suggestions(
            risk_assessment["risk_factors"]
        )
        
        # Categorize specific risks
        risk_assessment["compliance_issues"] = [
            r for r in risk_assessment["risk_factors"] 
            if r["category"] == RiskCategory.LEGAL_COMPLIANCE
        ]
        
        risk_assessment["financial_risks"] = [
            r for r in risk_assessment["risk_factors"] 
            if r["category"] == RiskCategory.FINANCIAL
        ]
        
        # Add detailed breakdown
        risk_assessment["detailed_breakdown"] = self._create_detailed_breakdown(risk_assessment)
        
        return risk_assessment
    
    def _analyze_legal_point_risk(self, legal_point: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze individual legal point for risk factors"""
        title = legal_point.get('title', '').lower()
        description = legal_point.get('description', '').lower()
        priority = legal_point.get('priority', 'low')
        
        # Risk keywords mapping
        risk_keywords = {
            RiskCategory.LEGAL_COMPLIANCE: [
                'قانون', 'مخالفة', 'غير قانوني', 'انتهاك', 'مخالف للقانون',
                'غير متوافق', 'قانوني', 'التزام', 'امتثال'
            ],
            RiskCategory.FINANCIAL: [
                'مالي', 'تكلفة', 'غرامة', 'تعويض', 'خسارة', 'ضرر مالي',
                'دفع', 'مبلغ', 'رسوم', 'فوائد'
            ],
            RiskCategory.REGULATORY: [
                'تنظيمي', 'هيئة', 'ترخيص', 'موافقة', 'تصريح', 'رقابة',
                'معايير', 'لوائح', 'تنظيم'
            ],
            RiskCategory.CONTRACTUAL: [
                'عقد', 'شرط', 'التزام', 'إنهاء', 'فسخ', 'تجديد',
                'مدة', 'انتهاء', 'تعديل'
            ],
            RiskCategory.OPERATIONAL: [
                'تشغيلي', 'عملية', 'إجراء', 'تنفيذ', 'أداء', 'خدمة',
                'عمل', 'مهمة', 'نشاط'
            ]
        }
        
        # Determine category
        category = RiskCategory.CONTRACTUAL  # Default
        max_matches = 0
        
        for cat, keywords in risk_keywords.items():
            matches = sum(1 for keyword in keywords if keyword in title or keyword in description)
            if matches > max_matches:
                max_matches = matches
                category = cat
        
        # Determine risk level based on priority and content
        risk_level = self._map_priority_to_risk_level(priority)
        
        # Enhance risk level based on content analysis
        if any(keyword in title or keyword in description for keyword in 
               ['خطر', 'مخاطر', 'خطير', 'حرج', 'عاجل', 'فوري']):
            risk_level = self._escalate_risk_level(risk_level)
        
        return {
            "title": legal_point.get('title', ''),
            "description": legal_point.get('description', ''),
            "category": category,
            "risk_level": risk_level,
            "priority": priority,
            "score": self.risk_weights[risk_level],
            "law_reference": legal_point.get('law_reference', '')
        }
    
    def _calculate_category_score(self, risks: List[Dict[str, Any]]) -> float:
        """Calculate score for a risk category"""
        if not risks:
            return 0.0
        
        total_score = sum(risk["score"] for risk in risks)
        return min(total_score / len(risks), 10.0)
    
    def _determine_risk_level(self, score: float) -> str:
        """Determine overall risk level from score"""
        if score >= 8.0:
            return RiskLevel.CRITICAL
        elif score >= 6.0:
            return RiskLevel.HIGH
        elif score >= 3.0:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW
    
    def _map_priority_to_risk_level(self, priority: str) -> str:
        """Map priority to risk level"""
        mapping = {
            'high': RiskLevel.HIGH,
            'medium': RiskLevel.MEDIUM,
            'low': RiskLevel.LOW
        }
        return mapping.get(priority, RiskLevel.LOW)
    
    def _escalate_risk_level(self, current_level: str) -> str:
        """Escalate risk level by one step"""
        escalation = {
            RiskLevel.LOW: RiskLevel.MEDIUM,
            RiskLevel.MEDIUM: RiskLevel.HIGH,
            RiskLevel.HIGH: RiskLevel.CRITICAL,
            RiskLevel.CRITICAL: RiskLevel.CRITICAL
        }
        return escalation.get(current_level, current_level)
    
    def _generate_mitigation_suggestions(self, risk_factors: List[Dict[str, Any]]) -> List[str]:
        """Generate mitigation suggestions based on risk factors"""
        suggestions = []
        
        # Group by category
        category_risks = {}
        for risk in risk_factors:
            category = risk["category"]
            if category not in category_risks:
                category_risks[category] = []
            category_risks[category].append(risk)
        
        # Generate category-specific suggestions
        for category, risks in category_risks.items():
            if category == RiskCategory.LEGAL_COMPLIANCE:
                suggestions.append("مراجعة العقد مع مستشار قانوني متخصص في القانون الكويتي")
                suggestions.append("التأكد من توافق جميع البنود مع القوانين المحلية")
            
            elif category == RiskCategory.FINANCIAL:
                suggestions.append("إجراء تقييم مالي شامل للمخاطر المحتملة")
                suggestions.append("وضع ضمانات مالية مناسبة")
            
            elif category == RiskCategory.REGULATORY:
                suggestions.append("التحقق من جميع التراخيص والموافقات المطلوبة")
                suggestions.append("مراجعة اللوائح التنظيمية ذات الصلة")
            
            elif category == RiskCategory.CONTRACTUAL:
                suggestions.append("إعادة صياغة البنود الغامضة أو المبهمة")
                suggestions.append("إضافة شروط حماية إضافية")
            
            elif category == RiskCategory.OPERATIONAL:
                suggestions.append("وضع إجراءات تشغيلية واضحة")
                suggestions.append("تحديد المسؤوليات والصلاحيات بوضوح")
        
        return list(set(suggestions))  # Remove duplicates
    
    def _create_detailed_breakdown(self, risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Create detailed risk breakdown"""
        return {
            "total_risk_factors": len(risk_assessment["risk_factors"]),
            "critical_risks": len([r for r in risk_assessment["risk_factors"] if r["risk_level"] == RiskLevel.CRITICAL]),
            "high_risks": len([r for r in risk_assessment["risk_factors"] if r["risk_level"] == RiskLevel.HIGH]),
            "medium_risks": len([r for r in risk_assessment["risk_factors"] if r["risk_level"] == RiskLevel.MEDIUM]),
            "low_risks": len([r for r in risk_assessment["risk_factors"] if r["risk_level"] == RiskLevel.LOW]),
            "most_critical_category": self._get_most_critical_category(risk_assessment["category_scores"]),
            "assessment_date": datetime.now().isoformat()
        }
    
    def _get_most_critical_category(self, category_scores: Dict[str, Any]) -> str:
        """Get the most critical risk category"""
        if not category_scores:
            return "none"
        
        max_score = 0
        critical_category = "none"
        
        for category, data in category_scores.items():
            if data["score"] > max_score:
                max_score = data["score"]
                critical_category = category
        
        return critical_category

class RiskUI:
    """Risk assessment UI components"""
    
    def __init__(self):
        self.risk_assessor = RiskAssessment()
    
    def display_risk_dashboard(self, analysis: Dict[str, Any]):
        """Display comprehensive risk dashboard"""
        risk_data = self.risk_assessor.calculate_comprehensive_risk_score(analysis)
        
        st.markdown("### 🎯 تقييم المخاطر الشامل")
        
        # Overall risk score
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            score = risk_data["overall_score"]
            risk_level = risk_data["risk_level"]
            
            # Color coding
            color = self._get_risk_color(risk_level)
            
            st.markdown(f"""
            <div style="background: {color}; padding: 20px; border-radius: 10px; text-align: center; color: white;">
                <h2 style="margin: 0; color: white;">درجة المخاطر الإجمالية</h2>
                <h1 style="margin: 10px 0; color: white;">{score:.1f}/10</h1>
                <h3 style="margin: 0; color: white;">{self._get_risk_level_name(risk_level)}</h3>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.metric("عوامل المخاطر", risk_data["detailed_breakdown"]["total_risk_factors"])
        
        with col3:
            critical_count = risk_data["detailed_breakdown"]["critical_risks"]
            st.metric("مخاطر حرجة", critical_count, delta=None if critical_count == 0 else "تحتاج انتباه")
        
        # Category breakdown
        if risk_data["category_scores"]:
            st.markdown("#### 📊 تفصيل المخاطر حسب الفئة")
            
            for category, data in risk_data["category_scores"].items():
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**{self._get_category_name(category)}**")
                    progress_value = data["score"] / 10.0
                    st.progress(progress_value)
                
                with col2:
                    st.metric("", f"{data['score']:.1f}/10")
        
        # Risk factors
        if risk_data["risk_factors"]:
            st.markdown("#### ⚠️ عوامل المخاطر المحددة")
            
            for risk in risk_data["risk_factors"]:
                risk_color = self._get_risk_color(risk["risk_level"])
                
                with st.expander(f"🔸 {risk['title']} - {self._get_risk_level_name(risk['risk_level'])}"):
                    st.markdown(f"**الوصف:** {risk['description']}")
                    st.markdown(f"**الفئة:** {self._get_category_name(risk['category'])}")
                    st.markdown(f"**المرجع القانوني:** {risk.get('law_reference', 'غير محدد')}")
        
        # Mitigation suggestions
        if risk_data["mitigation_suggestions"]:
            st.markdown("#### 💡 اقتراحات التخفيف من المخاطر")
            
            for i, suggestion in enumerate(risk_data["mitigation_suggestions"], 1):
                st.markdown(f"{i}. {suggestion}")
    
    def _get_risk_color(self, risk_level: str) -> str:
        """Get color for risk level"""
        colors = {
            RiskLevel.LOW: "#27ae60",
            RiskLevel.MEDIUM: "#f39c12",
            RiskLevel.HIGH: "#e74c3c",
            RiskLevel.CRITICAL: "#8e44ad"
        }
        return colors.get(risk_level, "#95a5a6")
    
    def _get_risk_level_name(self, risk_level: str) -> str:
        """Get Arabic risk level name"""
        names = {
            RiskLevel.LOW: "منخفض",
            RiskLevel.MEDIUM: "متوسط",
            RiskLevel.HIGH: "عالي",
            RiskLevel.CRITICAL: "حرج"
        }
        return names.get(risk_level, risk_level)
    
    def _get_category_name(self, category: str) -> str:
        """Get Arabic category name"""
        names = {
            RiskCategory.LEGAL_COMPLIANCE: "الامتثال القانوني",
            RiskCategory.FINANCIAL: "المخاطر المالية",
            RiskCategory.REGULATORY: "المخاطر التنظيمية",
            RiskCategory.CONTRACTUAL: "المخاطر التعاقدية",
            RiskCategory.OPERATIONAL: "المخاطر التشغيلية"
        }
        return names.get(category, category)
