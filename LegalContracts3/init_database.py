#!/usr/bin/env python3
"""
Database Initialization and Migration Script
Developed by MAXBIT LLC © 2025
"""

import sqlite3
import json
import os
from datetime import datetime
from pathlib import Path
import uuid

def create_sqlite_database(db_path: str = "contracts.db"):
    """Create SQLite database with all required tables"""
    print(f"🔧 Creating SQLite database: {db_path}")
    
    with sqlite3.connect(db_path) as conn:
        # Enable foreign keys
        conn.execute("PRAGMA foreign_keys = ON")
        
        # Users table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'client',
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_by TEXT
            )
        """)
        
        # Contracts table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS contracts (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                original_text TEXT NOT NULL,
                analysis_result TEXT,
                contract_type TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                user_id TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                risk_score REAL,
                tags TEXT,
                notes TEXT,
                version INTEGER DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        
        # Comments table (for collaboration)
        conn.execute("""
            CREATE TABLE IF NOT EXISTS comments (
                id TEXT PRIMARY KEY,
                contract_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                content TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                parent_id TEXT,
                FOREIGN KEY (contract_id) REFERENCES contracts (id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (parent_id) REFERENCES comments (id)
            )
        """)

        # Contract history table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS contract_history (
                id TEXT PRIMARY KEY,
                contract_id TEXT NOT NULL,
                version INTEGER NOT NULL,
                filename TEXT NOT NULL,
                original_text TEXT NOT NULL,
                analysis_result TEXT,
                contract_type TEXT,
                status TEXT,
                risk_score REAL,
                user_id TEXT NOT NULL,
                created_at TEXT NOT NULL,
                action_type TEXT NOT NULL,
                action_description TEXT,
                FOREIGN KEY (contract_id) REFERENCES contracts (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Contract templates table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS contract_templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                template_type TEXT NOT NULL,
                content TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_by TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                usage_count INTEGER DEFAULT 0,
                tags TEXT,
                file_path TEXT,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        
        # Templates table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                template_type TEXT NOT NULL,
                content TEXT NOT NULL,
                variables TEXT,
                created_by TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        
        # Create indexes for better performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_contracts_user_id ON contracts (user_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_contracts_created_at ON contracts (created_at)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_contracts_type ON contracts (contract_type)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts (status)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_comments_contract_id ON comments (contract_id)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments (user_id)")
        
        conn.commit()
    
    print("✅ SQLite database created successfully")

def migrate_json_to_sqlite(json_file: str = "contracts.json", db_path: str = "contracts.db"):
    """Migrate data from JSON file to SQLite database"""
    if not os.path.exists(json_file):
        print(f"📝 No JSON file found at {json_file}, skipping migration")
        return
    
    print(f"🔄 Migrating data from {json_file} to {db_path}")
    
    # Load JSON data
    with open(json_file, 'r', encoding='utf-8') as f:
        contracts_data = json.load(f)
    
    if not contracts_data:
        print("📝 No contracts found in JSON file")
        return
    
    with sqlite3.connect(db_path) as conn:
        migrated_count = 0
        
        for contract_id, contract in contracts_data.items():
            try:
                # Check if contract already exists
                existing = conn.execute(
                    "SELECT id FROM contracts WHERE id = ?", 
                    (contract_id,)
                ).fetchone()
                
                if existing:
                    print(f"⏭️  Contract {contract_id[:8]} already exists, skipping")
                    continue
                
                # Insert contract
                conn.execute("""
                    INSERT INTO contracts (
                        id, filename, original_text, analysis_result, 
                        contract_type, status, user_id, created_at, 
                        updated_at, risk_score, tags, notes, version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    contract.get('id', contract_id),
                    contract.get('filename', 'Unknown'),
                    contract.get('original_text', ''),
                    json.dumps(contract.get('analysis', {})),
                    contract.get('contract_type', 'other'),
                    contract.get('status', 'analyzed'),
                    contract.get('created_by', 'admin'),
                    contract.get('created_at', datetime.now().isoformat()),
                    contract.get('updated_at', datetime.now().isoformat()),
                    contract.get('risk_score', 0),
                    json.dumps(contract.get('tags', [])),
                    contract.get('notes', ''),
                    contract.get('version', 1)
                ))
                
                migrated_count += 1
                
            except Exception as e:
                print(f"❌ Error migrating contract {contract_id[:8]}: {e}")
        
        conn.commit()
        print(f"✅ Migrated {migrated_count} contracts successfully")

def create_default_admin_user(db_path: str = "contracts.db"):
    """Create default admin user if none exists"""
    print("👤 Checking for admin user...")
    
    with sqlite3.connect(db_path) as conn:
        # Check if admin user exists
        admin_exists = conn.execute(
            "SELECT id FROM users WHERE username = 'admin'"
        ).fetchone()
        
        if admin_exists:
            print("✅ Admin user already exists")
            return
        
        # Create admin user
        admin_id = str(uuid.uuid4())
        # Simple hash for demo - in production use proper password hashing
        password_hash = "admin123"  # This should be properly hashed
        
        conn.execute("""
            INSERT INTO users (
                id, username, email, full_name, password_hash, 
                role, created_at, updated_at, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            admin_id,
            "admin",
            "<EMAIL>",
            "System Administrator",
            password_hash,
            "admin",
            datetime.now().isoformat(),
            datetime.now().isoformat(),
            True
        ))
        
        conn.commit()
        print("✅ Default admin user created (username: admin, password: admin123)")

def add_sample_data(db_path: str = "contracts.db"):
    """Add sample data for testing"""
    print("📊 Adding sample data...")
    
    with sqlite3.connect(db_path) as conn:
        # Check if we already have contracts
        contract_count = conn.execute("SELECT COUNT(*) FROM contracts").fetchone()[0]
        
        if contract_count > 0:
            print(f"✅ Database already has {contract_count} contracts")
            return
        
        # Get admin user ID
        admin_user = conn.execute(
            "SELECT id FROM users WHERE username = 'admin'"
        ).fetchone()
        
        if not admin_user:
            print("❌ No admin user found, cannot add sample data")
            return
        
        admin_id = admin_user[0]
        
        # Sample contracts
        sample_contracts = [
            {
                "filename": "عقد_عمل_نموذجي.pdf",
                "contract_type": "employment",
                "risk_score": 25.5,
                "original_text": "هذا عقد عمل نموذجي بين الشركة والموظف..."
            },
            {
                "filename": "اتفاقية_تجارية.docx", 
                "contract_type": "commercial",
                "risk_score": 45.2,
                "original_text": "اتفاقية تجارية بين الطرفين لتوريد البضائع..."
            },
            {
                "filename": "عقد_إيجار_عقاري.pdf",
                "contract_type": "real_estate", 
                "risk_score": 15.8,
                "original_text": "عقد إيجار عقار سكني لمدة سنة واحدة..."
            },
            {
                "filename": "عقد_خدمات_قانونية.doc",
                "contract_type": "service",
                "risk_score": 35.7,
                "original_text": "عقد تقديم خدمات قانونية واستشارية..."
            },
            {
                "filename": "اتفاقية_شراكة.pdf",
                "contract_type": "partnership",
                "risk_score": 55.3,
                "original_text": "اتفاقية شراكة تجارية بين الشركاء..."
            }
        ]
        
        for i, contract_data in enumerate(sample_contracts):
            contract_id = str(uuid.uuid4())
            
            # Create sample analysis result
            analysis_result = {
                "risk_score": contract_data["risk_score"],
                "legal_points": [
                    {"title": "نقطة قانونية مهمة", "priority": "high"},
                    {"title": "بند يحتاج مراجعة", "priority": "medium"}
                ],
                "recommendations": [
                    {"title": "توصية للتحسين", "urgency": "important"}
                ]
            }
            
            conn.execute("""
                INSERT INTO contracts (
                    id, filename, original_text, analysis_result,
                    contract_type, status, user_id, created_at,
                    updated_at, risk_score, tags, notes, version
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                contract_id,
                contract_data["filename"],
                contract_data["original_text"],
                json.dumps(analysis_result),
                contract_data["contract_type"],
                "analyzed",
                admin_id,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                contract_data["risk_score"],
                json.dumps(["نموذج", "اختبار"]),
                "عقد نموذجي للاختبار",
                1
            ))
        
        # Add Kuwaiti contract templates
        kuwaiti_templates = [
            {
                "name": "عقد عمل كويتي - موظف بدوام كامل",
                "description": "نموذج عقد عمل للموظفين بدوام كامل وفقاً لقانون العمل الكويتي",
                "template_type": "employment",
                "content": """عقد عمل

الطرف الأول: _______________________ (صاحب العمل)
الطرف الثاني: _______________________ (الموظف)

المادة الأولى: طبيعة العمل
يتعهد الطرف الثاني بالعمل لدى الطرف الأول في وظيفة _______________________

المادة الثانية: الراتب والمزايا
- الراتب الأساسي: _______________________ دينار كويتي شهرياً
- البدلات: _______________________
- المزايا الإضافية: _______________________

المادة الثالثة: ساعات العمل
ساعات العمل اليومية: _______ ساعات (وفقاً لقانون العمل الكويتي)
أيام العمل الأسبوعية: _______ أيام

المادة الرابعة: الإجازات
- الإجازة السنوية: 30 يوماً مدفوعة الأجر
- الإجازة المرضية: وفقاً لقانون العمل الكويتي
- إجازة الأمومة: 70 يوماً مدفوعة الأجر

المادة الخامسة: إنهاء العقد
يمكن إنهاء هذا العقد بإشعار مسبق مدته _______ يوماً

التوقيع:
الطرف الأول: _______________________
الطرف الثاني: _______________________
التاريخ: _______________________""",
                "tags": "عمل,موظف,دوام_كامل,كويت"
            },
            {
                "name": "عقد إيجار عقاري - سكني",
                "description": "نموذج عقد إيجار للعقارات السكنية في دولة الكويت",
                "template_type": "real_estate",
                "content": """عقد إيجار عقار سكني

المؤجر: _______________________
المستأجر: _______________________

المادة الأولى: العقار المؤجر
العنوان: _______________________
المساحة: _______ متر مربع
الوصف: _______________________

المادة الثانية: مدة الإيجار
مدة الإيجار: _______ سنة/سنوات
تاريخ البداية: _______________________
تاريخ الانتهاء: _______________________

المادة الثالثة: قيمة الإيجار
- الإيجار السنوي: _______ دينار كويتي
- طريقة الدفع: _______________________
- تاريخ الاستحقاق: _______________________

المادة الرابعة: التأمين
مبلغ التأمين: _______ دينار كويتي (قابل للاسترداد)

المادة الخامسة: الالتزامات
التزامات المؤجر: _______________________
التزامات المستأجر: _______________________

التوقيع:
المؤجر: _______________________
المستأجر: _______________________
التاريخ: _______________________""",
                "tags": "إيجار,عقار,سكني,كويت"
            }
        ]

        for template in kuwaiti_templates:
            template_id = str(uuid.uuid4())
            conn.execute("""
                INSERT INTO contract_templates (
                    id, name, description, template_type, content,
                    is_active, created_by, created_at, updated_at,
                    usage_count, tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                template_id,
                template["name"],
                template["description"],
                template["template_type"],
                template["content"],
                1,  # is_active
                admin_id,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                0,  # usage_count
                template["tags"]
            ))

        conn.commit()
        print(f"✅ Added {len(sample_contracts)} sample contracts")
        print(f"✅ Added {len(kuwaiti_templates)} Kuwaiti contract templates")

def initialize_database():
    """Complete database initialization"""
    print("🚀 Starting database initialization...")
    
    # Create SQLite database
    create_sqlite_database()
    
    # Migrate existing JSON data
    migrate_json_to_sqlite()
    
    # Create default admin user
    create_default_admin_user()
    
    # Add sample data
    add_sample_data()
    
    print("🎉 Database initialization complete!")
    print("\n📋 Summary:")
    print("  ✅ SQLite database created")
    print("  ✅ JSON data migrated (if available)")
    print("  ✅ Default admin user created")
    print("  ✅ Sample data added")
    print("\n🔑 Login credentials:")
    print("  Username: admin")
    print("  Password: admin123")

if __name__ == "__main__":
    initialize_database()
