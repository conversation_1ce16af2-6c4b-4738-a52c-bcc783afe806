#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legal Clauses Library Manager
Manages pre-built legal clauses with risk assessments
"""

import json
import logging
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class ClauseLibraryManager:
    """Manages legal clauses library with risk assessments"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self._initialize_default_clauses()
    
    def _initialize_default_clauses(self):
        """Initialize default legal clauses for Kuwait and Saudi systems"""
        
        # Kuwait legal clauses
        kuwait_clauses = [
            {
                'title': 'بند المسؤولية المحدودة',
                'content': 'تقتصر مسؤولية الطرف الأول على الأضرار المباشرة فقط، ولا يتحمل أي مسؤولية عن الأضرار غير المباشرة أو التبعية أو الخاصة.',
                'category': 'المسؤولية',
                'subcategory': 'تحديد المسؤولية',
                'legal_system': 'kuwait',
                'risk_level': 'medium',
                'risk_score': 60,
                'description': 'بند يحدد نطاق المسؤولية القانونية للأطراف',
                'usage_notes': 'يُستخدم في العقود التجارية لتحديد نطاق المسؤولية',
                'tags': 'مسؤولية,تحديد,أضرار,تجاري'
            },
            {
                'title': 'شرط القوة القاهرة',
                'content': 'لا يُعتبر أي من الطرفين مخلاً بالتزاماته إذا كان عدم الوفاء ناتجاً عن قوة قاهرة أو ظروف خارجة عن إرادته.',
                'category': 'القوة القاهرة',
                'subcategory': 'إعفاء من المسؤولية',
                'legal_system': 'kuwait',
                'risk_level': 'low',
                'risk_score': 30,
                'description': 'شرط يعفي الأطراف من المسؤولية في حالات القوة القاهرة',
                'usage_notes': 'ضروري في جميع العقود طويلة المدى',
                'tags': 'قوة قاهرة,إعفاء,ظروف استثنائية'
            },
            {
                'title': 'بند السرية والكتمان',
                'content': 'يتعهد كل طرف بالحفاظ على سرية جميع المعلومات التي يحصل عليها من الطرف الآخر وعدم إفشائها لأي طرف ثالث.',
                'category': 'السرية',
                'subcategory': 'حماية المعلومات',
                'legal_system': 'kuwait',
                'risk_level': 'high',
                'risk_score': 80,
                'description': 'بند يضمن حماية المعلومات السرية',
                'usage_notes': 'أساسي في عقود الخدمات والشراكات',
                'tags': 'سرية,كتمان,معلومات,حماية'
            },
            {
                'title': 'شروط الدفع والتحصيل',
                'content': 'يتم الدفع خلال 30 يوماً من تاريخ الفاتورة، وفي حالة التأخير يحق للدائن فرض فوائد تأخير بنسبة 1% شهرياً.',
                'category': 'المالية',
                'subcategory': 'شروط الدفع',
                'legal_system': 'kuwait',
                'risk_level': 'medium',
                'risk_score': 50,
                'description': 'تحديد شروط وآليات الدفع',
                'usage_notes': 'يجب مراجعة نسب الفوائد وفقاً للقانون الكويتي',
                'tags': 'دفع,فوائد,تأخير,مالي'
            },
            {
                'title': 'بند إنهاء العقد',
                'content': 'يحق لأي من الطرفين إنهاء هذا العقد بإشعار كتابي مدته 30 يوماً، مع الوفاء بجميع الالتزامات المستحقة.',
                'category': 'الإنهاء',
                'subcategory': 'إنهاء العقد',
                'legal_system': 'kuwait',
                'risk_level': 'medium',
                'risk_score': 55,
                'description': 'تحديد شروط وإجراءات إنهاء العقد',
                'usage_notes': 'يجب تحديد مدة الإشعار بوضوح',
                'tags': 'إنهاء,إشعار,التزامات'
            }
        ]
        
        # Saudi legal clauses
        saudi_clauses = [
            {
                'title': 'بند الامتثال للأنظمة السعودية',
                'content': 'يلتزم جميع الأطراف بالامتثال الكامل للأنظمة واللوائح المعمول بها في المملكة العربية السعودية.',
                'category': 'الامتثال',
                'subcategory': 'الامتثال التنظيمي',
                'legal_system': 'saudi',
                'risk_level': 'high',
                'risk_score': 85,
                'description': 'ضمان الامتثال للأنظمة السعودية',
                'usage_notes': 'إلزامي في جميع العقود في المملكة',
                'tags': 'امتثال,أنظمة,سعودية,قانوني'
            },
            {
                'title': 'شرط التحكيم في الرياض',
                'content': 'أي نزاع ينشأ عن هذا العقد يُحال إلى التحكيم وفقاً لنظام التحكيم السعودي في مدينة الرياض.',
                'category': 'تسوية النزاعات',
                'subcategory': 'التحكيم',
                'legal_system': 'saudi',
                'risk_level': 'medium',
                'risk_score': 45,
                'description': 'تحديد آلية تسوية النزاعات',
                'usage_notes': 'يفضل التحكيم على التقاضي في العقود التجارية',
                'tags': 'تحكيم,نزاعات,رياض,تسوية'
            },
            {
                'title': 'بند الزكاة والضرائب',
                'content': 'كل طرف مسؤول عن دفع الزكاة والضرائب المستحقة عليه وفقاً للأنظمة السعودية.',
                'category': 'المالية',
                'subcategory': 'الزكاة والضرائب',
                'legal_system': 'saudi',
                'risk_level': 'high',
                'risk_score': 75,
                'description': 'تحديد المسؤولية الضريبية',
                'usage_notes': 'ضروري مع تطبيق ضريبة القيمة المضافة',
                'tags': 'زكاة,ضرائب,مالي,سعودية'
            },
            {
                'title': 'شرط السعودة',
                'content': 'يلتزم المقاول بتطبيق نسب السعودة المطلوبة وفقاً لبرنامج نطاقات ولوائح وزارة الموارد البشرية.',
                'category': 'الموارد البشرية',
                'subcategory': 'السعودة',
                'legal_system': 'saudi',
                'risk_level': 'high',
                'risk_score': 80,
                'description': 'الالتزام بمتطلبات السعودة',
                'usage_notes': 'إلزامي في عقود الخدمات والمقاولات',
                'tags': 'سعودة,نطاقات,موارد بشرية'
            },
            {
                'title': 'بند حماية البيانات الشخصية',
                'content': 'يلتزم الطرفان بحماية البيانات الشخصية وفقاً لنظام حماية البيانات الشخصية السعودي.',
                'category': 'حماية البيانات',
                'subcategory': 'البيانات الشخصية',
                'legal_system': 'saudi',
                'risk_level': 'high',
                'risk_score': 85,
                'description': 'ضمان حماية البيانات الشخصية',
                'usage_notes': 'إلزامي مع نظام حماية البيانات الجديد',
                'tags': 'بيانات,حماية,خصوصية,سعودية'
            }
        ]
        
        # Add clauses if they don't exist
        try:
            existing_clauses = self.get_legal_clauses()
            if len(existing_clauses) == 0:
                logger.info("Initializing default legal clauses...")

                for clause in kuwait_clauses + saudi_clauses:
                    self.db_manager.add_legal_clause(**clause)

                logger.info(f"Added {len(kuwait_clauses + saudi_clauses)} default clauses")
        except Exception as e:
            logger.error(f"Error initializing default clauses: {e}")
    
    def get_legal_clauses(self, legal_system: str = None, category: str = None,
                         search_term: str = None) -> List[Dict[str, Any]]:
        """Get legal clauses with optional filtering"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT id, title, content, category, subcategory, legal_system,
                           risk_level, risk_score, description, usage_notes, tags,
                           created_by, created_at, updated_at
                    FROM legal_clauses
                    WHERE 1=1
                '''
                params = []

                if legal_system:
                    query += ' AND legal_system = ?'
                    params.append(legal_system)

                if category:
                    query += ' AND category = ?'
                    params.append(category)

                if search_term:
                    query += ' AND (title LIKE ? OR content LIKE ? OR description LIKE ?)'
                    search_pattern = f'%{search_term}%'
                    params.extend([search_pattern, search_pattern, search_pattern])

                query += ' ORDER BY title'

                cursor.execute(query, params)

                clauses = []
                for row in cursor.fetchall():
                    clauses.append({
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'category': row[3],
                        'subcategory': row[4],
                        'legal_system': row[5],
                        'risk_level': row[6],
                        'risk_score': row[7],
                        'description': row[8],
                        'usage_notes': row[9],
                        'tags': row[10],
                        'created_by': row[11],
                        'created_at': row[12],
                        'updated_at': row[13]
                    })

                return clauses

        except Exception as e:
            logger.error(f"Error getting legal clauses: {e}")
            return []

    def search_clauses(self, search_term: str, legal_system: str = None,
                      category: str = None) -> List[Dict[str, Any]]:
        """Search for clauses with advanced filtering"""
        return self.get_legal_clauses(
            legal_system=legal_system,
            category=category,
            search_term=search_term
        )
    
    def get_clauses_by_category(self, category: str, legal_system: str = None) -> List[Dict[str, Any]]:
        """Get clauses by category"""
        return self.get_legal_clauses(
            legal_system=legal_system,
            category=category
        )

    def get_high_risk_clauses(self, legal_system: str = None) -> List[Dict[str, Any]]:
        """Get clauses with high risk scores"""
        clauses = self.get_legal_clauses(legal_system=legal_system)
        return [clause for clause in clauses if clause['risk_score'] >= 70]
    
    def add_custom_clause(self, title: str, content: str, category: str, 
                         legal_system: str, **kwargs) -> Optional[str]:
        """Add a custom clause to the library"""
        return self.db_manager.add_legal_clause(
            title=title,
            content=content,
            category=category,
            legal_system=legal_system,
            **kwargs
        )
    
    def get_clause_statistics(self, legal_system: str = None) -> Dict[str, Any]:
        """Get statistics about the clause library"""
        try:
            clauses = self.get_legal_clauses(legal_system=legal_system)
            categories = self.db_manager.get_clause_categories(legal_system=legal_system)
            
            if not clauses:
                return {
                    'total_clauses': 0,
                    'categories': [],
                    'risk_distribution': {},
                    'average_risk_score': 0
                }
            
            # Calculate risk distribution
            risk_distribution = {'low': 0, 'medium': 0, 'high': 0}
            total_risk_score = 0
            
            for clause in clauses:
                risk_level = clause['risk_level']
                if risk_level in risk_distribution:
                    risk_distribution[risk_level] += 1
                total_risk_score += clause['risk_score']
            
            average_risk_score = total_risk_score / len(clauses) if clauses else 0
            
            return {
                'total_clauses': len(clauses),
                'categories': categories,
                'risk_distribution': risk_distribution,
                'average_risk_score': round(average_risk_score, 2)
            }
            
        except Exception as e:
            logger.error(f"Error getting clause statistics: {e}")
            return {
                'total_clauses': 0,
                'categories': [],
                'risk_distribution': {},
                'average_risk_score': 0
            }
    
    def export_clauses(self, legal_system: str = None) -> Dict[str, Any]:
        """Export clauses for backup or sharing"""
        try:
            clauses = self.get_legal_clauses(legal_system=legal_system)
            
            export_data = {
                'export_type': 'legal_clauses',
                'export_timestamp': datetime.now().isoformat(),
                'legal_system': legal_system,
                'total_clauses': len(clauses),
                'clauses': clauses
            }
            
            return export_data
            
        except Exception as e:
            logger.error(f"Error exporting clauses: {e}")
            return None
    
    def import_clauses(self, import_data: Dict[str, Any], created_by: str = None) -> int:
        """Import clauses from export data"""
        try:
            if 'clauses' not in import_data:
                return 0
            
            imported_count = 0
            for clause_data in import_data['clauses']:
                # Remove id and timestamps for import
                clause_data.pop('id', None)
                clause_data.pop('created_at', None)
                clause_data.pop('updated_at', None)
                clause_data['created_by'] = created_by
                
                clause_id = self.db_manager.add_legal_clause(**clause_data)
                if clause_id:
                    imported_count += 1
            
            return imported_count
            
        except Exception as e:
            logger.error(f"Error importing clauses: {e}")
            return 0
