#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Law Selection UI Component
Interface for selecting and managing legal systems
"""

import streamlit as st
from typing import Dict, Any, Optional
from legal_frameworks import get_legal_framework_manager, LegalSystem, LegalReference
from enhanced_i18n import get_i18n, t

class LawSelectionUI:
    """Law selection user interface component"""
    
    def __init__(self):
        self.legal_manager = get_legal_framework_manager()
        self.i18n = get_i18n()
    
    def render_law_selector(self, key: str = "law_selector") -> LegalSystem:
        """Render law selection dropdown"""
        available_systems = self.legal_manager.get_available_systems()
        current_system = self.legal_manager.current_system.value

        # Law selector header
        st.subheader(f"⚖️ {t('forms.legal_system')}")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            selected_system = st.selectbox(
                t('forms.legal_system'),
                options=list(available_systems.keys()),
                format_func=lambda x: available_systems[x],
                index=list(available_systems.keys()).index(current_system),
                key=key,
                label_visibility="collapsed"
            )
        
        with col2:
            if st.button("🔄 " + t('buttons.apply'), key=f"{key}_apply"):
                if selected_system != current_system:
                    self.legal_manager.set_legal_system(LegalSystem(selected_system))
                    st.success(f"{t('messages.updated')}: {available_systems[selected_system]}")
                    st.rerun()
        
        # Display current system information
        self._render_system_info(LegalSystem(selected_system))
        
        return LegalSystem(selected_system)
    
    def _render_system_info(self, system: LegalSystem):
        """Render information about the selected legal system"""
        # Temporarily set the system to get its info
        original_system = self.legal_manager.current_system
        self.legal_manager.set_legal_system(system)
        
        system_name = self.legal_manager.get_system_name()
        system_description = self.legal_manager.get_system_description()
        
        # Restore original system
        self.legal_manager.set_legal_system(original_system)
        
        if system == LegalSystem.KUWAIT:
            self._render_kuwait_info()
        elif system == LegalSystem.SAUDI_ARABIA:
            self._render_saudi_info()
        
        with st.expander(f"📋 {t('legal.legal_analysis')}", expanded=True):
            st.write(system_description)
    
    def _render_kuwait_info(self):
        """Render Kuwait legal system information"""
        st.markdown(f"### 🇰🇼 {t('legal.kuwait_law')}")
        st.caption("النظام القانوني الكويتي")

        col1, col2 = st.columns(2)

        with col1:
            st.info(f"📜 **{t('legal.civil_code')}**\nالقانون المدني رقم 67/1980")
            st.success(f"👥 **{t('legal.labor_law')}**\nقانون العمل في القطاع الأهلي")

        with col2:
            st.warning(f"🏢 **{t('legal.commercial_code')}**\nالقانون التجاري رقم 68/1980")
            st.info(f"⚖️ **{t('legal.compliance_check')}**\nفحص الامتثال التلقائي")
    
    def _render_saudi_info(self):
        """Render Saudi legal system information"""
        st.markdown(f"### 🇸🇦 {t('legal.saudi_law')}")
        st.caption("النظام القانوني السعودي")

        col1, col2 = st.columns(2)

        with col1:
            st.info(f"📜 **{t('legal.civil_code')}**\nنظام المعاملات المدنية")
            st.success(f"👥 **{t('legal.labor_law')}**\nنظام العمل السعودي")

        with col2:
            st.warning(f"🏢 **{t('legal.commercial_code')}**\nنظام الشركات السعودي")
            st.error(f"🕌 **{t('legal.sharia_compliance')}**\nالامتثال للضوابط الشرعية")
    
    def render_analysis_options(self, selected_system: LegalSystem) -> Dict[str, Any]:
        """Render analysis options based on selected legal system"""
        st.subheader(f"🔍 {t('legal.legal_analysis')}")
        
        col1, col2 = st.columns(2)
        
        with col1:
            analysis_depth = st.selectbox(
                "عمق التحليل",
                options=["سريع", "متوسط", "مفصل", "شامل"],
                index=2,
                help="اختر مستوى التفصيل في التحليل"
            )
            
            include_recommendations = st.checkbox(
                "تضمين التوصيات",
                value=True,
                help="إضافة توصيات لتحسين العقد"
            )
        
        with col2:
            risk_sensitivity = st.selectbox(
                "حساسية كشف المخاطر",
                options=["منخفضة", "متوسطة", "عالية", "قصوى"],
                index=2,
                help="مستوى الحساسية في كشف المخاطر القانونية"
            )
            
            if selected_system == LegalSystem.SAUDI_ARABIA:
                sharia_check = st.checkbox(
                    "فحص الامتثال الشرعي",
                    value=True,
                    help="التحقق من الامتثال للضوابط الشرعية"
                )
            else:
                sharia_check = False
        
        # Analysis focus areas
        st.markdown("### مجالات التركيز")
        
        focus_areas = []
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.checkbox("تكوين العقد", value=True):
                focus_areas.append("contract_formation")
            if st.checkbox("الالتزامات", value=True):
                focus_areas.append("obligations")
        
        with col2:
            if st.checkbox("المسؤولية", value=True):
                focus_areas.append("liability")
            if st.checkbox("الإنهاء", value=True):
                focus_areas.append("termination")
        
        with col3:
            if st.checkbox("المخاطر المالية", value=True):
                focus_areas.append("financial_risks")
            if st.checkbox("الامتثال القانوني", value=True):
                focus_areas.append("legal_compliance")
        
        return {
            "legal_system": selected_system,
            "analysis_depth": analysis_depth,
            "risk_sensitivity": risk_sensitivity,
            "include_recommendations": include_recommendations,
            "sharia_check": sharia_check,
            "focus_areas": focus_areas
        }
    
    def render_legal_references(self, system: LegalSystem, topic: str) -> Optional[LegalReference]:
        """Render legal references for a specific topic"""
        # Temporarily set the system
        original_system = self.legal_manager.current_system
        self.legal_manager.set_legal_system(system)
        
        reference = self.legal_manager.get_legal_reference(topic)
        
        # Restore original system
        self.legal_manager.set_legal_system(original_system)
        
        if reference:
            st.markdown(f"""
            <div class="metric-card fade-in">
                <h5 style="margin: 0 0 0.5rem 0; color: var(--primary-color);">
                    📚 {reference.law_name}
                </h5>
                <p style="margin: 0 0 0.5rem 0; color: var(--secondary-color); font-weight: bold;">
                    {reference.article}
                </p>
                <p style="margin: 0; color: var(--text-secondary); font-size: 0.9rem;">
                    {reference.description}
                </p>
                <div style="margin-top: 0.5rem; padding: 0.5rem; background: var(--surface-color); border-radius: 6px;">
                    <span style="color: var(--accent-color); font-size: 0.8rem;">
                        📂 {reference.category}
                    </span>
                </div>
            </div>
            """, unsafe_allow_html=True)
        
        return reference
    
    def get_analysis_prompt_for_system(self, system: LegalSystem) -> str:
        """Get analysis prompt for the selected legal system"""
        # Temporarily set the system
        original_system = self.legal_manager.current_system
        self.legal_manager.set_legal_system(system)
        
        prompt = self.legal_manager.get_analysis_prompt_prefix()
        
        # Restore original system
        self.legal_manager.set_legal_system(original_system)
        
        return prompt

# Global instance
law_selection_ui = LawSelectionUI()

def get_law_selection_ui() -> LawSelectionUI:
    """Get global law selection UI instance"""
    return law_selection_ui
