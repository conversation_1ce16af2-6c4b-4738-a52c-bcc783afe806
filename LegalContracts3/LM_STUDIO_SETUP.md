# إعداد LM Studio لمحلل العقود القانونية الكويتية
## LM Studio Setup for Kuwaiti Legal Contract Analyzer

### 📋 نظرة عامة / Overview

هذا الدليل يوضح كيفية إعداد LM Studio لاستخدامه مع تطبيق محلل العقود القانونية الكويتية.

This guide explains how to set up LM Studio for use with the Kuwaiti Legal Contract Analyzer application.

### 🔧 خطوات الإعداد / Setup Steps

#### 1. تحميل وتثبيت LM Studio / Download and Install LM Studio

1. **تحميل LM Studio:**
   - اذهب إلى: https://lmstudio.ai/
   - حمل النسخة المناسبة لنظام التشغيل الخاص بك
   - ثبت التطبيق

2. **تشغيل LM Studio:**
   - افتح تطبيق LM Studio
   - انتظر حتى يكتمل التحميل

#### 2. تحميل النموذج / Download Model

1. **البحث عن النموذج:**
   - في LM Studio، اذهب إلى تبويب "Search"
   - ابحث عن: `llama-3.1-8b-instruct`
   - أو ابحث عن: `Meta-Llama-3.1-8B-Instruct`

2. **تحميل النموذج:**
   - اختر النموذج المناسب (8B للأجهزة العادية)
   - انقر على "Download"
   - انتظر حتى يكتمل التحميل (قد يستغرق وقتاً طويلاً)

#### 3. تشغيل الخادم المحلي / Start Local Server

1. **تحميل النموذج:**
   - اذهب إلى تبويب "Chat"
   - اختر النموذج المحمل من القائمة
   - انتظر حتى يتم تحميل النموذج في الذاكرة

2. **تشغيل الخادم:**
   - اذهب إلى تبويب "Local Server"
   - تأكد من أن المنفذ هو: `1234`
   - انقر على "Start Server"
   - يجب أن ترى رسالة "Server is running"

#### 4. التحقق من الاتصال / Verify Connection

```bash
# اختبار الاتصال / Test connection
curl http://localhost:1234/v1/models

# يجب أن ترى استجابة JSON مع قائمة النماذج
# You should see a JSON response with model list
```

### 🎯 النماذج الموصى بها / Recommended Models

#### للأجهزة العادية (8-16GB RAM) / For Regular Devices
- **llama-3.1-8b-instruct** ⭐ (موصى به / Recommended)
- **llama-3-8b-instruct**
- **mistral-7b-instruct**

#### للأجهزة القوية (32GB+ RAM) / For Powerful Devices
- **llama-3.1-13b-instruct**
- **llama-3.1-70b-instruct** (يتطلب GPU قوي / Requires powerful GPU)

### ⚙️ إعدادات الأداء / Performance Settings

#### في LM Studio / In LM Studio:

1. **إعدادات النموذج / Model Settings:**
   - Temperature: 0.3 (للدقة / For accuracy)
   - Max Tokens: 4000
   - Top P: 0.9

2. **إعدادات الخادم / Server Settings:**
   - Port: 1234 (افتراضي / Default)
   - Host: localhost
   - Enable CORS: Yes

#### في التطبيق / In Application:
- سيتم تطبيق الإعدادات تلقائياً
- يمكن تغيير النموذج من الشريط الجانبي

### 🔍 استكشاف الأخطاء / Troubleshooting

#### مشكلة: لا يمكن الاتصال بـ LM Studio
**الحلول / Solutions:**

1. **تأكد من تشغيل الخادم:**
   ```bash
   curl http://localhost:1234/v1/models
   ```

2. **تحقق من المنفذ:**
   - تأكد أن LM Studio يعمل على المنفذ 1234
   - تأكد أن المنفذ غير مستخدم من تطبيق آخر

3. **إعادة تشغيل الخادم:**
   - أوقف الخادم في LM Studio
   - انتظر 5 ثوانٍ
   - شغل الخادم مرة أخرى

#### مشكلة: النموذج بطيء جداً
**الحلول / Solutions:**

1. **استخدم نموذج أصغر:**
   - جرب 7B بدلاً من 8B
   - أو استخدم نموذج مضغوط

2. **تحسين الإعدادات:**
   - قلل Max Tokens إلى 2000
   - استخدم GPU إذا كان متاحاً

3. **إغلاق التطبيقات الأخرى:**
   - أغلق المتصفحات والتطبيقات الثقيلة
   - تأكد من توفر ذاكرة كافية

#### مشكلة: النموذج لا يستجيب بالعربية
**الحلول / Solutions:**

1. **تأكد من النموذج:**
   - استخدم Llama 3.1 (يدعم العربية بشكل أفضل)
   - تجنب النماذج القديمة

2. **تحقق من الإعدادات:**
   - تأكد أن Temperature = 0.3
   - تأكد أن النموذج محمل بالكامل

### 🚀 تشغيل التطبيق / Running the Application

بعد إعداد LM Studio:

```bash
# تشغيل التطبيق / Run application
streamlit run app.py

# أو استخدم السكريبت / Or use script
./run_app.sh
```

### 📊 مقارنة الأداء / Performance Comparison

| النموذج / Model | الحجم / Size | الذاكرة المطلوبة / RAM | السرعة / Speed | الجودة / Quality |
|------------------|--------------|------------------------|-----------------|-------------------|
| llama-3.1-8b     | ~4.7GB       | 8GB+                   | متوسط / Medium  | عالية / High     |
| llama-3-8b       | ~4.7GB       | 8GB+                   | متوسط / Medium  | جيدة / Good      |
| mistral-7b       | ~4.1GB       | 6GB+                   | سريع / Fast     | جيدة / Good      |

### 💡 نصائح للأداء الأفضل / Tips for Better Performance

1. **استخدم SSD:** لتحميل أسرع للنموذج
2. **ذاكرة كافية:** تأكد من توفر ذاكرة كافية
3. **إغلاق التطبيقات:** أغلق التطبيقات غير الضرورية
4. **GPU:** استخدم GPU إذا كان متاحاً
5. **تحديث LM Studio:** استخدم أحدث إصدار

### 🔗 روابط مفيدة / Useful Links

- **LM Studio الرسمي:** https://lmstudio.ai/
- **دليل LM Studio:** https://lmstudio.ai/docs
- **نماذج Hugging Face:** https://huggingface.co/models
- **مجتمع LM Studio:** https://discord.gg/lmstudio

### 📞 الدعم / Support

إذا واجهت مشاكل:

1. **راجع هذا الدليل** مرة أخرى
2. **تشغيل اختبار التثبيت:**
   ```bash
   python3 test_installation.py
   ```
3. **تحقق من سجلات LM Studio** للأخطاء
4. **تواصل مع الدعم** إذا استمرت المشاكل

---

**ملاحظة:** LM Studio يوفر واجهة سهلة الاستخدام وأداء ممتاز للنماذج المحلية. هذا الإعداد يضمن خصوصية كاملة ومعالجة محلية بدون إنترنت.

**Note:** LM Studio provides an easy-to-use interface and excellent performance for local models. This setup ensures complete privacy and local processing without internet.
