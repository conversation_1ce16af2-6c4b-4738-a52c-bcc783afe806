#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User Management System for Enhanced Legal Contract Analyzer
Comprehensive user administration with role-based access control
"""

import sqlite3
import hashlib
import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserManager:
    """Comprehensive user management system"""
    
    def __init__(self, db_path: str = "legal_analyzer.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize user management database tables"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until TIMESTAMP,
                    preferences TEXT DEFAULT '{}',
                    profile_image TEXT,
                    phone TEXT,
                    department TEXT,
                    notes TEXT
                )
            """)
            
            # User sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # User activity log
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # User permissions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    permission TEXT NOT NULL,
                    granted_by TEXT,
                    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            conn.commit()
            
            # Create default admin user if not exists
            self._create_default_admin()
            
            conn.close()
            logger.info("User management database initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            raise
    
    def _create_default_admin(self):
        """Create default admin user if not exists"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if admin exists
            cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if cursor.fetchone():
                conn.close()
                return
            
            # Create admin user
            admin_id = str(uuid.uuid4())
            salt = self._generate_salt()
            password_hash = self._hash_password("admin123", salt)
            
            cursor.execute("""
                INSERT INTO users (
                    id, username, password_hash, salt, email, full_name, role, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                admin_id, "admin", password_hash, salt, 
                "<EMAIL>", "System Administrator", "admin", 1
            ))
            
            conn.commit()
            conn.close()
            logger.info("Default admin user created")
            
        except Exception as e:
            logger.error(f"Error creating default admin: {e}")
    
    def _generate_salt(self) -> str:
        """Generate random salt for password hashing"""
        return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:16]
    
    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt"""
        return hashlib.sha256((password + salt).encode()).hexdigest()
    
    def create_user(self, username: str, password: str, email: str, 
                   full_name: str, role: str = "user", is_active: bool = True,
                   phone: str = "", department: str = "") -> Optional[str]:
        """Create new user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if username or email already exists
            cursor.execute("SELECT id FROM users WHERE username = ? OR email = ?", (username, email))
            if cursor.fetchone():
                conn.close()
                return None
            
            # Create user
            user_id = str(uuid.uuid4())
            salt = self._generate_salt()
            password_hash = self._hash_password(password, salt)
            
            cursor.execute("""
                INSERT INTO users (
                    id, username, password_hash, salt, email, full_name, 
                    role, is_active, phone, department
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, username, password_hash, salt, email, full_name,
                role, is_active, phone, department
            ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.log_activity(user_id, "user_created", f"User {username} created")
            
            logger.info(f"User {username} created successfully")
            return user_id
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user credentials"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get user data
            cursor.execute("""
                SELECT id, username, password_hash, salt, email, full_name, 
                       role, is_active, login_attempts, locked_until
                FROM users WHERE username = ?
            """, (username,))
            
            user_data = cursor.fetchone()
            if not user_data:
                conn.close()
                return None
            
            user_id, username, stored_hash, salt, email, full_name, role, is_active, login_attempts, locked_until = user_data
            
            # Check if account is locked
            if locked_until:
                lock_time = datetime.fromisoformat(locked_until)
                if datetime.now() < lock_time:
                    conn.close()
                    return None
            
            # Check if account is active
            if not is_active:
                conn.close()
                return None
            
            # Verify password
            password_hash = self._hash_password(password, salt)
            if password_hash != stored_hash:
                # Increment login attempts
                cursor.execute("""
                    UPDATE users SET login_attempts = login_attempts + 1
                    WHERE id = ?
                """, (user_id,))
                
                # Lock account after 5 failed attempts
                if login_attempts >= 4:
                    lock_until = datetime.now() + timedelta(minutes=30)
                    cursor.execute("""
                        UPDATE users SET locked_until = ?
                        WHERE id = ?
                    """, (lock_until.isoformat(), user_id))
                
                conn.commit()
                conn.close()
                return None
            
            # Reset login attempts and update last login
            cursor.execute("""
                UPDATE users SET 
                    login_attempts = 0, 
                    locked_until = NULL,
                    last_login = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (user_id,))
            
            conn.commit()
            conn.close()
            
            # Log successful login
            self.log_activity(user_id, "login", f"User {username} logged in")
            
            return {
                'id': user_id,
                'username': username,
                'email': email,
                'full_name': full_name,
                'role': role,
                'is_active': is_active
            }
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, username, email, full_name, role, is_active, 
                       created_at, last_login, phone, department
                FROM users ORDER BY created_at DESC
            """)
            
            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'full_name': row[3],
                    'role': row[4],
                    'is_active': bool(row[5]),
                    'created_at': row[6],
                    'last_login': row[7],
                    'phone': row[8],
                    'department': row[9]
                })
            
            conn.close()
            return users
            
        except Exception as e:
            logger.error(f"Error getting users: {e}")
            return []
    
    def update_user(self, user_id: str, **kwargs) -> bool:
        """Update user information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Build update query
            update_fields = []
            values = []
            
            for field, value in kwargs.items():
                if field in ['username', 'email', 'full_name', 'role', 'is_active', 'phone', 'department']:
                    update_fields.append(f"{field} = ?")
                    values.append(value)
            
            if not update_fields:
                conn.close()
                return False
            
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            values.append(user_id)
            
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(query, values)
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.log_activity(user_id, "user_updated", f"User information updated")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            return False
    
    def delete_user(self, user_id: str) -> bool:
        """Delete user (soft delete by deactivating)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Don't allow deleting admin user
            cursor.execute("SELECT username FROM users WHERE id = ?", (user_id,))
            result = cursor.fetchone()
            if result and result[0] == 'admin':
                conn.close()
                return False
            
            # Soft delete by deactivating
            cursor.execute("""
                UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (user_id,))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.log_activity(user_id, "user_deleted", f"User deactivated")
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting user: {e}")
            return False
    
    def log_activity(self, user_id: str, action: str, details: str = "", ip_address: str = ""):
        """Log user activity"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO user_activity (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            """, (user_id, action, details, ip_address))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
    
    def get_user_activity(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get user activity log"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT action, details, ip_address, timestamp
                FROM user_activity 
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (user_id, limit))
            
            activities = []
            for row in cursor.fetchall():
                activities.append({
                    'action': row[0],
                    'details': row[1],
                    'ip_address': row[2],
                    'timestamp': row[3]
                })
            
            conn.close()
            return activities
            
        except Exception as e:
            logger.error(f"Error getting user activity: {e}")
            return []
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """Get user statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total users
            cursor.execute("SELECT COUNT(*) FROM users")
            total_users = cursor.fetchone()[0]
            
            # Active users
            cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            active_users = cursor.fetchone()[0]
            
            # Users by role
            cursor.execute("""
                SELECT role, COUNT(*) 
                FROM users 
                WHERE is_active = 1 
                GROUP BY role
            """)
            role_distribution = dict(cursor.fetchall())
            
            # Recent logins (last 7 days)
            cursor.execute("""
                SELECT COUNT(*) 
                FROM users 
                WHERE last_login >= datetime('now', '-7 days')
            """)
            recent_logins = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'inactive_users': total_users - active_users,
                'role_distribution': role_distribution,
                'recent_logins': recent_logins,
                'activity_rate': (recent_logins / active_users * 100) if active_users > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}
