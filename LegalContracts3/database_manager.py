#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Manager for Legal Contract Analysis
Handles SQLite database operations for storing analysis history, user data, and templates
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import streamlit as st
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages SQLite database operations for the legal contract analysis application"""
    
    def __init__(self, db_path: str = "legal_contracts.db"):
        """Initialize database manager"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with required tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id TEXT PRIMARY KEY,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        email TEXT,
                        full_name TEXT,
                        role TEXT DEFAULT 'user',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        preferences TEXT DEFAULT '{}'
                    )
                ''')
                
                # Contracts table (enhanced)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS contracts (
                        id TEXT PRIMARY KEY,
                        user_id TEXT,
                        filename TEXT NOT NULL,
                        file_type TEXT,
                        file_size INTEGER,
                        content TEXT NOT NULL,
                        metadata TEXT DEFAULT '{}',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # Analysis results table (enhanced)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS analysis_results (
                        id TEXT PRIMARY KEY,
                        contract_id TEXT NOT NULL,
                        user_id TEXT,
                        analysis_type TEXT DEFAULT 'comprehensive',
                        results TEXT NOT NULL,
                        risk_score INTEGER DEFAULT 0,
                        legal_points TEXT DEFAULT '[]',
                        recommendations TEXT DEFAULT '[]',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (contract_id) REFERENCES contracts (id),
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')
                
                # Templates table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS templates (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        category TEXT,
                        template_text TEXT NOT NULL,
                        legal_system TEXT,
                        created_by TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1,
                        usage_count INTEGER DEFAULT 0,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')
                
                # Analysis history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS analysis_history (
                        id TEXT PRIMARY KEY,
                        user_id TEXT,
                        contract_id TEXT,
                        analysis_id TEXT,
                        action TEXT,
                        details TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        FOREIGN KEY (contract_id) REFERENCES contracts (id),
                        FOREIGN KEY (analysis_id) REFERENCES analysis_results (id)
                    )
                ''')
                
                # System settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Menu preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS menu_preferences (
                        id TEXT PRIMARY KEY,
                        user_id TEXT,
                        menu_item TEXT NOT NULL,
                        is_visible BOOLEAN DEFAULT 1,
                        display_order INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')

                # Shared analyses table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS shared_analyses (
                        id TEXT PRIMARY KEY,
                        analysis_id TEXT NOT NULL,
                        shared_by TEXT NOT NULL,
                        share_code TEXT UNIQUE NOT NULL,
                        access_type TEXT DEFAULT 'read_only',
                        expires_at TIMESTAMP,
                        access_count INTEGER DEFAULT 0,
                        max_access_count INTEGER DEFAULT -1,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (analysis_id) REFERENCES analysis_results (id),
                        FOREIGN KEY (shared_by) REFERENCES users (id)
                    )
                ''')

                # Legal clauses library table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS legal_clauses (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        category TEXT NOT NULL,
                        subcategory TEXT,
                        legal_system TEXT NOT NULL,
                        risk_level TEXT DEFAULT 'medium',
                        risk_score INTEGER DEFAULT 50,
                        description TEXT,
                        usage_notes TEXT,
                        tags TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_by TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')

                # Contract templates table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS contract_templates (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        category TEXT NOT NULL,
                        legal_system TEXT NOT NULL,
                        template_content TEXT NOT NULL,
                        variables TEXT,
                        instructions TEXT,
                        risk_assessment TEXT,
                        compliance_notes TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_by TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')

                # Custom legal prompts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS custom_prompts (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        prompt_text TEXT NOT NULL,
                        category TEXT,
                        legal_system TEXT,
                        analysis_type TEXT DEFAULT 'custom',
                        is_public BOOLEAN DEFAULT 0,
                        usage_count INTEGER DEFAULT 0,
                        created_by TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')

                # Compliance rules table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS compliance_rules (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        legal_system TEXT NOT NULL,
                        jurisdiction TEXT,
                        rule_type TEXT NOT NULL,
                        rule_content TEXT NOT NULL,
                        severity TEXT DEFAULT 'medium',
                        is_active BOOLEAN DEFAULT 1,
                        created_by TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                ''')

                # Create default admin user if not exists
                self._create_default_admin(cursor)

                # Insert default settings
                self._insert_default_settings(cursor)

                # Migrate existing database if needed
                self._migrate_database(cursor)

                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Database initialization error: {e}")
            raise

    def _migrate_database(self, cursor):
        """Migrate existing database to new schema"""
        try:
            # Check if we need to migrate contracts table
            cursor.execute("PRAGMA table_info(contracts)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'user_id' not in columns:
                logger.info("Migrating contracts table...")
                # Add missing columns to contracts table
                try:
                    cursor.execute("ALTER TABLE contracts ADD COLUMN user_id TEXT")
                except:
                    pass  # Column might already exist
                try:
                    cursor.execute("ALTER TABLE contracts ADD COLUMN content TEXT")
                except:
                    pass
                try:
                    cursor.execute("ALTER TABLE contracts ADD COLUMN metadata TEXT DEFAULT '{}'")
                except:
                    pass

                # Migrate data from original_text to content if needed
                try:
                    cursor.execute("UPDATE contracts SET content = original_text WHERE content IS NULL OR content = ''")
                    # Make original_text nullable for new records
                    cursor.execute("CREATE TABLE contracts_new AS SELECT id, user_id, filename, file_type, file_size, content, metadata, created_at, updated_at FROM contracts")
                    cursor.execute("DROP TABLE contracts")
                    cursor.execute("ALTER TABLE contracts_new RENAME TO contracts")
                except Exception as e:
                    logger.warning(f"Could not migrate original_text column: {e}")
                    # Create a new contracts table with correct schema
                    cursor.execute("DROP TABLE IF EXISTS contracts")
                    cursor.execute('''
                        CREATE TABLE contracts (
                            id TEXT PRIMARY KEY,
                            user_id TEXT,
                            filename TEXT NOT NULL,
                            file_type TEXT,
                            file_size INTEGER,
                            content TEXT NOT NULL,
                            metadata TEXT DEFAULT '{}',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    ''')

            # Check if we need to migrate analysis_results table
            cursor.execute("PRAGMA table_info(analysis_results)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'analysis_type' not in columns:
                logger.info("Migrating analysis_results table...")
                # Add missing columns to analysis_results table
                try:
                    cursor.execute("ALTER TABLE analysis_results ADD COLUMN user_id TEXT")
                except:
                    pass
                try:
                    cursor.execute("ALTER TABLE analysis_results ADD COLUMN analysis_type TEXT DEFAULT 'comprehensive'")
                except:
                    pass
                try:
                    cursor.execute("ALTER TABLE analysis_results ADD COLUMN results TEXT")
                except:
                    pass
                try:
                    cursor.execute("ALTER TABLE analysis_results ADD COLUMN legal_points TEXT DEFAULT '[]'")
                except:
                    pass
                try:
                    cursor.execute("ALTER TABLE analysis_results ADD COLUMN recommendations TEXT DEFAULT '[]'")
                except:
                    pass

                # Migrate data from analysis_data to results if needed
                try:
                    cursor.execute("UPDATE analysis_results SET results = analysis_data WHERE results IS NULL OR results = ''")
                    # Recreate table with correct schema
                    cursor.execute("CREATE TABLE analysis_results_new AS SELECT id, contract_id, user_id, analysis_type, results, risk_score, legal_points, recommendations, created_at FROM analysis_results")
                    cursor.execute("DROP TABLE analysis_results")
                    cursor.execute("ALTER TABLE analysis_results_new RENAME TO analysis_results")
                except Exception as e:
                    logger.warning(f"Could not migrate analysis_data column: {e}")
                    # Create a new analysis_results table with correct schema
                    cursor.execute("DROP TABLE IF EXISTS analysis_results")
                    cursor.execute('''
                        CREATE TABLE analysis_results (
                            id TEXT PRIMARY KEY,
                            contract_id TEXT NOT NULL,
                            user_id TEXT,
                            analysis_type TEXT DEFAULT 'comprehensive',
                            results TEXT NOT NULL,
                            risk_score INTEGER DEFAULT 0,
                            legal_points TEXT DEFAULT '[]',
                            recommendations TEXT DEFAULT '[]',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (contract_id) REFERENCES contracts (id),
                            FOREIGN KEY (user_id) REFERENCES users (id)
                        )
                    ''')

            logger.info("Database migration completed")

        except Exception as e:
            logger.warning(f"Database migration warning: {e}")
            # Don't fail initialization due to migration issues
    
    def _create_default_admin(self, cursor):
        """Create default admin user"""
        import hashlib
        
        # Check if admin exists
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone():
            return
        
        # Create admin user
        admin_id = str(uuid.uuid4())
        password_hash = hashlib.sha256("admin123".encode()).hexdigest()
        
        cursor.execute('''
            INSERT INTO users (id, username, password_hash, email, full_name, role)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (admin_id, 'admin', password_hash, '<EMAIL>', 'System Administrator', 'admin'))
        
        logger.info("Default admin user created")
    
    def _insert_default_settings(self, cursor):
        """Insert default system settings"""
        default_settings = [
            ('app_version', '1.0.0', 'Application version'),
            ('default_legal_system', 'kuwait', 'Default legal system'),
            ('max_file_size', '52428800', 'Maximum file size in bytes (50MB)'),
            ('supported_languages', '["ar", "en"]', 'Supported languages'),
            ('ai_backend', 'lmstudio', 'Default AI backend'),
            ('analysis_retention_days', '365', 'Analysis data retention period in days')
        ]
        
        for key, value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (key, value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))
    
    def save_contract(self, filename: str, text: str, analysis_data: Dict[str, Any], 
                     user_id: str, file_type: str = "text") -> str:
        """Save contract and analysis results"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Save contract
                contract_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO contracts (id, filename, original_text, file_size, file_type, uploaded_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (contract_id, filename, text, len(text), file_type, user_id))
                
                # Save analysis results
                analysis_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO analysis_results (id, contract_id, legal_system, risk_score, analysis_data)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    analysis_id, 
                    contract_id, 
                    analysis_data.get('legal_system', 'kuwait'),
                    analysis_data.get('risk_score', 0),
                    json.dumps(analysis_data, ensure_ascii=False)
                ))
                
                # Log history
                self._log_history(cursor, user_id, contract_id, analysis_id, 'contract_analyzed', 
                                'Contract uploaded and analyzed')
                
                conn.commit()
                logger.info(f"Contract saved with ID: {contract_id}")
                return contract_id
                
        except Exception as e:
            logger.error(f"Error saving contract: {e}")
            raise
    
    def get_user_contracts(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get contracts for a specific user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT c.id, c.filename, c.created_at, c.file_type,
                           ar.risk_score, ar.legal_system, ar.analysis_data
                    FROM contracts c
                    LEFT JOIN analysis_results ar ON c.id = ar.contract_id
                    WHERE c.uploaded_by = ?
                    ORDER BY c.created_at DESC
                    LIMIT ?
                ''', (user_id, limit))
                
                contracts = []
                for row in cursor.fetchall():
                    analysis_data = json.loads(row[6]) if row[6] else {}
                    contracts.append({
                        'id': row[0],
                        'filename': row[1],
                        'created_at': row[2],
                        'file_type': row[3],
                        'risk_score': row[4] or 0,
                        'legal_system': row[5] or 'kuwait',
                        'clauses': analysis_data.get('clauses', 0),
                        'issues': analysis_data.get('issues', 0),
                        'pages': analysis_data.get('pages', 1)
                    })
                
                return contracts
                
        except Exception as e:
            logger.error(f"Error getting user contracts: {e}")
            return []
    
    def get_contract_details(self, contract_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed contract information"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT c.*, ar.analysis_data, ar.risk_score, ar.legal_system
                    FROM contracts c
                    LEFT JOIN analysis_results ar ON c.id = ar.contract_id
                    WHERE c.id = ?
                ''', (contract_id,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                analysis_data = json.loads(row[8]) if row[8] else {}
                
                return {
                    'id': row[0],
                    'filename': row[1],
                    'original_text': row[2],
                    'file_size': row[3],
                    'file_type': row[4],
                    'uploaded_by': row[5],
                    'created_at': row[6],
                    'updated_at': row[7],
                    'analysis_data': analysis_data,
                    'risk_score': row[9] or 0,
                    'legal_system': row[10] or 'kuwait'
                }
                
        except Exception as e:
            logger.error(f"Error getting contract details: {e}")
            return None
    
    def save_template(self, name: str, description: str, category: str, 
                     template_text: str, legal_system: str, user_id: str) -> str:
        """Save contract template"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                template_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO templates (id, name, description, category, template_text, 
                                         legal_system, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (template_id, name, description, category, template_text, legal_system, user_id))
                
                self._log_history(cursor, user_id, None, None, 'template_created', 
                                f'Template "{name}" created')
                
                conn.commit()
                logger.info(f"Template saved with ID: {template_id}")
                return template_id
                
        except Exception as e:
            logger.error(f"Error saving template: {e}")
            raise
    
    def get_templates(self, legal_system: str = None, category: str = None) -> List[Dict[str, Any]]:
        """Get available templates"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = "SELECT * FROM templates WHERE is_active = 1"
                params = []
                
                if legal_system:
                    query += " AND legal_system = ?"
                    params.append(legal_system)
                
                if category:
                    query += " AND category = ?"
                    params.append(category)
                
                query += " ORDER BY usage_count DESC, created_at DESC"
                
                cursor.execute(query, params)
                
                templates = []
                for row in cursor.fetchall():
                    templates.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'category': row[3],
                        'template_text': row[4],
                        'legal_system': row[5],
                        'created_by': row[6],
                        'created_at': row[7],
                        'updated_at': row[8],
                        'usage_count': row[10]
                    })
                
                return templates
                
        except Exception as e:
            logger.error(f"Error getting templates: {e}")
            return []
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user login"""
        try:
            import hashlib
            
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, username, email, full_name, role, preferences
                    FROM users 
                    WHERE username = ? AND password_hash = ? AND is_active = 1
                ''', (username, password_hash))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                # Update last login
                cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                ''', (row[0],))
                
                conn.commit()
                
                return {
                    'id': row[0],
                    'username': row[1],
                    'email': row[2],
                    'full_name': row[3],
                    'role': row[4],
                    'preferences': json.loads(row[5]) if row[5] else {}
                }
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def _log_history(self, cursor, user_id: str, contract_id: str, analysis_id: str, 
                    action: str, details: str):
        """Log user action to history"""
        history_id = str(uuid.uuid4())
        cursor.execute('''
            INSERT INTO analysis_history (id, user_id, contract_id, analysis_id, action, details)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (history_id, user_id, contract_id, analysis_id, action, details))
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total contracts
                cursor.execute("SELECT COUNT(*) FROM contracts")
                total_contracts = cursor.fetchone()[0]
                
                # Total users
                cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
                total_users = cursor.fetchone()[0]
                
                # Total analyses
                cursor.execute("SELECT COUNT(*) FROM analysis_results")
                total_analyses = cursor.fetchone()[0]
                
                # Average risk score
                cursor.execute("SELECT AVG(risk_score) FROM analysis_results WHERE risk_score > 0")
                avg_risk = cursor.fetchone()[0] or 0
                
                return {
                    'total_contracts': total_contracts,
                    'total_users': total_users,
                    'total_analyses': total_analyses,
                    'average_risk_score': round(avg_risk, 1)
                }
                
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {
                'total_contracts': 0,
                'total_users': 0,
                'total_analyses': 0,
                'average_risk_score': 0
            }

    # Menu Preferences Management
    def get_menu_preferences(self, user_id: Optional[str] = None) -> Dict[str, bool]:
        """Get menu preferences for a user or global preferences"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if user_id:
                    cursor.execute('''
                        SELECT menu_item, is_visible
                        FROM menu_preferences
                        WHERE user_id = ?
                        ORDER BY display_order
                    ''', (user_id,))
                else:
                    # Get global preferences (admin settings)
                    cursor.execute('''
                        SELECT menu_item, is_visible
                        FROM menu_preferences
                        WHERE user_id IS NULL
                        ORDER BY display_order
                    ''')

                preferences = {}
                for row in cursor.fetchall():
                    preferences[row[0]] = bool(row[1])

                # If no preferences found, return default all visible
                if not preferences:
                    default_items = [
                        "home", "analysis", "database", "statistics", "templates",
                        "history", "risk", "collaboration", "reports", "advanced",
                        "insights", "monitoring", "guidelines", "users", "settings"
                    ]
                    for item in default_items:
                        preferences[item] = True

                return preferences

        except Exception as e:
            logger.error(f"Error getting menu preferences: {e}")
            # Return default all visible
            return {
                "home": True, "analysis": True, "database": True, "statistics": True,
                "templates": True, "history": True, "risk": True, "collaboration": True,
                "reports": True, "advanced": True, "insights": True, "monitoring": True,
                "guidelines": True, "users": True, "settings": True
            }

    def update_menu_preferences(self, preferences: Dict[str, bool], user_id: Optional[str] = None) -> bool:
        """Update menu preferences for a user or global preferences"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Delete existing preferences
                if user_id:
                    cursor.execute('DELETE FROM menu_preferences WHERE user_id = ?', (user_id,))
                else:
                    cursor.execute('DELETE FROM menu_preferences WHERE user_id IS NULL')

                # Insert new preferences
                for order, (menu_item, is_visible) in enumerate(preferences.items()):
                    pref_id = str(uuid.uuid4())
                    cursor.execute('''
                        INSERT INTO menu_preferences
                        (id, user_id, menu_item, is_visible, display_order)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (pref_id, user_id, menu_item, is_visible, order))

                conn.commit()
                logger.info(f"Menu preferences updated for user: {user_id or 'global'}")
                return True

        except Exception as e:
            logger.error(f"Error updating menu preferences: {e}")
            return False

    def reset_menu_preferences(self, user_id: Optional[str] = None) -> bool:
        """Reset menu preferences to default (all visible)"""
        default_preferences = {
            "home": True, "analysis": True, "database": True, "statistics": True,
            "templates": True, "history": True, "risk": True, "collaboration": True,
            "reports": True, "advanced": True, "insights": True, "monitoring": True,
            "guidelines": True, "users": True, "settings": True
        }
        return self.update_menu_preferences(default_preferences, user_id)

    # Contract Analysis History Management
    def save_analysis_result(self, user_id: str, contract_data: Dict[str, Any], analysis_data: Dict[str, Any]) -> str:
        """Save contract analysis result to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Generate IDs
                contract_id = str(uuid.uuid4())
                analysis_id = str(uuid.uuid4())

                # Save contract data
                cursor.execute('''
                    INSERT INTO contracts
                    (id, user_id, filename, file_type, file_size, content, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    contract_id,
                    user_id,
                    contract_data.get('filename', 'Unknown'),
                    contract_data.get('file_type', 'unknown'),
                    contract_data.get('file_size', 0),
                    contract_data.get('content', ''),
                    json.dumps(contract_data.get('metadata', {})),
                    datetime.now().isoformat()
                ))

                # Save analysis result
                cursor.execute('''
                    INSERT INTO analysis_results
                    (id, contract_id, user_id, analysis_type, results, risk_score, legal_points, recommendations, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analysis_id,
                    contract_id,
                    user_id,
                    analysis_data.get('analysis_type', 'comprehensive'),
                    json.dumps(analysis_data),
                    analysis_data.get('risk_score', 0),
                    json.dumps(analysis_data.get('legal_points', [])),
                    json.dumps(analysis_data.get('recommendations', [])),
                    datetime.now().isoformat()
                ))

                # Log the analysis action
                history_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO analysis_history
                    (id, user_id, contract_id, analysis_id, action, details, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    history_id,
                    user_id,
                    contract_id,
                    analysis_id,
                    'analysis_completed',
                    json.dumps({
                        'filename': contract_data.get('filename'),
                        'risk_score': analysis_data.get('risk_score'),
                        'analysis_type': analysis_data.get('analysis_type')
                    }),
                    datetime.now().isoformat()
                ))

                conn.commit()
                logger.info(f"Analysis result saved: {analysis_id}")
                return analysis_id

        except Exception as e:
            logger.error(f"Error saving analysis result: {e}")
            return None

    def get_user_analysis_history(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get analysis history for a user with pagination"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT
                        ar.id as analysis_id,
                        ar.created_at,
                        ar.analysis_type,
                        ar.risk_score,
                        c.filename,
                        c.file_type,
                        c.file_size,
                        ar.results,
                        ar.legal_points,
                        ar.recommendations
                    FROM analysis_results ar
                    JOIN contracts c ON ar.contract_id = c.id
                    WHERE ar.user_id = ?
                    ORDER BY ar.created_at DESC
                    LIMIT ? OFFSET ?
                ''', (user_id, limit, offset))

                history = []
                for row in cursor.fetchall():
                    try:
                        results = json.loads(row[7]) if row[7] else {}
                        legal_points = json.loads(row[8]) if row[8] else []
                        recommendations = json.loads(row[9]) if row[9] else []
                    except json.JSONDecodeError:
                        results = {}
                        legal_points = []
                        recommendations = []

                    history.append({
                        'analysis_id': row[0],
                        'created_at': row[1],
                        'analysis_type': row[2],
                        'risk_score': row[3],
                        'filename': row[4],
                        'file_type': row[5],
                        'file_size': row[6],
                        'results': results,
                        'legal_points': legal_points,
                        'recommendations': recommendations
                    })

                return history

        except Exception as e:
            logger.error(f"Error getting analysis history: {e}")
            return []

    def get_analysis_by_id(self, analysis_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get specific analysis by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT
                        ar.id,
                        ar.created_at,
                        ar.analysis_type,
                        ar.risk_score,
                        ar.results,
                        ar.legal_points,
                        ar.recommendations,
                        c.filename,
                        c.file_type,
                        c.file_size,
                        c.content,
                        c.metadata
                    FROM analysis_results ar
                    JOIN contracts c ON ar.contract_id = c.id
                    WHERE ar.id = ? AND ar.user_id = ?
                ''', (analysis_id, user_id))

                row = cursor.fetchone()
                if not row:
                    return None

                try:
                    results = json.loads(row[4]) if row[4] else {}
                    legal_points = json.loads(row[5]) if row[5] else []
                    recommendations = json.loads(row[6]) if row[6] else []
                    metadata = json.loads(row[11]) if row[11] else {}
                except json.JSONDecodeError:
                    results = {}
                    legal_points = []
                    recommendations = []
                    metadata = {}

                return {
                    'analysis_id': row[0],
                    'created_at': row[1],
                    'analysis_type': row[2],
                    'risk_score': row[3],
                    'results': results,
                    'legal_points': legal_points,
                    'recommendations': recommendations,
                    'filename': row[7],
                    'file_type': row[8],
                    'file_size': row[9],
                    'content': row[10],
                    'metadata': metadata
                }

        except Exception as e:
            logger.error(f"Error getting analysis by ID: {e}")
            return None

    def search_analysis_history(self, user_id: str, search_term: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search analysis history by filename or content"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                search_pattern = f"%{search_term}%"
                cursor.execute('''
                    SELECT
                        ar.id as analysis_id,
                        ar.created_at,
                        ar.analysis_type,
                        ar.risk_score,
                        c.filename,
                        c.file_type,
                        c.file_size,
                        ar.results
                    FROM analysis_results ar
                    JOIN contracts c ON ar.contract_id = c.id
                    WHERE ar.user_id = ?
                    AND (c.filename LIKE ? OR c.content LIKE ?)
                    ORDER BY ar.created_at DESC
                    LIMIT ?
                ''', (user_id, search_pattern, search_pattern, limit))

                history = []
                for row in cursor.fetchall():
                    try:
                        results = json.loads(row[7]) if row[7] else {}
                    except json.JSONDecodeError:
                        results = {}

                    history.append({
                        'analysis_id': row[0],
                        'created_at': row[1],
                        'analysis_type': row[2],
                        'risk_score': row[3],
                        'filename': row[4],
                        'file_type': row[5],
                        'file_size': row[6],
                        'results': results
                    })

                return history

        except Exception as e:
            logger.error(f"Error searching analysis history: {e}")
            return []

    def delete_analysis(self, analysis_id: str, user_id: str) -> bool:
        """Delete an analysis and its associated contract"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get contract_id first
                cursor.execute('SELECT contract_id FROM analysis_results WHERE id = ? AND user_id = ?',
                             (analysis_id, user_id))
                result = cursor.fetchone()
                if not result:
                    return False

                contract_id = result[0]

                # Delete in order: history, analysis, contract
                cursor.execute('DELETE FROM analysis_history WHERE analysis_id = ?', (analysis_id,))
                cursor.execute('DELETE FROM analysis_results WHERE id = ?', (analysis_id,))
                cursor.execute('DELETE FROM contracts WHERE id = ?', (contract_id,))

                conn.commit()
                logger.info(f"Analysis deleted: {analysis_id}")
                return True

        except Exception as e:
            logger.error(f"Error deleting analysis: {e}")
            return False

    # Analysis Sharing System
    def create_share_link(self, analysis_id: str, user_id: str, access_type: str = 'read_only',
                         expires_hours: Optional[int] = None, max_access: int = -1) -> Optional[str]:
        """Create a shareable link for an analysis"""
        try:
            import secrets
            import string

            # Generate unique share code
            share_code = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))

            # Calculate expiration time
            expires_at = None
            if expires_hours:
                from datetime import timedelta
                expires_at = (datetime.now() + timedelta(hours=expires_hours)).isoformat()

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Verify user owns the analysis
                cursor.execute('''
                    SELECT id FROM analysis_results
                    WHERE id = ? AND user_id = ?
                ''', (analysis_id, user_id))

                if not cursor.fetchone():
                    logger.warning(f"User {user_id} does not own analysis {analysis_id}")
                    return None

                # Create share record
                share_id = str(uuid.uuid4())
                cursor.execute('''
                    INSERT INTO shared_analyses
                    (id, analysis_id, shared_by, share_code, access_type, expires_at, max_access_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (share_id, analysis_id, user_id, share_code, access_type, expires_at, max_access))

                conn.commit()
                logger.info(f"Share link created: {share_code}")
                return share_code

        except Exception as e:
            logger.error(f"Error creating share link: {e}")
            return None

    def get_shared_analysis(self, share_code: str) -> Optional[Dict[str, Any]]:
        """Get analysis by share code"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Get share info and check validity
                cursor.execute('''
                    SELECT sa.analysis_id, sa.access_type, sa.expires_at, sa.access_count,
                           sa.max_access_count, sa.is_active, sa.shared_by
                    FROM shared_analyses sa
                    WHERE sa.share_code = ?
                ''', (share_code,))

                share_info = cursor.fetchone()
                if not share_info:
                    return None

                analysis_id, access_type, expires_at, access_count, max_access, is_active, shared_by = share_info

                # Check if share is still valid
                if not is_active:
                    return None

                if expires_at:
                    expire_time = datetime.fromisoformat(expires_at)
                    if datetime.now() > expire_time:
                        return None

                if max_access > 0 and access_count >= max_access:
                    return None

                # Get the analysis
                analysis = self.get_analysis_by_id(analysis_id, shared_by)
                if not analysis:
                    return None

                # Increment access count
                cursor.execute('''
                    UPDATE shared_analyses
                    SET access_count = access_count + 1
                    WHERE share_code = ?
                ''', (share_code,))
                conn.commit()

                # Add sharing metadata
                analysis['share_info'] = {
                    'access_type': access_type,
                    'access_count': access_count + 1,
                    'shared_by': shared_by
                }

                return analysis

        except Exception as e:
            logger.error(f"Error getting shared analysis: {e}")
            return None

    def get_user_shared_analyses(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all analyses shared by a user"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT sa.id, sa.analysis_id, sa.share_code, sa.access_type,
                           sa.expires_at, sa.access_count, sa.max_access_count,
                           sa.is_active, sa.created_at, ar.risk_score, c.filename
                    FROM shared_analyses sa
                    JOIN analysis_results ar ON sa.analysis_id = ar.id
                    JOIN contracts c ON ar.contract_id = c.id
                    WHERE sa.shared_by = ?
                    ORDER BY sa.created_at DESC
                ''', (user_id,))

                shares = []
                for row in cursor.fetchall():
                    shares.append({
                        'share_id': row[0],
                        'analysis_id': row[1],
                        'share_code': row[2],
                        'access_type': row[3],
                        'expires_at': row[4],
                        'access_count': row[5],
                        'max_access_count': row[6],
                        'is_active': row[7],
                        'created_at': row[8],
                        'risk_score': row[9],
                        'filename': row[10]
                    })

                return shares

        except Exception as e:
            logger.error(f"Error getting user shared analyses: {e}")
            return []

    def revoke_share_link(self, share_code: str, user_id: str) -> bool:
        """Revoke a share link"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE shared_analyses
                    SET is_active = 0
                    WHERE share_code = ? AND shared_by = ?
                ''', (share_code, user_id))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error revoking share link: {e}")
            return False

    def get_user_shared_links(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all shared links created by a user (alias for get_user_shared_analyses)"""
        return self.get_user_shared_analyses(user_id)

    def delete_shared_link(self, share_id: str, user_id: str) -> bool:
        """Delete a shared link"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    DELETE FROM shared_analyses
                    WHERE id = ? AND shared_by = ?
                ''', (share_id, user_id))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error deleting shared link: {e}")
            return False

    # Export/Import System
    def export_analysis_data(self, analysis_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Export complete analysis data for backup"""
        try:
            analysis = self.get_analysis_by_id(analysis_id, user_id)
            if not analysis:
                return None

            # Create comprehensive export package
            export_data = {
                'export_version': '1.0',
                'export_timestamp': datetime.now().isoformat(),
                'analysis': analysis,
                'metadata': {
                    'exported_by': user_id,
                    'original_analysis_id': analysis_id,
                    'export_type': 'complete_backup'
                }
            }

            return export_data

        except Exception as e:
            logger.error(f"Error exporting analysis data: {e}")
            return None

    def import_analysis_data(self, export_data: Dict[str, Any], user_id: str) -> Optional[str]:
        """Import analysis data from backup"""
        try:
            # Validate export data
            if 'analysis' not in export_data or 'export_version' not in export_data:
                logger.error("Invalid export data format")
                return None

            analysis = export_data['analysis']

            # Prepare contract data
            contract_data = {
                'filename': f"[IMPORTED] {analysis.get('filename', 'Unknown')}",
                'file_type': analysis.get('file_type', 'imported'),
                'file_size': analysis.get('file_size', 0),
                'content': analysis.get('content', ''),
                'metadata': {
                    'imported_from': export_data.get('metadata', {}),
                    'import_timestamp': datetime.now().isoformat(),
                    'original_analysis_id': analysis.get('analysis_id')
                }
            }

            # Prepare analysis data
            analysis_data = {
                'analysis_type': f"imported_{analysis.get('analysis_type', 'comprehensive')}",
                'risk_score': analysis.get('risk_score', 0),
                'legal_points': analysis.get('legal_points', []),
                'recommendations': analysis.get('recommendations', []),
                'text': analysis.get('content', ''),
                'results': analysis.get('results', {})
            }

            # Save as new analysis
            new_analysis_id = self.save_analysis_result(user_id, contract_data, analysis_data)

            if new_analysis_id:
                logger.info(f"Analysis imported successfully: {new_analysis_id}")
                return new_analysis_id
            else:
                logger.error("Failed to save imported analysis")
                return None

        except Exception as e:
            logger.error(f"Error importing analysis data: {e}")
            return None

    # Legal Clauses Library System
    def add_legal_clause(self, title: str, content: str, category: str, legal_system: str,
                        subcategory: str = None, risk_level: str = 'medium', risk_score: int = 50,
                        description: str = None, usage_notes: str = None, tags: str = None,
                        created_by: str = None) -> Optional[str]:
        """Add a new legal clause to the library"""
        try:
            clause_id = str(uuid.uuid4())

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO legal_clauses
                    (id, title, content, category, subcategory, legal_system, risk_level,
                     risk_score, description, usage_notes, tags, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (clause_id, title, content, category, subcategory, legal_system,
                      risk_level, risk_score, description, usage_notes, tags, created_by))

                conn.commit()
                logger.info(f"Legal clause added: {clause_id}")
                return clause_id

        except Exception as e:
            logger.error(f"Error adding legal clause: {e}")
            return None

    def get_legal_clauses(self, legal_system: str = None, category: str = None,
                         search_term: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get legal clauses with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT id, title, content, category, subcategory, legal_system,
                           risk_level, risk_score, description, usage_notes, tags,
                           created_at, updated_at
                    FROM legal_clauses
                    WHERE is_active = 1
                '''
                params = []

                if legal_system:
                    query += ' AND legal_system = ?'
                    params.append(legal_system)

                if category:
                    query += ' AND category = ?'
                    params.append(category)

                if search_term:
                    query += ' AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)'
                    search_pattern = f'%{search_term}%'
                    params.extend([search_pattern, search_pattern, search_pattern])

                query += ' ORDER BY category, title LIMIT ?'
                params.append(limit)

                cursor.execute(query, params)

                clauses = []
                for row in cursor.fetchall():
                    clauses.append({
                        'id': row[0],
                        'title': row[1],
                        'content': row[2],
                        'category': row[3],
                        'subcategory': row[4],
                        'legal_system': row[5],
                        'risk_level': row[6],
                        'risk_score': row[7],
                        'description': row[8],
                        'usage_notes': row[9],
                        'tags': row[10],
                        'created_at': row[11],
                        'updated_at': row[12]
                    })

                return clauses

        except Exception as e:
            logger.error(f"Error getting legal clauses: {e}")
            return []

    def get_clause_categories(self, legal_system: str = None) -> List[Dict[str, Any]]:
        """Get available clause categories"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT category, COUNT(*) as count
                    FROM legal_clauses
                    WHERE is_active = 1
                '''
                params = []

                if legal_system:
                    query += ' AND legal_system = ?'
                    params.append(legal_system)

                query += ' GROUP BY category ORDER BY category'

                cursor.execute(query, params)

                categories = []
                for row in cursor.fetchall():
                    categories.append({
                        'category': row[0],
                        'count': row[1]
                    })

                return categories

        except Exception as e:
            logger.error(f"Error getting clause categories: {e}")
            return []

    def update_legal_clause(self, clause_id: str, **kwargs) -> bool:
        """Update a legal clause"""
        try:
            if not kwargs:
                return False

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Build update query dynamically
                set_clauses = []
                params = []

                for key, value in kwargs.items():
                    if key in ['title', 'content', 'category', 'subcategory', 'legal_system',
                              'risk_level', 'risk_score', 'description', 'usage_notes', 'tags']:
                        set_clauses.append(f'{key} = ?')
                        params.append(value)

                if not set_clauses:
                    return False

                set_clauses.append('updated_at = CURRENT_TIMESTAMP')
                params.append(clause_id)

                query = f'''
                    UPDATE legal_clauses
                    SET {', '.join(set_clauses)}
                    WHERE id = ?
                '''

                cursor.execute(query, params)
                conn.commit()

                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error updating legal clause: {e}")
            return False

    def delete_legal_clause(self, clause_id: str) -> bool:
        """Soft delete a legal clause"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE legal_clauses
                    SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (clause_id,))

                conn.commit()
                return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error deleting legal clause: {e}")
            return False
