#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Export Utilities Module
Handles PDF and Word document generation for contract analysis results
"""

import io
from datetime import datetime
from typing import Dict, List, Any
import streamlit as st

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.enums import TA_RIGHT, TA_CENTER
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

class PDFExporter:
    """Export analysis results to PDF format"""
    
    def __init__(self):
        """Initialize PDF exporter"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab is required for PDF export. Install with: pip install reportlab")
        
        # Register Arabic font if available
        self.arabic_font = self._register_arabic_font()
        
    def _register_arabic_font(self) -> str:
        """Register Arabic font for PDF generation"""
        try:
            import os
            # Try to register a system Arabic font
            font_paths = [
                '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS
                '/System/Library/Fonts/Arial.ttf',  # macOS
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',  # Linux
                'C:/Windows/Fonts/arial.ttf',  # Windows
                'C:/Windows/Fonts/calibri.ttf',  # Windows
            ]

            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        return 'Arabic'
                except Exception as e:
                    continue

            # Fallback to default font
            return 'Helvetica'
        except Exception as e:
            return 'Helvetica'
    
    def create_pdf(self, analysis: Dict[str, Any]) -> bytes:
        """Create PDF document from analysis results"""
        buffer = io.BytesIO()
        
        # Create document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Build content
        story = []
        styles = self._get_styles()
        
        # Title
        title = Paragraph("Legal Contract Analysis Report / تقرير تحليل العقد القانوني", styles['ArabicTitle'])
        story.append(title)
        story.append(Spacer(1, 6))

        # Company branding
        company_info = Paragraph("Developed by MAXBIT LLC - 2025 / تطوير شركة ماكس بت المحدودة", styles['ArabicReference'])
        story.append(company_info)

        # Contact information
        contact_info = Paragraph("maxbit.net | <EMAIL> | +1 626 509 0918", styles['ArabicReference'])
        story.append(contact_info)
        story.append(Spacer(1, 12))
        
        # Document info
        info_data = [
            ['File Name / اسم الملف:', analysis.get('filename', 'Not specified / غير محدد')],
            ['Analysis Date / تاريخ التحليل:', self._format_timestamp(analysis.get('timestamp'))],
            ['Analysis Type / نوع التحليل:', 'Kuwaiti Law Analysis / تحليل وفقاً للقانون الكويتي']
        ]
        
        info_table = Table(info_data, colWidths=[2*inch, 4*inch])
        font_name = 'Helvetica' if self.arabic_font == 'Helvetica' else self.arabic_font
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (0, -1), colors.grey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # Translation section
        if analysis.get('translation'):
            story.append(Paragraph("الترجمة العربية", styles['ArabicHeading1']))
            story.append(Spacer(1, 12))
            
            translation_text = analysis['translation'][:2000] + "..." if len(analysis['translation']) > 2000 else analysis['translation']
            story.append(Paragraph(translation_text, styles['ArabicBodyText']))
            story.append(Spacer(1, 20))
        
        # Legal points section
        if analysis.get('legal_points'):
            story.append(Paragraph("النقاط القانونية", styles['ArabicHeading1']))
            story.append(Spacer(1, 12))

            for point in analysis['legal_points']:
                story.append(Paragraph(f"• {point.get('title', '')}", styles['ArabicHeading2']))
                story.append(Paragraph(point.get('description', ''), styles['ArabicBodyText']))
                if point.get('law_reference'):
                    story.append(Paragraph(f"المرجع القانوني: {point['law_reference']}", styles['ArabicReference']))
                story.append(Spacer(1, 10))
        
        # Recommendations section
        if analysis.get('recommendations'):
            story.append(Paragraph("التوصيات", styles['ArabicHeading1']))
            story.append(Spacer(1, 12))

            for rec in analysis['recommendations']:
                story.append(Paragraph(f"• {rec.get('title', '')}", styles['ArabicHeading2']))
                story.append(Paragraph(rec.get('description', ''), styles['ArabicBodyText']))
                story.append(Spacer(1, 10))

        # Footer with company branding
        story.append(Spacer(1, 30))
        footer_line = Paragraph("_" * 80, styles['ArabicReference'])
        story.append(footer_line)
        story.append(Spacer(1, 10))

        # Footer content
        footer_text = Paragraph("Generated by Kuwaiti Legal Contract Analyzer / تم إنشاء هذا التقرير بواسطة محلل العقود القانونية الكويتية", styles['ArabicReference'])
        story.append(footer_text)
        story.append(Spacer(1, 6))

        company_footer = Paragraph("Developed by MAXBIT LLC © 2025 / تطوير شركة ماكس بت المحدودة", styles['ArabicReference'])
        story.append(company_footer)
        story.append(Spacer(1, 6))

        contact_footer = Paragraph("maxbit.net | <EMAIL> | +1 626 509 0918", styles['ArabicReference'])
        story.append(contact_footer)

        # Build PDF
        doc.build(story)
        
        # Get PDF data
        pdf_data = buffer.getvalue()
        buffer.close()
        
        return pdf_data
    
    def _get_styles(self):
        """Get paragraph styles for PDF"""
        styles = getSampleStyleSheet()

        # Use Helvetica as fallback for better compatibility
        font_name = 'Helvetica' if self.arabic_font == 'Helvetica' else self.arabic_font

        # Arabic title style
        styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName=font_name
        ))

        # Arabic heading style
        styles.add(ParagraphStyle(
            name='ArabicHeading1',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            fontName=font_name
        ))

        # Arabic subheading style
        styles.add(ParagraphStyle(
            name='ArabicHeading2',
            parent=styles['Heading2'],
            fontSize=12,
            spaceAfter=6,
            alignment=TA_RIGHT,
            fontName=font_name
        ))

        # Arabic body text style
        styles.add(ParagraphStyle(
            name='ArabicBodyText',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=12,
            alignment=TA_RIGHT,
            fontName=font_name
        ))

        # Reference style
        styles.add(ParagraphStyle(
            name='ArabicReference',
            parent=styles['Normal'],
            fontSize=9,
            spaceAfter=6,
            alignment=TA_RIGHT,
            fontName=self.arabic_font,
            textColor=colors.blue
        ))
        
        return styles
    
    def _format_timestamp(self, timestamp: str) -> str:
        """Format timestamp for display"""
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return timestamp or 'غير محدد'

class WordExporter:
    """Export analysis results to Word document format"""
    
    def __init__(self):
        """Initialize Word exporter"""
        if not PYTHON_DOCX_AVAILABLE:
            raise ImportError("python-docx is required for Word export. Install with: pip install python-docx")
    
    def create_document(self, analysis: Dict[str, Any]) -> bytes:
        """Create Word document from analysis results"""
        doc = Document()
        
        # Set document direction to RTL
        sections = doc.sections
        for section in sections:
            section.page_height = Inches(11.69)
            section.page_width = Inches(8.27)
            section.left_margin = Inches(1)
            section.right_margin = Inches(1)
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
        
        # Add title
        title = doc.add_heading('تقرير تحليل العقد القانوني', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add company branding
        company_para = doc.add_paragraph()
        company_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        company_run = company_para.add_run('تطوير شركة ماكس بت المحدودة (MAXBIT LLC) - 2025')
        company_run.font.size = Pt(10)
        company_run.font.italic = True

        doc.add_paragraph()  # Empty line

        # Add document information
        doc.add_heading('معلومات الوثيقة', level=1)
        
        info_table = doc.add_table(rows=3, cols=2)
        info_table.style = 'Table Grid'
        
        info_data = [
            ['اسم الملف:', analysis.get('filename', 'غير محدد')],
            ['تاريخ التحليل:', self._format_timestamp(analysis.get('timestamp'))],
            ['نوع التحليل:', 'تحليل وفقاً للقانون الكويتي']
        ]
        
        for i, (label, value) in enumerate(info_data):
            info_table.cell(i, 0).text = label
            info_table.cell(i, 1).text = value
            info_table.cell(i, 0).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
            info_table.cell(i, 1).paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # Add translation section
        if analysis.get('translation'):
            doc.add_heading('الترجمة العربية', level=1)
            translation_para = doc.add_paragraph(analysis['translation'])
            translation_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        
        # Add legal points section
        if analysis.get('legal_points'):
            doc.add_heading('النقاط القانونية', level=1)
            
            for point in analysis['legal_points']:
                # Point title
                point_title = doc.add_heading(point.get('title', ''), level=2)
                point_title.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                # Point description
                desc_para = doc.add_paragraph(point.get('description', ''))
                desc_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                # Legal reference
                if point.get('law_reference'):
                    ref_para = doc.add_paragraph(f"المرجع القانوني: {point['law_reference']}")
                    ref_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                    ref_run = ref_para.runs[0]
                    ref_run.font.size = Pt(9)
                    ref_run.font.color.rgb = None  # Blue color
        
        # Add recommendations section
        if analysis.get('recommendations'):
            doc.add_heading('التوصيات', level=1)
            
            for rec in analysis['recommendations']:
                # Recommendation title
                rec_title = doc.add_heading(rec.get('title', ''), level=2)
                rec_title.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                # Recommendation description
                desc_para = doc.add_paragraph(rec.get('description', ''))
                desc_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                
                # Urgency indicator
                urgency = rec.get('urgency', 'medium')
                urgency_text = {
                    'urgent': 'عاجل',
                    'important': 'مهم',
                    'optional': 'اختياري'
                }.get(urgency, urgency)
                
                urgency_para = doc.add_paragraph(f"الأولوية: {urgency_text}")
                urgency_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                urgency_run = urgency_para.runs[0]
                urgency_run.font.size = Pt(9)
                urgency_run.bold = True
        
        # Add footer with company branding
        doc.add_paragraph()  # Empty line

        footer_para1 = doc.add_paragraph()
        footer_para1.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_run1 = footer_para1.add_run(f"تم إنشاء هذا التقرير بواسطة محلل العقود القانونية الكويتية - {datetime.now().strftime('%Y-%m-%d')}")
        footer_run1.font.size = Pt(9)

        footer_para2 = doc.add_paragraph()
        footer_para2.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_run2 = footer_para2.add_run("تطوير شركة ماكس بت المحدودة (MAXBIT LLC) © 2025")
        footer_run2.font.size = Pt(8)
        footer_run2.font.bold = True

        footer_para3 = doc.add_paragraph()
        footer_para3.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_run3 = footer_para3.add_run("www.maxbit.net")
        footer_run3.font.size = Pt(8)
        footer_run3.font.italic = True
        
        # Save to buffer
        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)
        
        return buffer.getvalue()
    
    def _format_timestamp(self, timestamp: str) -> str:
        """Format timestamp for display"""
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return timestamp or 'غير محدد'

class ExportManager:
    """Manage all export operations"""
    
    def __init__(self):
        """Initialize export manager"""
        self.pdf_exporter = None
        self.word_exporter = None
        
        # Initialize exporters if libraries are available
        if REPORTLAB_AVAILABLE:
            try:
                self.pdf_exporter = PDFExporter()
            except Exception as e:
                st.warning(f"تعذر تهيئة مصدر PDF: {str(e)}")
        
        if PYTHON_DOCX_AVAILABLE:
            try:
                self.word_exporter = WordExporter()
            except Exception as e:
                st.warning(f"تعذر تهيئة مصدر Word: {str(e)}")
    
    def export_pdf(self, analysis: Dict[str, Any]) -> bytes:
        """Export to PDF"""
        if not self.pdf_exporter:
            raise RuntimeError("PDF exporter not available. Install reportlab: pip install reportlab")
        
        return self.pdf_exporter.create_pdf(analysis)
    
    def export_word(self, analysis: Dict[str, Any]) -> bytes:
        """Export to Word"""
        if not self.word_exporter:
            raise RuntimeError("Word exporter not available. Install python-docx: pip install python-docx")
        
        return self.word_exporter.create_document(analysis)
    
    def is_pdf_available(self) -> bool:
        """Check if PDF export is available"""
        return self.pdf_exporter is not None
    
    def is_word_available(self) -> bool:
        """Check if Word export is available"""
        return self.word_exporter is not None
