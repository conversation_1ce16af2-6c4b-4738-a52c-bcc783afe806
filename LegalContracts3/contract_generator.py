#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI-Powered Contract Templates Generator
Creates complete contracts from scratch based on user requirements
"""

import json
import logging
import requests
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from database_manager import DatabaseManager
from clause_library_manager import ClauseLibraryManager

logger = logging.getLogger(__name__)

class ContractGenerator:
    """AI-powered contract generation system"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.clause_manager = ClauseLibraryManager()
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """Initialize default contract templates"""
        
        default_templates = [
            {
                'name': 'عقد خدمات استشارية',
                'description': 'قالب لعقود الخدمات الاستشارية والمهنية',
                'category': 'خدمات',
                'legal_system': 'kuwait',
                'template_content': '''
عقد خدمات استشارية

الطرف الأول: {client_name}
الطرف الثاني: {consultant_name}

نطاق الخدمات:
{service_scope}

المدة: {duration}
القيمة: {amount} {currency}

شروط الدفع:
{payment_terms}

المسؤوليات:
{responsibilities}

بنود إضافية:
{additional_clauses}
                ''',
                'variables': json.dumps({
                    'client_name': 'اسم العميل',
                    'consultant_name': 'اسم المستشار',
                    'service_scope': 'نطاق الخدمات',
                    'duration': 'مدة العقد',
                    'amount': 'المبلغ',
                    'currency': 'العملة',
                    'payment_terms': 'شروط الدفع',
                    'responsibilities': 'المسؤوليات',
                    'additional_clauses': 'بنود إضافية'
                }),
                'instructions': 'يُستخدم لعقود الخدمات الاستشارية والمهنية',
                'risk_assessment': 'متوسط المخاطر - يتطلب تحديد واضح لنطاق الخدمات',
                'compliance_notes': 'يجب مراجعة متطلبات الترخيص المهني'
            },
            {
                'name': 'عقد توريد',
                'description': 'قالب لعقود التوريد والمشتريات',
                'category': 'توريد',
                'legal_system': 'kuwait',
                'template_content': '''
عقد توريد

المورد: {supplier_name}
المشتري: {buyer_name}

المواد المطلوبة:
{items_description}

الكمية: {quantity}
السعر الإجمالي: {total_price} {currency}

مواعيد التسليم:
{delivery_schedule}

شروط الجودة:
{quality_terms}

الضمانات:
{warranties}

شروط الدفع:
{payment_terms}
                ''',
                'variables': json.dumps({
                    'supplier_name': 'اسم المورد',
                    'buyer_name': 'اسم المشتري',
                    'items_description': 'وصف المواد',
                    'quantity': 'الكمية',
                    'total_price': 'السعر الإجمالي',
                    'currency': 'العملة',
                    'delivery_schedule': 'جدول التسليم',
                    'quality_terms': 'شروط الجودة',
                    'warranties': 'الضمانات',
                    'payment_terms': 'شروط الدفع'
                }),
                'instructions': 'يُستخدم لعقود التوريد والمشتريات',
                'risk_assessment': 'عالي المخاطر - يتطلب تحديد دقيق للمواصفات',
                'compliance_notes': 'يجب مراجعة متطلبات الجمارك والمعايير'
            },
            {
                'name': 'عقد عمل سعودي',
                'description': 'قالب لعقود العمل وفقاً لنظام العمل السعودي',
                'category': 'عمل',
                'legal_system': 'saudi',
                'template_content': '''
عقد عمل

صاحب العمل: {employer_name}
العامل: {employee_name}

المسمى الوظيفي: {job_title}
الراتب الأساسي: {basic_salary} ريال سعودي
البدلات: {allowances}

ساعات العمل: {working_hours}
الإجازات: {vacation_days} يوم سنوياً

مدة العقد: {contract_duration}
مكان العمل: {work_location}

الالتزامات:
{obligations}

شروط الإنهاء:
{termination_terms}

السعودة: يخضع هذا العقد لمتطلبات برنامج نطاقات
                ''',
                'variables': json.dumps({
                    'employer_name': 'اسم صاحب العمل',
                    'employee_name': 'اسم العامل',
                    'job_title': 'المسمى الوظيفي',
                    'basic_salary': 'الراتب الأساسي',
                    'allowances': 'البدلات',
                    'working_hours': 'ساعات العمل',
                    'vacation_days': 'أيام الإجازة',
                    'contract_duration': 'مدة العقد',
                    'work_location': 'مكان العمل',
                    'obligations': 'الالتزامات',
                    'termination_terms': 'شروط الإنهاء'
                }),
                'instructions': 'يُستخدم لعقود العمل في المملكة العربية السعودية',
                'risk_assessment': 'متوسط المخاطر - يجب الامتثال لنظام العمل',
                'compliance_notes': 'يجب مراجعة نظام العمل السعودي ومتطلبات السعودة'
            }
        ]
        
        # Add templates if they don't exist
        try:
            existing_templates = self.get_contract_templates()
            if len(existing_templates) == 0:
                logger.info("Initializing default contract templates...")
                
                for template in default_templates:
                    self.add_contract_template(**template)
                
                logger.info(f"Added {len(default_templates)} default templates")
        except Exception as e:
            logger.error(f"Error initializing default templates: {e}")
    
    def add_contract_template(self, name: str, description: str, category: str,
                            legal_system: str, template_content: str, variables: str = None,
                            instructions: str = None, risk_assessment: str = None,
                            compliance_notes: str = None, created_by: str = None) -> Optional[str]:
        """Add a new contract template"""
        try:
            template_id = str(uuid.uuid4())
            
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO contract_templates 
                    (id, name, description, category, legal_system, template_content,
                     variables, instructions, risk_assessment, compliance_notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (template_id, name, description, category, legal_system, template_content,
                      variables, instructions, risk_assessment, compliance_notes, created_by))
                
                conn.commit()
                logger.info(f"Contract template added: {template_id}")
                return template_id
                
        except Exception as e:
            logger.error(f"Error adding contract template: {e}")
            return None
    
    def get_contract_templates(self, legal_system: str = None, category: str = None) -> List[Dict[str, Any]]:
        """Get contract templates with optional filtering"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT id, name, description, category, legal_system, template_content,
                           variables, instructions, risk_assessment, compliance_notes,
                           created_at, updated_at
                    FROM contract_templates 
                    WHERE is_active = 1
                '''
                params = []
                
                if legal_system:
                    query += ' AND legal_system = ?'
                    params.append(legal_system)
                
                if category:
                    query += ' AND category = ?'
                    params.append(category)
                
                query += ' ORDER BY category, name'
                
                cursor.execute(query, params)
                
                templates = []
                for row in cursor.fetchall():
                    templates.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'category': row[3],
                        'legal_system': row[4],
                        'template_content': row[5],
                        'variables': json.loads(row[6]) if row[6] else {},
                        'instructions': row[7],
                        'risk_assessment': row[8],
                        'compliance_notes': row[9],
                        'created_at': row[10],
                        'updated_at': row[11]
                    })
                
                return templates
                
        except Exception as e:
            logger.error(f"Error getting contract templates: {e}")
            return []

    def get_user_contracts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get contracts generated by a specific user"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()

                # For now, return empty list as we don't have a generated contracts table yet
                # This would need to be implemented with a proper contracts table
                return []

        except Exception as e:
            logger.error(f"Error getting user contracts: {e}")
            return []

    def save_generated_contract(self, contract_params: Dict[str, Any],
                               contract_content: str, user_id: str) -> Optional[str]:
        """Save a generated contract (placeholder implementation)"""
        try:
            # For now, just return a mock contract ID
            # This would need proper database implementation
            contract_id = str(uuid.uuid4())
            logger.info(f"Generated contract saved with ID: {contract_id}")
            return contract_id

        except Exception as e:
            logger.error(f"Error saving generated contract: {e}")
            return None
    
    def generate_contract_with_ai(self, contract_type: str, requirements: Dict[str, Any],
                                 legal_system: str = 'kuwait', ai_model: str = 'lm_studio') -> Optional[Dict[str, Any]]:
        """Generate a complete contract using AI based on requirements"""
        try:
            # Get relevant clauses
            relevant_clauses = self.clause_manager.search_clauses(
                search_term=contract_type,
                legal_system=legal_system
            )
            
            # Prepare AI prompt
            prompt = self._build_contract_generation_prompt(
                contract_type, requirements, legal_system, relevant_clauses
            )
            
            # Call AI service
            ai_response = self._call_ai_service(prompt, ai_model)

            if ai_response and ai_response.strip():
                # Parse and structure the response
                contract_data = self._parse_ai_contract_response(ai_response, requirements)
                
                # Add metadata
                contract_data.update({
                    'generation_method': 'ai_powered',
                    'legal_system': legal_system,
                    'contract_type': contract_type,
                    'requirements': requirements,
                    'used_clauses': [clause['id'] for clause in relevant_clauses[:5]]
                })
                
                return contract_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error generating contract with AI: {e}")
            return None
    
    def _build_contract_generation_prompt(self, contract_type: str, requirements: Dict[str, Any],
                                        legal_system: str, clauses: List[Dict[str, Any]]) -> str:
        """Build AI prompt for contract generation"""
        
        legal_context = {
            'kuwait': 'القانون الكويتي والقانون المدني الكويتي',
            'saudi': 'النظام السعودي ونظام المعاملات المدنية السعودي'
        }
        
        clauses_text = "\n".join([
            f"- {clause['title']}: {clause['content'][:100]}..."
            for clause in clauses[:5]
        ])
        
        prompt = f"""
أنت خبير قانوني متخصص في صياغة العقود وفقاً لـ {legal_context.get(legal_system, 'القانون العربي')}.

المطلوب: إنشاء عقد {contract_type} كامل ومفصل

متطلبات العقد:
{json.dumps(requirements, ensure_ascii=False, indent=2)}

البنود القانونية المقترحة:
{clauses_text}

يرجى إنشاء عقد شامل يتضمن:
1. ديباجة العقد مع تحديد الأطراف
2. تعريف المصطلحات المهمة
3. نطاق العمل أو الخدمات
4. الالتزامات المالية وشروط الدفع
5. المدة الزمنية وشروط التجديد
6. المسؤوليات والضمانات
7. شروط الإنهاء
8. تسوية النزاعات
9. القانون الواجب التطبيق
10. أحكام عامة

تأكد من:
- الامتثال للقوانين المحلية
- وضوح الصياغة القانونية
- تغطية جميع الجوانب المهمة
- تجنب الغموض أو التضارب

العقد:
"""
        
        return prompt
    
    def _call_ai_service(self, prompt: str, ai_model: str = 'lm_studio') -> Optional[str]:
        """Call AI service for contract generation"""
        try:
            if ai_model == 'lm_studio':
                # Try LM Studio
                lm_studio_url = "http://localhost:1234/v1/chat/completions"

                payload = {
                    "model": "local-model",
                    "messages": [
                        {"role": "system", "content": "أنت خبير قانوني متخصص في صياغة العقود العربية."},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.3,
                    "max_tokens": 4000
                }

                response = requests.post(lm_studio_url, json=payload, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                else:
                    logger.warning(f"LM Studio failed with status {response.status_code}, trying Ollama...")

            # Try Ollama (either as primary choice or fallback)
            ollama_url = "http://localhost:11434/api/generate"

            payload = {
                "model": "llama3.1:8b",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "num_predict": 4000
                }
            }

            response = requests.post(ollama_url, json=payload, timeout=60)

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')

            logger.error(f"Both AI services failed. LM Studio and Ollama are not responding.")
            # Return a basic template-based contract as fallback
            return self._generate_fallback_contract(prompt)

        except Exception as e:
            logger.error(f"Error calling AI service: {e}")
            # Return a basic template-based contract as fallback
            return self._generate_fallback_contract(prompt)
    
    def _parse_ai_contract_response(self, ai_response: str, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response into structured contract data"""
        try:
            # Ensure requirements is a dictionary
            if not isinstance(requirements, dict):
                requirements = {}

            return {
                'contract_content': ai_response,
                'title': requirements.get('contract_title', 'عقد مُولد بالذكاء الاصطناعي'),
                'parties': requirements.get('parties', {}),
                'key_terms': requirements.get('key_terms', {}),
                'generated_at': datetime.now().isoformat(),
                'word_count': len(ai_response.split()),
                'estimated_pages': max(1, len(ai_response) // 2000)
            }

        except Exception as e:
            logger.error(f"Error parsing AI contract response: {e}")
            return {
                'contract_content': ai_response if isinstance(ai_response, str) else str(ai_response),
                'title': 'عقد مُولد بالذكاء الاصطناعي',
                'generated_at': datetime.now().isoformat()
            }

    def _generate_fallback_contract(self, prompt: str) -> str:
        """Generate a basic contract when AI services are not available"""
        try:
            # Extract basic information from prompt for template
            fallback_contract = f"""
عقد قانوني

تم إنشاء هذا العقد باستخدام النظام الأساسي نظراً لعدم توفر خدمات الذكاء الاصطناعي.

الأطراف:
- الطرف الأول: [يرجى تعبئة اسم الطرف الأول]
- الطرف الثاني: [يرجى تعبئة اسم الطرف الثاني]

موضوع العقد:
[يرجى تحديد موضوع العقد]

الشروط والأحكام:
1. يلتزم الطرف الأول بـ [تحديد الالتزامات]
2. يلتزم الطرف الثاني بـ [تحديد الالتزامات]
3. مدة العقد: [تحديد المدة]
4. القيمة المالية: [تحديد القيمة]

أحكام عامة:
- يخضع هذا العقد للقوانين المعمول بها
- أي تعديل على هذا العقد يجب أن يكون مكتوباً وموقعاً من الطرفين
- في حالة النزاع، يتم الرجوع للقضاء المختص

التوقيعات:
الطرف الأول: ________________    التاريخ: ________________
الطرف الثاني: ________________    التاريخ: ________________

ملاحظة: هذا عقد أساسي تم إنشاؤه تلقائياً. يرجى مراجعته وتعديله حسب الحاجة مع استشارة قانونية مناسبة.
"""
            return fallback_contract.strip()

        except Exception as e:
            logger.error(f"Error generating fallback contract: {e}")
            return "خطأ في إنشاء العقد الأساسي. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني."
