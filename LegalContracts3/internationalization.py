"""
Internationalization and Multi-Language Support
Developed by MAXBIT LLC © 2025
"""

import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
import streamlit as st

class LanguageManager:
    """Multi-language support manager"""
    
    def __init__(self, default_language: str = "ar"):
        self.default_language = default_language
        self.current_language = default_language
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """Load all translation files"""
        translations_dir = Path("translations")
        translations_dir.mkdir(exist_ok=True)
        
        # Create default translation files if they don't exist
        self._create_default_translations()
        
        # Load all translation files
        for lang_file in translations_dir.glob("*.json"):
            lang_code = lang_file.stem
            try:
                with open(lang_file, 'r', encoding='utf-8') as f:
                    self.translations[lang_code] = json.load(f)
            except Exception as e:
                st.error(f"Error loading translation file {lang_file}: {e}")
    
    def _create_default_translations(self):
        """Create default translation files"""
        translations_dir = Path("translations")
        
        # Arabic translations (default)
        arabic_translations = {
            "app_title": "محلل العقود القانونية الكويتية",
            "app_subtitle": "منصة شاملة لتحليل العقود وفقاً للقانون الكويتي",
            "login": "تسجيل الدخول",
            "logout": "تسجيل الخروج",
            "username": "اسم المستخدم",
            "password": "كلمة المرور",
            "welcome": "مرحباً",
            "dashboard": "لوحة التحكم",
            "contracts": "العقود",
            "analysis": "التحليل",
            "reports": "التقارير",
            "settings": "الإعدادات",
            "help": "المساعدة",
            "upload_file": "رفع ملف",
            "analyze_contract": "تحليل العقد",
            "risk_assessment": "تقييم المخاطر",
            "legal_compliance": "الامتثال القانوني",
            "recommendations": "التوصيات",
            "export": "تصدير",
            "save": "حفظ",
            "cancel": "إلغاء",
            "delete": "حذف",
            "edit": "تعديل",
            "view": "عرض",
            "search": "بحث",
            "filter": "تصفية",
            "sort": "ترتيب",
            "date": "التاريخ",
            "time": "الوقت",
            "user": "المستخدم",
            "admin": "مدير",
            "status": "الحالة",
            "active": "نشط",
            "inactive": "غير نشط",
            "pending": "معلق",
            "approved": "موافق عليه",
            "rejected": "مرفوض",
            "high_risk": "مخاطر عالية",
            "medium_risk": "مخاطر متوسطة",
            "low_risk": "مخاطر منخفضة",
            "contract_types": {
                "employment": "عقد عمل",
                "commercial": "عقد تجاري",
                "real_estate": "عقد عقاري",
                "service": "عقد خدمة",
                "partnership": "عقد شراكة",
                "government": "عقد حكومي"
            },
            "legal_laws": {
                "civil_code": "القانون المدني 67/1980",
                "labor_law": "قانون العمل 6/2010",
                "commercial_law": "القانون التجاري 68/1980"
            },
            "navigation": {
                "home": "الرئيسية",
                "analyze": "تحليل",
                "history": "السجل",
                "templates": "القوالب",
                "collaboration": "التعاون",
                "reports": "التقارير",
                "risk_analysis": "تحليل المخاطر",
                "advanced_analysis": "التحليل المتقدم",
                "mobile_dashboard": "لوحة التحكم المحمولة",
                "api_docs": "وثائق API",
                "ai_insights": "تحليلات الذكاء الاصطناعي",
                "monitoring": "مراقبة النظام",
                "admin": "إدارة المستخدمين",
                "system_config": "إعدادات النظام",
                "maintenance": "أدوات الصيانة",
                "system_logs": "سجلات النظام"
            },
            "messages": {
                "success": "تم بنجاح",
                "error": "حدث خطأ",
                "warning": "تحذير",
                "info": "معلومات",
                "loading": "جاري التحميل...",
                "processing": "جاري المعالجة...",
                "analyzing": "جاري التحليل...",
                "uploading": "جاري الرفع...",
                "saving": "جاري الحفظ...",
                "login_success": "تم تسجيل الدخول بنجاح",
                "login_failed": "فشل تسجيل الدخول",
                "file_uploaded": "تم رفع الملف بنجاح",
                "analysis_complete": "تم التحليل بنجاح",
                "invalid_file": "ملف غير صالح",
                "file_too_large": "الملف كبير جداً",
                "no_text_found": "لم يتم العثور على نص",
                "ai_error": "خطأ في الذكاء الاصطناعي",
                "database_error": "خطأ في قاعدة البيانات",
                "network_error": "خطأ في الشبكة",
                "permission_denied": "ليس لديك صلاحية"
            },
            "forms": {
                "contract_title": "عنوان العقد",
                "contract_type": "نوع العقد",
                "contract_text": "نص العقد",
                "upload_instructions": "اسحب الملف هنا أو انقر للاختيار",
                "supported_formats": "الصيغ المدعومة: TXT, DOC, DOCX, PDF",
                "max_file_size": "الحد الأقصى للملف: 50 ميجابايت",
                "required_field": "حقل مطلوب",
                "optional_field": "حقل اختياري"
            }
        }
        
        # English translations
        english_translations = {
            "app_title": "Kuwaiti Legal Contract Analyzer",
            "app_subtitle": "Comprehensive platform for contract analysis according to Kuwaiti law",
            "login": "Login",
            "logout": "Logout",
            "username": "Username",
            "password": "Password",
            "welcome": "Welcome",
            "dashboard": "Dashboard",
            "contracts": "Contracts",
            "analysis": "Analysis",
            "reports": "Reports",
            "settings": "Settings",
            "help": "Help",
            "upload_file": "Upload File",
            "analyze_contract": "Analyze Contract",
            "risk_assessment": "Risk Assessment",
            "legal_compliance": "Legal Compliance",
            "recommendations": "Recommendations",
            "export": "Export",
            "save": "Save",
            "cancel": "Cancel",
            "delete": "Delete",
            "edit": "Edit",
            "view": "View",
            "search": "Search",
            "filter": "Filter",
            "sort": "Sort",
            "date": "Date",
            "time": "Time",
            "user": "User",
            "admin": "Admin",
            "status": "Status",
            "active": "Active",
            "inactive": "Inactive",
            "pending": "Pending",
            "approved": "Approved",
            "rejected": "Rejected",
            "high_risk": "High Risk",
            "medium_risk": "Medium Risk",
            "low_risk": "Low Risk",
            "contract_types": {
                "employment": "Employment Contract",
                "commercial": "Commercial Contract",
                "real_estate": "Real Estate Contract",
                "service": "Service Contract",
                "partnership": "Partnership Agreement",
                "government": "Government Contract"
            },
            "legal_laws": {
                "civil_code": "Civil Code 67/1980",
                "labor_law": "Labor Law 6/2010",
                "commercial_law": "Commercial Law 68/1980"
            },
            "navigation": {
                "home": "Home",
                "analyze": "Analyze",
                "history": "History",
                "templates": "Templates",
                "collaboration": "Collaboration",
                "reports": "Reports",
                "risk_analysis": "Risk Analysis",
                "advanced_analysis": "Advanced Analysis",
                "mobile_dashboard": "Mobile Dashboard",
                "api_docs": "API Documentation",
                "ai_insights": "AI Insights",
                "monitoring": "System Monitoring",
                "admin": "User Management",
                "system_config": "System Configuration",
                "maintenance": "Maintenance Tools",
                "system_logs": "System Logs"
            },
            "messages": {
                "success": "Success",
                "error": "Error",
                "warning": "Warning",
                "info": "Information",
                "loading": "Loading...",
                "processing": "Processing...",
                "analyzing": "Analyzing...",
                "uploading": "Uploading...",
                "saving": "Saving...",
                "login_success": "Login successful",
                "login_failed": "Login failed",
                "file_uploaded": "File uploaded successfully",
                "analysis_complete": "Analysis completed successfully",
                "invalid_file": "Invalid file",
                "file_too_large": "File too large",
                "no_text_found": "No text found",
                "ai_error": "AI processing error",
                "database_error": "Database error",
                "network_error": "Network error",
                "permission_denied": "Permission denied"
            },
            "forms": {
                "contract_title": "Contract Title",
                "contract_type": "Contract Type",
                "contract_text": "Contract Text",
                "upload_instructions": "Drag file here or click to select",
                "supported_formats": "Supported formats: TXT, DOC, DOCX, PDF",
                "max_file_size": "Maximum file size: 50 MB",
                "required_field": "Required field",
                "optional_field": "Optional field"
            }
        }
        
        # Save translation files
        with open(translations_dir / "ar.json", 'w', encoding='utf-8') as f:
            json.dump(arabic_translations, f, ensure_ascii=False, indent=2)
        
        with open(translations_dir / "en.json", 'w', encoding='utf-8') as f:
            json.dump(english_translations, f, ensure_ascii=False, indent=2)
    
    def set_language(self, language_code: str):
        """Set current language"""
        if language_code in self.translations:
            self.current_language = language_code
            # Store in session state
            if 'language' not in st.session_state:
                st.session_state.language = language_code
            else:
                st.session_state.language = language_code
        else:
            st.error(f"Language {language_code} not supported")
    
    def get_text(self, key: str, default: str = None) -> str:
        """Get translated text for key"""
        # Handle nested keys (e.g., "navigation.home")
        keys = key.split('.')
        
        try:
            value = self.translations[self.current_language]
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            # Fallback to default language
            try:
                value = self.translations[self.default_language]
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default or key
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages"""
        return {
            "ar": "العربية",
            "en": "English"
        }
    
    def is_rtl(self) -> bool:
        """Check if current language is RTL"""
        rtl_languages = ["ar", "he", "fa", "ur"]
        return self.current_language in rtl_languages

class LocalizationUI:
    """UI components for localization"""
    
    def __init__(self, lang_manager: LanguageManager):
        self.lang_manager = lang_manager
    
    def language_selector(self):
        """Display language selector"""
        available_languages = self.lang_manager.get_available_languages()
        
        # Get current language from session state
        current_lang = st.session_state.get('language', self.lang_manager.default_language)
        
        # Language selector in sidebar
        with st.sidebar:
            st.markdown("---")
            selected_lang = st.selectbox(
                "🌐 Language / اللغة",
                options=list(available_languages.keys()),
                format_func=lambda x: available_languages[x],
                index=list(available_languages.keys()).index(current_lang),
                key="language_selector"
            )
            
            if selected_lang != current_lang:
                self.lang_manager.set_language(selected_lang)
                st.rerun()
    
    def localized_title(self, title_key: str):
        """Display localized title"""
        title = self.lang_manager.get_text(title_key)
        st.title(title)
    
    def localized_header(self, header_key: str):
        """Display localized header"""
        header = self.lang_manager.get_text(header_key)
        st.header(header)
    
    def localized_subheader(self, subheader_key: str):
        """Display localized subheader"""
        subheader = self.lang_manager.get_text(subheader_key)
        st.subheader(subheader)
    
    def localized_button(self, button_key: str, **kwargs) -> bool:
        """Display localized button"""
        button_text = self.lang_manager.get_text(button_key)
        return st.button(button_text, **kwargs)
    
    def localized_selectbox(self, label_key: str, options_key: str = None, **kwargs):
        """Display localized selectbox"""
        label = self.lang_manager.get_text(label_key)
        
        if options_key:
            options_dict = self.lang_manager.get_text(options_key)
            if isinstance(options_dict, dict):
                options = list(options_dict.keys())
                format_func = lambda x: options_dict.get(x, x)
                return st.selectbox(label, options, format_func=format_func, **kwargs)
        
        return st.selectbox(label, **kwargs)
    
    def localized_text_input(self, label_key: str, **kwargs):
        """Display localized text input"""
        label = self.lang_manager.get_text(label_key)
        return st.text_input(label, **kwargs)
    
    def localized_text_area(self, label_key: str, **kwargs):
        """Display localized text area"""
        label = self.lang_manager.get_text(label_key)
        return st.text_area(label, **kwargs)
    
    def localized_file_uploader(self, label_key: str, **kwargs):
        """Display localized file uploader"""
        label = self.lang_manager.get_text(label_key)
        help_text = self.lang_manager.get_text("forms.upload_instructions")
        return st.file_uploader(label, help=help_text, **kwargs)
    
    def localized_message(self, message_type: str, message_key: str):
        """Display localized message"""
        message = self.lang_manager.get_text(f"messages.{message_key}")
        
        if message_type == "success":
            st.success(message)
        elif message_type == "error":
            st.error(message)
        elif message_type == "warning":
            st.warning(message)
        elif message_type == "info":
            st.info(message)
    
    def get_rtl_css(self) -> str:
        """Get RTL CSS if current language is RTL"""
        if self.lang_manager.is_rtl():
            return """
            <style>
            .main .block-container {
                direction: rtl;
                text-align: right;
            }
            
            .stSelectbox > div > div {
                direction: rtl;
                text-align: right;
            }
            
            .stTextInput > div > div > input {
                direction: rtl;
                text-align: right;
            }
            
            .stTextArea > div > div > textarea {
                direction: rtl;
                text-align: right;
            }
            
            .stButton > button {
                direction: rtl;
            }
            
            .stMarkdown {
                direction: rtl;
                text-align: right;
            }
            
            .stDataFrame {
                direction: ltr;
            }
            
            .stPlotlyChart {
                direction: ltr;
            }
            </style>
            """
        return ""

# Global language manager instance
lang_manager = LanguageManager()
localization_ui = LocalizationUI(lang_manager)

def get_language_manager() -> LanguageManager:
    """Get global language manager instance"""
    return lang_manager

def get_localization_ui() -> LocalizationUI:
    """Get global localization UI instance"""
    return localization_ui

def t(key: str, default: str = None) -> str:
    """Shorthand function for getting translated text"""
    return lang_manager.get_text(key, default)
