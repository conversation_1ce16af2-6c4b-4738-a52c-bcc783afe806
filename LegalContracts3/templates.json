{"49e6fb9a-3d43-48bb-ab86-e031e9d5096c": {"id": "49e6fb9a-3d43-48bb-ab86-e031e9d5096c", "title": "عقد عمل أساسي - القطاع الخاص", "description": "نموذج عقد عمل للقطاع الخاص وفقاً لقانون العمل الكويتي رقم 6/2010", "content": "عقد عمل\n\nبين: {{employer_name}} (صاح<PERSON> العمل)\nالعنوان: {{employer_address}}\nالرقم المدني: {{employer_civil_id}}\n\nوبين: {{employee_name}} (العامل)\nالعنوان: {{employee_address}}\nالرقم المدني: {{employee_civil_id}}\nالجنسية: {{employee_nationality}}\n\nالمادة الأولى: طبيعة العمل\nيتعهد العامل بأداء العمل التالي: {{job_description}}\nفي {{work_location}}\n\nالمادة الثانية: مدة العقد\nمدة هذا العقد {{contract_duration}} اعتباراً من {{start_date}}\n\nالمادة الثالثة: الراتب والمزايا\nالراتب الأساسي: {{basic_salary}} دينار كويتي شهرياً\nالبدلات: {{allowances}}\n\nالمادة الرابعة: ساعات العمل\nساعات العمل اليومية: {{daily_hours}} ساعات\nأيام العمل الأسبوعية: {{weekly_days}} أيام\n\nالمادة الخامسة: الإجازات\nالإجازة السنوية: {{annual_leave}} يوماً\nالإجازة المرضية: وفقاً لقانون العمل الكويتي\n\nالمادة السادسة: إنهاء العقد\nيمكن إنهاء هذا العقد وفقاً لأحكام قانون العمل الكويتي رقم 6/2010\n\nالتوقيع:\nصاحب العمل: ________________\nالعامل: ________________\nالتاريخ: {{signature_date}}", "category": "employment", "complexity": "basic", "variables": ["employer_name", "employer_address", "employer_civil_id", "employee_name", "employee_address", "employee_civil_id", "employee_nationality", "job_description", "work_location", "contract_duration", "start_date", "basic_salary", "allowances", "daily_hours", "weekly_days", "annual_leave", "signature_date"], "tags": ["<PERSON><PERSON><PERSON>", "قطاع خاص", "أساسي"], "legal_references": ["قانون العمل الكويتي رقم 6/2010"], "created_by": "system", "created_at": "2025-09-05T14:27:45.138953", "updated_at": "2025-09-05T14:27:45.138964", "usage_count": 0, "is_active": true}, "d69c8665-a228-49e3-a77d-38754a2eb537": {"id": "d69c8665-a228-49e3-a77d-38754a2eb537", "title": "عق<PERSON> بيع عقار", "description": "نموذج عقد بيع عقار وفقاً للقانون المدني الكويتي", "content": "عقد بيع عقار\n\nالبائع: {{seller_name}}\nالرقم المدني: {{seller_civil_id}}\nالعنوان: {{seller_address}}\n\nالمشتري: {{buyer_name}}\nالرقم المدني: {{buyer_civil_id}}\nالعنوان: {{buyer_address}}\n\nوصف العقار:\nالموقع: {{property_location}}\nالمساحة: {{property_area}} متر مربع\nرقم القطعة: {{plot_number}}\nرقم القسيمة: {{parcel_number}}\n\nالثمن: {{sale_price}} دينار كويتي\n\nشروط الدفع: {{payment_terms}}\n\nالتسليم: {{delivery_date}}\n\nالضمانات: {{warranties}}\n\nالتوقيع:\nالبائع: ________________\nالمشتري: ________________\nالتاريخ: {{signature_date}}", "category": "real_estate", "complexity": "intermediate", "variables": ["seller_name", "seller_civil_id", "seller_address", "buyer_name", "buyer_civil_id", "buyer_address", "property_location", "property_area", "plot_number", "parcel_number", "sale_price", "payment_terms", "delivery_date", "warranties", "signature_date"], "tags": ["<PERSON><PERSON><PERSON><PERSON>", "بيع", "ملكية"], "legal_references": ["القانون المدني الكويتي رقم 67/1980"], "created_by": "system", "created_at": "2025-09-05T14:27:45.139600", "updated_at": "2025-09-05T14:27:45.139604", "usage_count": 0, "is_active": true}, "0c2f8903-658b-4dc6-a041-846189629a1f": {"id": "0c2f8903-658b-4dc6-a041-846189629a1f", "title": "عق<PERSON> خدمات استشارية", "description": "نموذج عقد تقديم خدمات استشارية", "content": "عقد خدمات استشارية\n\nمقدم الخدمة: {{service_provider}}\nالرقم التجاري: {{commercial_registration}}\nالعنوان: {{provider_address}}\n\nالعميل: {{client_name}}\nالعنوان: {{client_address}}\n\nنطاق الخدمات: {{service_scope}}\n\nمدة العقد: {{contract_duration}}\n\nالأتعاب: {{fees}} دينار كويتي\n\nشروط الدفع: {{payment_terms}}\n\nالمسؤوليات:\nمقدم الخدمة: {{provider_responsibilities}}\nالعميل: {{client_responsibilities}}\n\nالسرية: {{confidentiality_clause}}\n\nالتوقيع:\nمقدم الخدمة: ________________\nالعميل: ________________\nالتاريخ: {{signature_date}}", "category": "service", "complexity": "intermediate", "variables": ["service_provider", "commercial_registration", "provider_address", "client_name", "client_address", "service_scope", "contract_duration", "fees", "payment_terms", "provider_responsibilities", "client_responsibilities", "confidentiality_clause", "signature_date"], "tags": ["خدمات", "استشارة", "مهني"], "legal_references": ["القانون المدني الكويتي رقم 67/1980", "القانون التجاري الكويتي رقم 68/1980"], "created_by": "system", "created_at": "2025-09-05T14:27:45.139944", "updated_at": "2025-09-05T14:27:45.139950", "usage_count": 1, "is_active": true}}