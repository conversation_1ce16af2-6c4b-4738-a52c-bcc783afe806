#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legal Frameworks Module
Comprehensive legal framework support for Kuwait and Saudi Arabia
"""

from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class LegalSystem(Enum):
    """Supported legal systems"""
    KUWAIT = "kuwait"
    SAUDI_ARABIA = "saudi_arabia"

@dataclass
class LegalReference:
    """Legal reference structure"""
    article: str
    law_name: str
    description: str
    category: str

class KuwaitLegalFramework:
    """Kuwait legal framework implementation"""
    
    CIVIL_CODE_REFERENCES = {
        "contract_formation": LegalReference(
            article="المواد 67-89",
            law_name="القانون المدني الكويتي رقم 67/1980",
            description="تكوين العقد وشروط صحته",
            category="civil"
        ),
        "obligations": LegalReference(
            article="المواد 203-456",
            law_name="القانون المدني الكويتي رقم 67/1980",
            description="الالتزامات والعقود",
            category="civil"
        ),
        "liability": LegalReference(
            article="المواد 227-243",
            law_name="القانون المدني الكويتي رقم 67/1980",
            description="المسؤولية المدنية",
            category="civil"
        ),
        "termination": LegalReference(
            article="المواد 189-202",
            law_name="القانون المدني الكويتي رقم 67/1980",
            description="انقضاء الالتزام",
            category="civil"
        )
    }
    
    COMMERCIAL_CODE_REFERENCES = {
        "commercial_contracts": LegalReference(
            article="المواد 1-50",
            law_name="القانون التجاري الكويتي رقم 68/1980",
            description="العقود التجارية",
            category="commercial"
        ),
        "company_law": LegalReference(
            article="المواد 51-200",
            law_name="القانون التجاري الكويتي رقم 68/1980",
            description="قانون الشركات",
            category="commercial"
        ),
        "commercial_transactions": LegalReference(
            article="المواد 201-300",
            law_name="القانون التجاري الكويتي رقم 68/1980",
            description="المعاملات التجارية",
            category="commercial"
        )
    }
    
    LABOR_LAW_REFERENCES = {
        "employment_contracts": LegalReference(
            article="المواد 1-30",
            law_name="قانون العمل في القطاع الأهلي رقم 6/2010",
            description="عقود العمل",
            category="labor"
        ),
        "worker_rights": LegalReference(
            article="المواد 31-60",
            law_name="قانون العمل في القطاع الأهلي رقم 6/2010",
            description="حقوق العامل",
            category="labor"
        ),
        "termination_procedures": LegalReference(
            article="المواد 61-80",
            law_name="قانون العمل في القطاع الأهلي رقم 6/2010",
            description="إنهاء عقد العمل",
            category="labor"
        )
    }

class SaudiLegalFramework:
    """Saudi Arabia legal framework implementation"""
    
    CIVIL_CODE_REFERENCES = {
        "contract_formation": LegalReference(
            article="المواد 1-50",
            law_name="نظام المعاملات المدنية السعودي",
            description="تكوين العقد وأركانه",
            category="civil"
        ),
        "obligations": LegalReference(
            article="المواد 51-200",
            law_name="نظام المعاملات المدنية السعودي",
            description="الالتزامات والعقود",
            category="civil"
        ),
        "liability": LegalReference(
            article="المواد 201-250",
            law_name="نظام المعاملات المدنية السعودي",
            description="المسؤولية المدنية",
            category="civil"
        ),
        "termination": LegalReference(
            article="المواد 251-300",
            law_name="نظام المعاملات المدنية السعودي",
            description="انقضاء الالتزام",
            category="civil"
        )
    }
    
    COMMERCIAL_CODE_REFERENCES = {
        "commercial_contracts": LegalReference(
            article="المواد 1-100",
            law_name="نظام الشركات السعودي",
            description="العقود التجارية",
            category="commercial"
        ),
        "company_law": LegalReference(
            article="المواد 101-300",
            law_name="نظام الشركات السعودي",
            description="أحكام الشركات",
            category="commercial"
        ),
        "commercial_transactions": LegalReference(
            article="المواد 1-50",
            law_name="نظام التجارة السعودي",
            description="المعاملات التجارية",
            category="commercial"
        ),
        "investment_law": LegalReference(
            article="المواد 1-40",
            law_name="نظام الاستثمار الأجنبي",
            description="الاستثمار الأجنبي",
            category="investment"
        )
    }
    
    LABOR_LAW_REFERENCES = {
        "employment_contracts": LegalReference(
            article="المواد 50-70",
            law_name="نظام العمل السعودي",
            description="عقود العمل",
            category="labor"
        ),
        "worker_rights": LegalReference(
            article="المواد 71-120",
            law_name="نظام العمل السعودي",
            description="حقوق وواجبات العامل",
            category="labor"
        ),
        "termination_procedures": LegalReference(
            article="المواد 121-150",
            law_name="نظام العمل السعودي",
            description="إنهاء عقد العمل",
            category="labor"
        ),
        "saudization": LegalReference(
            article="المواد 1-20",
            law_name="نظام نطاقات",
            description="توطين الوظائف",
            category="labor"
        )
    }
    
    SHARIA_COMPLIANCE = {
        "islamic_finance": LegalReference(
            article="المواد 1-30",
            law_name="لائحة أعمال التمويل",
            description="أحكام التمويل الإسلامي",
            category="sharia"
        ),
        "prohibited_transactions": LegalReference(
            article="الأحكام الشرعية",
            law_name="الضوابط الشرعية",
            description="المعاملات المحرمة شرعاً",
            category="sharia"
        ),
        "halal_business": LegalReference(
            article="الأحكام الشرعية",
            law_name="ضوابط الأعمال الحلال",
            description="ضوابط الأعمال المشروعة",
            category="sharia"
        )
    }

class LegalFrameworkManager:
    """Manager for all legal frameworks"""
    
    def __init__(self):
        self.kuwait = KuwaitLegalFramework()
        self.saudi = SaudiLegalFramework()
        self.current_system = LegalSystem.KUWAIT
    
    def set_legal_system(self, system: LegalSystem):
        """Set the current legal system"""
        self.current_system = system
    
    def get_current_framework(self):
        """Get the current legal framework"""
        if self.current_system == LegalSystem.KUWAIT:
            return self.kuwait
        elif self.current_system == LegalSystem.SAUDI_ARABIA:
            return self.saudi
        else:
            raise ValueError(f"Unsupported legal system: {self.current_system}")
    
    def get_legal_reference(self, topic: str, category: str = None) -> LegalReference:
        """Get legal reference for a specific topic"""
        framework = self.get_current_framework()
        
        # Search in all reference categories
        all_references = {}
        if hasattr(framework, 'CIVIL_CODE_REFERENCES'):
            all_references.update(framework.CIVIL_CODE_REFERENCES)
        if hasattr(framework, 'COMMERCIAL_CODE_REFERENCES'):
            all_references.update(framework.COMMERCIAL_CODE_REFERENCES)
        if hasattr(framework, 'LABOR_LAW_REFERENCES'):
            all_references.update(framework.LABOR_LAW_REFERENCES)
        if hasattr(framework, 'SHARIA_COMPLIANCE'):
            all_references.update(framework.SHARIA_COMPLIANCE)
        
        return all_references.get(topic)
    
    def get_system_name(self) -> str:
        """Get the current legal system name"""
        if self.current_system == LegalSystem.KUWAIT:
            return "القانون الكويتي"
        elif self.current_system == LegalSystem.SAUDI_ARABIA:
            return "النظام السعودي"
        else:
            return "نظام قانوني غير محدد"
    
    def get_system_description(self) -> str:
        """Get description of current legal system"""
        if self.current_system == LegalSystem.KUWAIT:
            return "النظام القانوني الكويتي المبني على القانون المدني والتجاري"
        elif self.current_system == LegalSystem.SAUDI_ARABIA:
            return "النظام القانوني السعودي المبني على الشريعة الإسلامية والأنظمة الحديثة"
        else:
            return "نظام قانوني غير محدد"
    
    def get_available_systems(self) -> Dict[str, str]:
        """Get available legal systems"""
        return {
            LegalSystem.KUWAIT.value: "🇰🇼 القانون الكويتي",
            LegalSystem.SAUDI_ARABIA.value: "🇸🇦 النظام السعودي"
        }
    
    def get_analysis_prompt_prefix(self) -> str:
        """Get system-specific analysis prompt prefix"""
        if self.current_system == LegalSystem.KUWAIT:
            return """أنت محامي كويتي متخصص في القانون المدني والتجاري الكويتي.
            لديك خبرة عميقة في القانون المدني رقم 67/1980 والقانون التجاري رقم 68/1980.
            قم بتحليل العقود وفقاً للقوانين الكويتية."""
        elif self.current_system == LegalSystem.SAUDI_ARABIA:
            return """أنت محامي سعودي متخصص في الأنظمة السعودية والشريعة الإسلامية.
            لديك خبرة عميقة في نظام المعاملات المدنية ونظام الشركات ونظام العمل السعودي.
            قم بتحليل العقود وفقاً للأنظمة السعودية والضوابط الشرعية."""
        else:
            return "أنت محامي متخصص في تحليل العقود."

    def get_legal_context(self, legal_system: str = None) -> str:
        """Get legal context for the specified or current legal system"""
        if legal_system:
            # Convert string to enum if needed
            if legal_system == 'kuwait':
                system = LegalSystem.KUWAIT
            elif legal_system == 'saudi_arabia' or legal_system == 'saudi':
                system = LegalSystem.SAUDI_ARABIA
            else:
                system = self.current_system
        else:
            system = self.current_system

        if system == LegalSystem.KUWAIT:
            return """السياق القانوني الكويتي:
            - القانون المدني الكويتي رقم 67 لسنة 1980
            - القانون التجاري الكويتي رقم 68 لسنة 1980
            - قانون العمل الكويتي رقم 6 لسنة 2010
            - قانون الشركات التجارية رقم 1 لسنة 2016
            - أحكام الشريعة الإسلامية كمصدر احتياطي للقانون
            """
        elif system == LegalSystem.SAUDI_ARABIA:
            return """السياق القانوني السعودي:
            - نظام المعاملات المدنية السعودي
            - نظام الشركات السعودي لعام 2015
            - نظام العمل السعودي لعام 2005
            - نظام المحكمة التجارية
            - أحكام الشريعة الإسلامية كمصدر أساسي للتشريع
            - رؤية المملكة 2030 والتطوير الاقتصادي
            """
        else:
            return "السياق القانوني العام للعقود والمعاملات التجارية"

# Global instance
legal_framework_manager = LegalFrameworkManager()

def get_legal_framework_manager() -> LegalFrameworkManager:
    """Get global legal framework manager instance"""
    return legal_framework_manager
