#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Collaboration System for Enhanced Legal Contract Analyzer
Team collaboration, comments, and review workflows
"""

import sqlite3
import uuid
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
import logging

logger = logging.getLogger(__name__)

class CollaborationSystem:
    """Team collaboration and review system"""
    
    def __init__(self, db_path: str = "legal_analyzer.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize collaboration database tables"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Comments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS contract_comments (
                    id TEXT PRIMARY KEY,
                    contract_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    comment_text TEXT NOT NULL,
                    comment_type TEXT DEFAULT 'general',
                    section_reference TEXT,
                    status TEXT DEFAULT 'open',
                    priority TEXT DEFAULT 'medium',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_by TEXT,
                    resolved_at TIMESTAMP,
                    parent_comment_id TEXT,
                    attachments TEXT DEFAULT '[]'
                )
            """)
            
            # Review workflows table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS review_workflows (
                    id TEXT PRIMARY KEY,
                    contract_id TEXT NOT NULL,
                    workflow_name TEXT NOT NULL,
                    created_by TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    reviewers TEXT NOT NULL,
                    deadline TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    workflow_data TEXT DEFAULT '{}'
                )
            """)
            
            # Review assignments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS review_assignments (
                    id TEXT PRIMARY KEY,
                    workflow_id TEXT NOT NULL,
                    reviewer_id TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    review_notes TEXT,
                    approval_status TEXT,
                    FOREIGN KEY (workflow_id) REFERENCES review_workflows (id)
                )
            """)
            
            # Team notifications table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    notification_type TEXT DEFAULT 'info',
                    related_id TEXT,
                    is_read BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP
                )
            """)
            
            # Contract sharing table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS contract_sharing (
                    id TEXT PRIMARY KEY,
                    contract_id TEXT NOT NULL,
                    shared_by TEXT NOT NULL,
                    shared_with TEXT NOT NULL,
                    permission_level TEXT DEFAULT 'view',
                    shared_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    access_count INTEGER DEFAULT 0,
                    last_accessed TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            logger.info("Collaboration database initialized successfully")
            
        except Exception as e:
            logger.error(f"Collaboration database initialization error: {e}")
            raise
    
    def add_comment(self, contract_id: str, user_id: str, comment_text: str,
                   comment_type: str = "general", section_reference: str = "",
                   priority: str = "medium", parent_comment_id: str = None) -> str:
        """Add comment to contract"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            comment_id = str(uuid.uuid4())
            
            cursor.execute("""
                INSERT INTO contract_comments (
                    id, contract_id, user_id, comment_text, comment_type,
                    section_reference, priority, parent_comment_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                comment_id, contract_id, user_id, comment_text, comment_type,
                section_reference, priority, parent_comment_id
            ))
            
            conn.commit()
            conn.close()
            
            # Create notification for contract owner and reviewers
            self._notify_comment_added(contract_id, user_id, comment_text)
            
            return comment_id
            
        except Exception as e:
            logger.error(f"Error adding comment: {e}")
            return ""
    
    def get_contract_comments(self, contract_id: str) -> List[Dict[str, Any]]:
        """Get all comments for a contract"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT c.id, c.user_id, c.comment_text, c.comment_type,
                       c.section_reference, c.status, c.priority, c.created_at,
                       c.parent_comment_id, u.full_name, u.username
                FROM contract_comments c
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.contract_id = ?
                ORDER BY c.created_at DESC
            """, (contract_id,))
            
            comments = []
            for row in cursor.fetchall():
                comments.append({
                    'id': row[0],
                    'user_id': row[1],
                    'comment_text': row[2],
                    'comment_type': row[3],
                    'section_reference': row[4],
                    'status': row[5],
                    'priority': row[6],
                    'created_at': row[7],
                    'parent_comment_id': row[8],
                    'author_name': row[9] or row[10],
                    'username': row[10]
                })
            
            conn.close()
            return comments
            
        except Exception as e:
            logger.error(f"Error getting comments: {e}")
            return []
    
    def create_review_workflow(self, contract_id: str, workflow_name: str,
                              created_by: str, reviewers: List[str],
                              deadline: str = None) -> str:
        """Create review workflow"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            workflow_id = str(uuid.uuid4())
            reviewers_json = json.dumps(reviewers)
            
            cursor.execute("""
                INSERT INTO review_workflows (
                    id, contract_id, workflow_name, created_by, reviewers, deadline
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                workflow_id, contract_id, workflow_name, created_by,
                reviewers_json, deadline
            ))
            
            # Create review assignments
            for reviewer_id in reviewers:
                assignment_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO review_assignments (
                        id, workflow_id, reviewer_id
                    ) VALUES (?, ?, ?)
                """, (assignment_id, workflow_id, reviewer_id))
                
                # Notify reviewer
                self._create_notification(
                    reviewer_id,
                    "مراجعة عقد جديدة",
                    f"تم تعيينك لمراجعة عقد: {workflow_name}",
                    "review_assignment",
                    workflow_id
                )
            
            conn.commit()
            conn.close()
            
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating review workflow: {e}")
            return ""
    
    def get_user_notifications(self, user_id: str, unread_only: bool = False) -> List[Dict[str, Any]]:
        """Get user notifications"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = """
                SELECT id, title, message, notification_type, related_id,
                       is_read, created_at
                FROM notifications
                WHERE user_id = ?
            """
            
            if unread_only:
                query += " AND is_read = 0"
            
            query += " ORDER BY created_at DESC LIMIT 50"
            
            cursor.execute(query, (user_id,))
            
            notifications = []
            for row in cursor.fetchall():
                notifications.append({
                    'id': row[0],
                    'title': row[1],
                    'message': row[2],
                    'type': row[3],
                    'related_id': row[4],
                    'is_read': bool(row[5]),
                    'created_at': row[6]
                })
            
            conn.close()
            return notifications
            
        except Exception as e:
            logger.error(f"Error getting notifications: {e}")
            return []
    
    def mark_notification_read(self, notification_id: str):
        """Mark notification as read"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE notifications SET is_read = 1 WHERE id = ?
            """, (notification_id,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
    
    def share_contract(self, contract_id: str, shared_by: str, shared_with: str,
                      permission_level: str = "view", expires_days: int = 30) -> str:
        """Share contract with user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            share_id = str(uuid.uuid4())
            expires_at = datetime.now() + timedelta(days=expires_days) if expires_days else None
            
            cursor.execute("""
                INSERT INTO contract_sharing (
                    id, contract_id, shared_by, shared_with, permission_level, expires_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                share_id, contract_id, shared_by, shared_with,
                permission_level, expires_at.isoformat() if expires_at else None
            ))
            
            conn.commit()
            conn.close()
            
            # Notify user about shared contract
            self._create_notification(
                shared_with,
                "عقد مشارك معك",
                f"تم مشاركة عقد معك بصلاحية {permission_level}",
                "contract_shared",
                contract_id
            )
            
            return share_id
            
        except Exception as e:
            logger.error(f"Error sharing contract: {e}")
            return ""
    
    def get_shared_contracts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get contracts shared with user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT cs.contract_id, cs.permission_level, cs.shared_at,
                       cs.expires_at, u.full_name as shared_by_name
                FROM contract_sharing cs
                LEFT JOIN users u ON cs.shared_by = u.id
                WHERE cs.shared_with = ?
                AND (cs.expires_at IS NULL OR cs.expires_at > datetime('now'))
                ORDER BY cs.shared_at DESC
            """, (user_id,))
            
            shared_contracts = []
            for row in cursor.fetchall():
                shared_contracts.append({
                    'contract_id': row[0],
                    'permission_level': row[1],
                    'shared_at': row[2],
                    'expires_at': row[3],
                    'shared_by_name': row[4]
                })
            
            conn.close()
            return shared_contracts
            
        except Exception as e:
            logger.error(f"Error getting shared contracts: {e}")
            return []
    
    def _create_notification(self, user_id: str, title: str, message: str,
                           notification_type: str = "info", related_id: str = None):
        """Create notification for user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            notification_id = str(uuid.uuid4())
            
            cursor.execute("""
                INSERT INTO notifications (
                    id, user_id, title, message, notification_type, related_id
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                notification_id, user_id, title, message, notification_type, related_id
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
    
    def _notify_comment_added(self, contract_id: str, commenter_id: str, comment_text: str):
        """Notify relevant users about new comment"""
        # This would notify contract owner and other reviewers
        # Implementation depends on contract ownership structure
        pass
    
    def get_collaboration_statistics(self) -> Dict[str, Any]:
        """Get collaboration statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total comments
            cursor.execute("SELECT COUNT(*) FROM contract_comments")
            total_comments = cursor.fetchone()[0]
            
            # Active workflows
            cursor.execute("SELECT COUNT(*) FROM review_workflows WHERE status = 'active'")
            active_workflows = cursor.fetchone()[0]
            
            # Shared contracts
            cursor.execute("SELECT COUNT(*) FROM contract_sharing WHERE expires_at > datetime('now') OR expires_at IS NULL")
            shared_contracts = cursor.fetchone()[0]
            
            # Recent activity (last 7 days)
            cursor.execute("""
                SELECT COUNT(*) FROM contract_comments 
                WHERE created_at >= datetime('now', '-7 days')
            """)
            recent_comments = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_comments': total_comments,
                'active_workflows': active_workflows,
                'shared_contracts': shared_contracts,
                'recent_comments': recent_comments
            }
            
        except Exception as e:
            logger.error(f"Error getting collaboration statistics: {e}")
            return {}
