#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LM Studio Connection Test for Kuwaiti Legal Contract Analysis Application
"""

import requests
import json
import sys

def test_lmstudio_connection():
    """Test LM Studio connection and API"""
    print("🧪 اختبار اتصال LM Studio")
    print("   Testing LM Studio Connection")
    print("=" * 50)
    
    base_url = "http://localhost:1234"
    
    # Test 1: Check if server is running
    print("\n1. 🔍 اختبار الخادم / Testing Server...")
    try:
        response = requests.get(f"{base_url}/v1/models", timeout=10)
        if response.status_code == 200:
            print("✅ LM Studio server is running")
        else:
            print(f"❌ Server responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to LM Studio server")
        print("💡 Make sure LM Studio is running and server is started on port 1234")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test 2: Get available models
    print("\n2. 📋 الحصول على النماذج / Getting Available Models...")
    try:
        data = response.json()
        models = data.get('data', [])
        
        if not models:
            print("❌ No models loaded in LM Studio")
            print("💡 Please load a model in LM Studio Chat tab first")
            return False
        
        print(f"✅ Found {len(models)} model(s):")
        for i, model in enumerate(models, 1):
            print(f"   {i}. {model.get('id', 'unknown')}")
        
        # Select the first available model for testing
        test_model = models[0]['id']
        print(f"\n🎯 Using model for test: {test_model}")
        
    except Exception as e:
        print(f"❌ Error parsing models: {e}")
        return False
    
    # Test 3: Test chat completion
    print("\n3. 💬 اختبار المحادثة / Testing Chat Completion...")
    try:
        test_data = {
            "model": test_model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Respond in both Arabic and English."
                },
                {
                    "role": "user", 
                    "content": "Hello, can you respond in Arabic? مرحبا، هل يمكنك الرد بالعربية؟"
                }
            ],
            "temperature": 0.3,
            "max_tokens": 100,
            "stream": False
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{base_url}/v1/chat/completions", 
            json=test_data, 
            headers=headers,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print("✅ Chat completion successful!")
                print(f"📝 Response: {content[:100]}...")
                return True
            else:
                print(f"❌ Unexpected response format: {result}")
                return False
        else:
            print(f"❌ Chat completion failed with status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error in chat completion: {e}")
        return False

def test_contract_analyzer():
    """Test the ContractAnalyzer class with LM Studio"""
    print("\n4. 🏛️ اختبار محلل العقود / Testing Contract Analyzer...")
    
    try:
        from ai_backend import ContractAnalyzer
        
        # Create analyzer with LM Studio backend
        analyzer = ContractAnalyzer(backend="lmstudio")
        
        # Test connection
        if analyzer.test_connection():
            print("✅ ContractAnalyzer connection successful")
        else:
            print("❌ ContractAnalyzer connection failed")
            return False
        
        # Get available models
        models = analyzer.get_available_models()
        if models:
            print(f"✅ Available models: {', '.join(models)}")
        else:
            print("⚠️ No models found")
        
        # Test simple translation
        print("\n🔄 Testing translation...")
        test_text = "This is a simple contract clause for testing."

        try:
            result = analyzer.translate_to_arabic(test_text)
            if result and len(result) > 10:
                print("✅ Translation test successful")
                print(f"📝 Sample: {result[:100]}...")
                return True
            else:
                print("❌ Translation test failed - empty or short response")
                return False
        except Exception as e:
            print(f"❌ Translation test error: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import ContractAnalyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ ContractAnalyzer test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🏛️ محلل العقود القانونية الكويتية - اختبار LM Studio")
    print("   Kuwaiti Legal Contract Analyzer - LM Studio Test")
    print("=" * 60)
    
    tests = [
        ("LM Studio Connection", test_lmstudio_connection),
        ("Contract Analyzer", test_contract_analyzer)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج / Test Summary")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! LM Studio is ready for use.")
        print("🚀 You can now run: streamlit run app.py")
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
        print("💡 Make sure LM Studio is running with a model loaded.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
