"""
User Authentication and Authorization System
Developed by MAXBIT LLC © 2025
"""

import streamlit as st
import hashlib
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import uuid

class UserRole:
    """User role definitions"""
    ADMIN = "admin"
    LAWYER = "lawyer"
    PARALEGAL = "paralegal"
    CLIENT = "client"

class UserManager:
    """Manage user authentication and authorization"""
    
    def __init__(self, users_file: str = "users.json"):
        self.users_file = users_file
        self.users = self._load_users()
        
    def _load_users(self) -> Dict[str, Any]:
        """Load users from file"""
        if os.path.exists(self.users_file):
            try:
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        # Create default admin user if no users exist
        default_users = {
            "admin": {
                "id": str(uuid.uuid4()),
                "username": "admin",
                "password_hash": self._hash_password("admin123"),
                "email": "<EMAIL>",
                "full_name": "مدير النظام",
                "role": UserRole.ADMIN,
                "created_at": datetime.now().isoformat(),
                "last_login": None,
                "is_active": True,
                "permissions": ["all"]
            }
        }
        self._save_users(default_users)
        return default_users
    
    def _save_users(self, users: Dict[str, Any]):
        """Save users to file"""
        with open(self.users_file, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        self.users = users
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user"""
        if username in self.users:
            user = self.users[username]
            if user["is_active"] and user["password_hash"] == self._hash_password(password):
                # Update last login
                user["last_login"] = datetime.now().isoformat()
                self._save_users(self.users)
                return user
        return None
    
    def create_user(self, username: str, password: str, email: str, full_name: str, 
                   role: str, created_by: str) -> bool:
        """Create new user"""
        if username in self.users:
            return False
        
        self.users[username] = {
            "id": str(uuid.uuid4()),
            "username": username,
            "password_hash": self._hash_password(password),
            "email": email,
            "full_name": full_name,
            "role": role,
            "created_at": datetime.now().isoformat(),
            "created_by": created_by,
            "last_login": None,
            "is_active": True,
            "permissions": self._get_default_permissions(role)
        }
        self._save_users(self.users)
        return True
    
    def _get_default_permissions(self, role: str) -> List[str]:
        """Get default permissions for role"""
        permissions = {
            UserRole.ADMIN: ["all"],
            UserRole.LAWYER: ["analyze", "export", "manage_clients", "view_history"],
            UserRole.PARALEGAL: ["analyze", "export", "view_history"],
            UserRole.CLIENT: ["analyze", "export"]
        }
        return permissions.get(role, ["analyze"])
    
    def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        return self.users.get(username)
    
    def update_user(self, username: str, updates: Dict[str, Any]) -> bool:
        """Update user information"""
        if username in self.users:
            self.users[username].update(updates)
            self._save_users(self.users)
            return True
        return False
    
    def delete_user(self, username: str) -> bool:
        """Delete user (deactivate)"""
        if username in self.users and username != "admin":
            self.users[username]["is_active"] = False
            self._save_users(self.users)
            return True
        return False
    
    def list_users(self) -> List[Dict[str, Any]]:
        """List all users"""
        return [user for user in self.users.values() if user["is_active"]]
    
    def has_permission(self, username: str, permission: str) -> bool:
        """Check if user has specific permission"""
        user = self.get_user(username)
        if not user:
            return False
        
        permissions = user.get("permissions", [])
        return "all" in permissions or permission in permissions

class AuthUI:
    """Authentication UI components"""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
    
    def login_form(self) -> Optional[Dict[str, Any]]:
        """LocusVMS style login using Streamlit columns"""

        # Hide Streamlit elements and set basic styling with Arabic support
        st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}
        .stDeployButton {visibility: hidden;}

        .stApp {
            background: #f8f9fa;
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
        }

        /* Fix Streamlit components for RTL */
        .stButton > button {
            direction: rtl;
            text-align: center;
        }

        .stTextInput > div > div > input {
            direction: rtl;
            text-align: right;
        }

        .main .block-container {
            padding: 0 !important;
            max-width: none !important;
        }

        .left-panel {
            background: #4a5568;
            color: white;
            padding: 4rem 2rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .right-panel {
            background: #f8f9fa;
            padding: 4rem 2rem;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: #4ade80;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 2rem auto;
            color: white;
        }

        .brand-title {
            font-size: 2.5rem;
            font-weight: 400;
            margin-bottom: 0.5rem;
            color: white;
        }

        .brand-subtitle {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
        }

        .brand-description {
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            max-width: 280px;
        }

        .form-title {
            font-size: 1.8rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-subtitle {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .field-label {
            font-size: 0.7rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .stTextInput > div > div > input {
            border: 1px solid #d1d5db !important;
            border-radius: 4px !important;
            padding: 0.75rem !important;
            font-size: 0.9rem !important;
            background: white !important;
            margin-bottom: 1rem !important;
        }

        .stButton > button {
            background: #374151 !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 0.75rem 2rem !important;
            font-weight: 600 !important;
            width: 100% !important;
            font-size: 0.8rem !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
        }
        </style>
        """, unsafe_allow_html=True)

        # Create two columns for split screen
        left_col, right_col = st.columns([1, 1])

        with left_col:
            st.markdown("""
            <div class="left-panel">
                <div class="logo-icon">📋</div>
                <h1 class="brand-title">ماكس بت القانونية</h1>
                <p class="brand-subtitle">نظام تحليل العقود</p>
                <p class="brand-description">
                    تطبيق قوي وسهل الاستخدام لإدارة تحليل العقود القانونية بتقنيات الذكاء الاصطناعي المتطورة.
                </p>
            </div>
            """, unsafe_allow_html=True)

        with right_col:
            # Top signup section
            st.markdown("""
            <div style="text-align: right; margin-bottom: 2rem; color: #6b7280; font-size: 0.85rem;">
                ليس لديك حساب؟
                <button style="background: white; border: 1px solid #d1d5db; color: #374151; padding: 0.5rem 1rem; border-radius: 4px; font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.5px; font-weight: 500;">إنشاء حساب</button>
            </div>
            """, unsafe_allow_html=True)

            # Form title and subtitle
            st.markdown("""
            <h2 class="form-title">تسجيل الدخول إلى ماكس بت القانونية</h2>
            <p class="form-subtitle">أدخل بيانات تسجيل الدخول أدناه</p>
            """, unsafe_allow_html=True)

            # Form fields
            st.markdown('<p class="field-label">عنوان البريد الإلكتروني</p>', unsafe_allow_html=True)
            username = st.text_input(
                "عنوان البريد الإلكتروني",
                placeholder="أدخل عنوان بريدك الإلكتروني",
                key="username_input",
                label_visibility="collapsed"
            )

            st.markdown('<p class="field-label">كلمة المرور</p>', unsafe_allow_html=True)
            password = st.text_input(
                "كلمة المرور",
                type="password",
                placeholder="أدخل كلمة المرور",
                key="password_input",
                label_visibility="collapsed"
            )

            st.markdown('<p style="color: #6b7280; font-size: 0.85rem; margin: 1rem 0;">تذكرني</p>', unsafe_allow_html=True)

            # Login button
            login_clicked = st.button("تسجيل الدخول", type="primary", use_container_width=True)

            # Test credentials info
            st.info("🔑 **بيانات التجربة:** البريد الإلكتروني: `admin` | كلمة المرور: `admin123`")

            st.markdown('<p style="color: #9ca3af; font-size: 0.8rem; text-align: right; margin-top: 2rem;">حقوق الطبع والنشر © 2024 - شركة ماكس بت المحدودة</p>', unsafe_allow_html=True)

        # Handle login
        if login_clicked:
            if username and password:
                user = self.user_manager.authenticate(username, password)
                if user:
                    st.session_state.user = user
                    st.session_state.authenticated = True
                    st.success("✅ تم تسجيل الدخول بنجاح! مرحباً بك في ماكس بت القانونية")
                    st.rerun()
                else:
                    st.error("❌ البريد الإلكتروني أو كلمة المرور غير صحيحة")
            else:
                st.error("⚠️ يرجى إدخال البريد الإلكتروني وكلمة المرور")

        return None
    
    def logout(self):
        """Logout user"""
        st.session_state.authenticated = False
        st.session_state.user = None
        st.rerun()
    
    def user_info_sidebar(self):
        """Display user info in sidebar"""
        if st.session_state.get("authenticated") and st.session_state.get("user"):
            user = st.session_state.user
            
            st.sidebar.markdown("---")
            st.sidebar.markdown("### 👤 معلومات المستخدم")
            
            st.sidebar.markdown(f"""
            **الاسم:** {user['full_name']}  
            **الدور:** {self._get_role_name(user['role'])}  
            **البريد:** {user['email']}
            """)
            
            if st.sidebar.button("تسجيل الخروج", use_container_width=True):
                self.logout()
    
    def _get_role_name(self, role: str) -> str:
        """Get Arabic role name"""
        role_names = {
            UserRole.ADMIN: "مدير النظام",
            UserRole.LAWYER: "محامي",
            UserRole.PARALEGAL: "مساعد قانوني",
            UserRole.CLIENT: "عميل"
        }
        return role_names.get(role, role)

def require_auth(func):
    """Decorator to require authentication"""
    def wrapper(*args, **kwargs):
        if not st.session_state.get("authenticated"):
            return False
        return func(*args, **kwargs)
    return wrapper

def require_permission(permission: str):
    """Decorator to require specific permission"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            if not st.session_state.get("authenticated"):
                return False
            
            user_manager = UserManager()
            username = st.session_state.user["username"]
            
            if not user_manager.has_permission(username, permission):
                st.error("ليس لديك صلاحية للوصول إلى هذه الميزة")
                return False
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# Initialize authentication
def init_auth():
    """Initialize authentication system"""
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "user" not in st.session_state:
        st.session_state.user = None

def is_authenticated() -> bool:
    """Check if user is authenticated"""
    return st.session_state.get("authenticated", False)

def get_current_user() -> Optional[Dict[str, Any]]:
    """Get current authenticated user"""
    return st.session_state.get("user")

def get_current_role() -> Optional[str]:
    """Get current user role"""
    user = get_current_user()
    return user["role"] if user else None
