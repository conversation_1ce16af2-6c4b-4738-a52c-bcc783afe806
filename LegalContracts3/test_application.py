#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Test Suite for Enhanced Legal Contract Analyzer
Tests all major functionality and components
"""

import unittest
import sqlite3
import tempfile
import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Import modules to test
from database_manager import DatabaseManager
from auth_manager import AuthManager
from template_manager import TemplateManager
from ai_backend import ContractAnalyzer
from file_processor import FileProcessor
from export_manager import ExportManager
from help_system import HelpSystem
from monitoring_system import MonitoringSystem

class TestDatabaseManager(unittest.TestCase):
    """Test database manager functionality"""
    
    def setUp(self):
        """Set up test database"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_manager = DatabaseManager(self.test_db.name)
    
    def tearDown(self):
        """Clean up test database"""
        os.unlink(self.test_db.name)
    
    def test_database_initialization(self):
        """Test database initialization"""
        self.assertTrue(os.path.exists(self.test_db.name))
        
        # Check if tables exist
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['users', 'contracts', 'analysis_results', 'templates', 'analysis_history']
        for table in expected_tables:
            self.assertIn(table, tables)
        
        conn.close()
    
    def test_user_operations(self):
        """Test user CRUD operations"""
        # Create user
        user_id = self.db_manager.create_user("testuser", "password123", "<EMAIL>")
        self.assertIsNotNone(user_id)
        
        # Get user
        user = self.db_manager.get_user_by_username("testuser")
        self.assertIsNotNone(user)
        self.assertEqual(user['username'], "testuser")
        
        # Update user
        success = self.db_manager.update_user(user_id, email="<EMAIL>")
        self.assertTrue(success)
        
        # Verify update
        updated_user = self.db_manager.get_user_by_id(user_id)
        self.assertEqual(updated_user['email'], "<EMAIL>")

class TestAuthManager(unittest.TestCase):
    """Test authentication manager"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_manager = DatabaseManager(self.test_db.name)
        self.auth_manager = AuthManager(self.db_manager)
    
    def tearDown(self):
        """Clean up"""
        os.unlink(self.test_db.name)
    
    def test_user_registration(self):
        """Test user registration"""
        success = self.auth_manager.register_user("testuser", "password123", "<EMAIL>")
        self.assertTrue(success)
        
        # Test duplicate registration
        success = self.auth_manager.register_user("testuser", "password456", "<EMAIL>")
        self.assertFalse(success)
    
    def test_user_login(self):
        """Test user login"""
        # Register user first
        self.auth_manager.register_user("testuser", "password123", "<EMAIL>")
        
        # Test valid login
        success = self.auth_manager.login("testuser", "password123")
        self.assertTrue(success)
        
        # Test invalid login
        success = self.auth_manager.login("testuser", "wrongpassword")
        self.assertFalse(success)
    
    def test_permissions(self):
        """Test user permissions"""
        # Register and login user
        self.auth_manager.register_user("testuser", "password123", "<EMAIL>")
        self.auth_manager.login("testuser", "password123")
        
        permissions = self.auth_manager.get_user_permissions()
        self.assertIsInstance(permissions, dict)
        self.assertIn('can_analyze_contracts', permissions)

class TestFileProcessor(unittest.TestCase):
    """Test file processing functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.file_processor = FileProcessor()
    
    def test_text_processing(self):
        """Test text processing"""
        test_text = "This is a test contract with legal terms."
        
        result = self.file_processor.process_text(test_text)
        
        self.assertIsInstance(result, dict)
        self.assertIn('text', result)
        self.assertIn('word_count', result)
        self.assertIn('language', result)
        self.assertEqual(result['text'], test_text)
        self.assertGreater(result['word_count'], 0)
    
    def test_file_validation(self):
        """Test file validation"""
        # Test valid file types
        self.assertTrue(self.file_processor.validate_file_type("document.pdf"))
        self.assertTrue(self.file_processor.validate_file_type("document.docx"))
        self.assertTrue(self.file_processor.validate_file_type("document.txt"))
        
        # Test invalid file types
        self.assertFalse(self.file_processor.validate_file_type("document.jpg"))
        self.assertFalse(self.file_processor.validate_file_type("document.exe"))

class TestContractAnalyzer(unittest.TestCase):
    """Test AI contract analysis"""
    
    def setUp(self):
        """Set up test environment"""
        self.analyzer = ContractAnalyzer()
    
    def test_analysis_structure(self):
        """Test analysis result structure"""
        test_contract = """
        Employment Contract
        
        This agreement is between Company ABC and Employee XYZ.
        The employee will work 40 hours per week.
        Salary is $50,000 per year.
        """
        
        # Mock analysis (since we don't want to call actual AI in tests)
        result = {
            'risk_score': 25,
            'legal_points': ['Salary clearly defined', 'Working hours specified'],
            'recommendations': ['Add termination clause', 'Include benefits section'],
            'summary': 'Basic employment contract with standard terms',
            'compliance_issues': [],
            'metadata': {
                'analysis_date': '2024-01-01',
                'legal_system': 'kuwait',
                'word_count': len(test_contract.split())
            }
        }
        
        # Verify structure
        self.assertIn('risk_score', result)
        self.assertIn('legal_points', result)
        self.assertIn('recommendations', result)
        self.assertIn('summary', result)
        self.assertIsInstance(result['risk_score'], (int, float))
        self.assertIsInstance(result['legal_points'], list)
        self.assertIsInstance(result['recommendations'], list)

class TestExportManager(unittest.TestCase):
    """Test export functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.export_manager = ExportManager()
    
    def test_export_data_structure(self):
        """Test export data structure"""
        test_data = {
            'contract_name': 'Test Contract',
            'analysis_date': '2024-01-01',
            'risk_score': 30,
            'legal_points': ['Point 1', 'Point 2'],
            'recommendations': ['Recommendation 1', 'Recommendation 2'],
            'summary': 'Test summary'
        }
        
        # Test data validation
        self.assertTrue(self.export_manager.validate_export_data(test_data))
        
        # Test missing required fields
        incomplete_data = {'contract_name': 'Test'}
        self.assertFalse(self.export_manager.validate_export_data(incomplete_data))

class TestTemplateManager(unittest.TestCase):
    """Test template management"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.db_manager = DatabaseManager(self.test_db.name)
        self.auth_manager = AuthManager(self.db_manager)
        self.template_manager = TemplateManager(self.db_manager, self.auth_manager)
    
    def tearDown(self):
        """Clean up"""
        os.unlink(self.test_db.name)
    
    def test_template_categories(self):
        """Test template categories"""
        categories = self.template_manager.get_template_categories()
        
        self.assertIsInstance(categories, dict)
        self.assertIn('employment', categories)
        self.assertIn('commercial', categories)
        self.assertIn('real_estate', categories)
    
    def test_template_operations(self):
        """Test template CRUD operations"""
        # Create template
        template_data = {
            'name': 'Test Template',
            'description': 'Test Description',
            'category': 'employment',
            'legal_system': 'kuwait',
            'content': 'Test template content'
        }
        
        template_id = self.template_manager.create_template(template_data)
        self.assertIsNotNone(template_id)
        
        # Get template
        template = self.template_manager.get_template(template_id)
        self.assertIsNotNone(template)
        self.assertEqual(template['name'], 'Test Template')

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def setUp(self):
        """Set up complete test environment"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        
        # Initialize all components
        self.db_manager = DatabaseManager(self.test_db.name)
        self.auth_manager = AuthManager(self.db_manager)
        self.template_manager = TemplateManager(self.db_manager, self.auth_manager)
        self.file_processor = FileProcessor()
        self.export_manager = ExportManager()
        self.help_system = HelpSystem()
        self.monitoring_system = MonitoringSystem(self.db_manager)
    
    def tearDown(self):
        """Clean up"""
        os.unlink(self.test_db.name)
    
    def test_complete_workflow(self):
        """Test complete contract analysis workflow"""
        # 1. Register and login user
        success = self.auth_manager.register_user("testuser", "password123", "<EMAIL>")
        self.assertTrue(success)
        
        success = self.auth_manager.login("testuser", "password123")
        self.assertTrue(success)
        
        # 2. Process contract text
        contract_text = "Test employment contract with basic terms."
        processed = self.file_processor.process_text(contract_text)
        self.assertIsNotNone(processed)
        
        # 3. Store contract in database
        contract_id = self.db_manager.store_contract(
            user_id=self.auth_manager.get_current_user()['id'],
            filename="test_contract.txt",
            content=contract_text,
            file_type="txt"
        )
        self.assertIsNotNone(contract_id)
        
        # 4. Create mock analysis result
        analysis_result = {
            'risk_score': 25,
            'legal_points': ['Basic terms included'],
            'recommendations': ['Add termination clause'],
            'summary': 'Simple employment contract'
        }
        
        # 5. Store analysis result
        analysis_id = self.db_manager.store_analysis_result(
            contract_id=contract_id,
            analysis_result=analysis_result,
            legal_system='kuwait'
        )
        self.assertIsNotNone(analysis_id)
        
        # 6. Test export functionality
        export_data = {
            'contract_name': 'test_contract.txt',
            'analysis_date': '2024-01-01',
            **analysis_result
        }
        
        valid = self.export_manager.validate_export_data(export_data)
        self.assertTrue(valid)
    
    def test_system_health(self):
        """Test system health monitoring"""
        health = self.monitoring_system._get_system_health()
        
        self.assertIsInstance(health, dict)
        self.assertIn('overall', health)
        self.assertIn('components', health)

def run_tests():
    """Run all tests and generate report"""
    print("🧪 Starting Enhanced Legal Contract Analyzer Test Suite")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDatabaseManager,
        TestAuthManager,
        TestFileProcessor,
        TestContractAnalyzer,
        TestExportManager,
        TestTemplateManager,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🧪 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n🚨 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    if not result.failures and not result.errors:
        print("\n✅ ALL TESTS PASSED!")
        print("🎉 Enhanced Legal Contract Analyzer is ready for production!")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
