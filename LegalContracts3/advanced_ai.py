"""
Advanced AI Features for Contract Analysis
Developed by MAXBIT LLC © 2025
"""

import json
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import streamlit as st
from ai_backend import ContractAnalyzer

class SpecializedAnalyzer:
    """Specialized AI analyzer for different contract types"""
    
    def __init__(self):
        self.base_analyzer = ContractAnalyzer()
        self.contract_patterns = self._load_contract_patterns()
        self.legal_precedents = self._load_legal_precedents()
    
    def _load_contract_patterns(self) -> Dict[str, Any]:
        """Load contract-specific patterns and rules"""
        return {
            "employment": {
                "required_clauses": [
                    "job_description", "salary", "working_hours", 
                    "probation_period", "termination_conditions"
                ],
                "kuwaiti_requirements": [
                    "minimum_wage_compliance", "working_hours_limit",
                    "annual_leave_entitlement", "end_of_service_benefits"
                ],
                "risk_indicators": [
                    "unclear_termination", "below_minimum_wage",
                    "excessive_working_hours", "missing_benefits"
                ]
            },
            "commercial": {
                "required_clauses": [
                    "parties_identification", "scope_of_work", "payment_terms",
                    "delivery_conditions", "liability_limitations"
                ],
                "kuwaiti_requirements": [
                    "commercial_registration", "tax_obligations",
                    "dispute_resolution", "governing_law"
                ],
                "risk_indicators": [
                    "unlimited_liability", "unclear_payment_terms",
                    "missing_force_majeure", "weak_ip_protection"
                ]
            },
            "real_estate": {
                "required_clauses": [
                    "property_description", "purchase_price", "payment_schedule",
                    "transfer_conditions", "warranties"
                ],
                "kuwaiti_requirements": [
                    "property_registration", "municipality_approval",
                    "ownership_verification", "zoning_compliance"
                ],
                "risk_indicators": [
                    "unclear_boundaries", "missing_approvals",
                    "payment_risks", "title_issues"
                ]
            }
        }
    
    def _load_legal_precedents(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load relevant Kuwaiti legal precedents"""
        return {
            "employment": [
                {
                    "case_id": "EMP-2023-001",
                    "summary": "Court ruled that probation period cannot exceed 6 months",
                    "law_reference": "Labor Law 6/2010 Article 35",
                    "implication": "Probation periods exceeding 6 months are invalid"
                },
                {
                    "case_id": "EMP-2022-045",
                    "summary": "Termination without notice requires serious misconduct proof",
                    "law_reference": "Labor Law 6/2010 Article 45",
                    "implication": "Clear misconduct definition required for immediate termination"
                }
            ],
            "commercial": [
                {
                    "case_id": "COM-2023-012",
                    "summary": "Force majeure clause must specifically mention government actions",
                    "law_reference": "Civil Code 67/1980 Article 203",
                    "implication": "Generic force majeure clauses may not cover regulatory changes"
                }
            ],
            "real_estate": [
                {
                    "case_id": "RE-2023-008",
                    "summary": "Property sale invalid without municipality clearance",
                    "law_reference": "Real Estate Law 5/1973",
                    "implication": "Municipality approval mandatory for property transfers"
                }
            ]
        }
    
    def analyze_specialized_contract(self, contract_text: str, 
                                   contract_type: str) -> Dict[str, Any]:
        """Perform specialized analysis based on contract type"""
        # Get base analysis
        base_analysis = self.base_analyzer.analyze_contract(contract_text)
        
        if not base_analysis:
            return None
        
        # Add specialized analysis
        specialized_analysis = {
            "base_analysis": base_analysis,
            "specialized_insights": self._get_specialized_insights(contract_text, contract_type),
            "compliance_check": self._check_kuwaiti_compliance(contract_text, contract_type),
            "risk_assessment": self._assess_specialized_risks(contract_text, contract_type),
            "legal_precedents": self._find_relevant_precedents(contract_text, contract_type),
            "improvement_suggestions": self._generate_improvements(contract_text, contract_type)
        }
        
        return specialized_analysis
    
    def _get_specialized_insights(self, contract_text: str, contract_type: str) -> Dict[str, Any]:
        """Get contract-type specific insights"""
        if contract_type not in self.contract_patterns:
            return {"message": "No specialized insights available for this contract type"}
        
        patterns = self.contract_patterns[contract_type]
        insights = {
            "contract_type": contract_type,
            "required_clauses_analysis": self._analyze_required_clauses(contract_text, patterns),
            "kuwaiti_law_compliance": self._check_kuwaiti_requirements(contract_text, patterns),
            "completeness_score": 0
        }
        
        # Calculate completeness score
        total_required = len(patterns["required_clauses"])
        found_clauses = len([c for c in insights["required_clauses_analysis"] if c["found"]])
        insights["completeness_score"] = (found_clauses / total_required) * 100 if total_required > 0 else 0
        
        return insights
    
    def _analyze_required_clauses(self, contract_text: str, patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze presence of required clauses"""
        clause_analysis = []
        
        clause_keywords = {
            "job_description": ["duties", "responsibilities", "job", "position", "role"],
            "salary": ["salary", "wage", "compensation", "pay", "remuneration"],
            "working_hours": ["hours", "schedule", "time", "shift"],
            "probation_period": ["probation", "trial", "probationary"],
            "termination_conditions": ["termination", "dismissal", "end", "notice"],
            "parties_identification": ["party", "parties", "between", "contractor"],
            "scope_of_work": ["scope", "work", "services", "deliverables"],
            "payment_terms": ["payment", "invoice", "billing", "due"],
            "delivery_conditions": ["delivery", "completion", "deadline"],
            "liability_limitations": ["liability", "limitation", "damages"],
            "property_description": ["property", "land", "building", "premises"],
            "purchase_price": ["price", "amount", "consideration", "cost"],
            "payment_schedule": ["schedule", "installment", "payment plan"],
            "transfer_conditions": ["transfer", "conveyance", "title"],
            "warranties": ["warranty", "guarantee", "representation"]
        }
        
        for clause in patterns["required_clauses"]:
            keywords = clause_keywords.get(clause, [clause.replace("_", " ")])
            found = any(keyword.lower() in contract_text.lower() for keyword in keywords)
            
            clause_analysis.append({
                "clause": clause,
                "clause_name": clause.replace("_", " ").title(),
                "found": found,
                "keywords": keywords,
                "importance": "high" if clause in ["salary", "scope_of_work", "payment_terms"] else "medium"
            })
        
        return clause_analysis
    
    def _check_kuwaiti_requirements(self, contract_text: str, patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check compliance with Kuwaiti legal requirements"""
        compliance_check = []
        
        requirement_keywords = {
            "minimum_wage_compliance": ["minimum wage", "120 kwd", "basic salary"],
            "working_hours_limit": ["8 hours", "48 hours", "working hours"],
            "annual_leave_entitlement": ["annual leave", "vacation", "30 days"],
            "end_of_service_benefits": ["end of service", "gratuity", "severance"],
            "commercial_registration": ["commercial registration", "license", "permit"],
            "tax_obligations": ["tax", "vat", "taxation"],
            "dispute_resolution": ["dispute", "arbitration", "court", "jurisdiction"],
            "governing_law": ["governing law", "kuwait law", "applicable law"],
            "property_registration": ["registration", "title deed", "ownership"],
            "municipality_approval": ["municipality", "approval", "permit"],
            "ownership_verification": ["ownership", "title", "deed"],
            "zoning_compliance": ["zoning", "land use", "permitted use"]
        }
        
        for requirement in patterns["kuwaiti_requirements"]:
            keywords = requirement_keywords.get(requirement, [requirement.replace("_", " ")])
            found = any(keyword.lower() in contract_text.lower() for keyword in keywords)
            
            compliance_check.append({
                "requirement": requirement,
                "requirement_name": requirement.replace("_", " ").title(),
                "compliant": found,
                "keywords": keywords,
                "severity": "high" if requirement in ["minimum_wage_compliance", "commercial_registration"] else "medium"
            })
        
        return compliance_check
    
    def _check_kuwaiti_compliance(self, contract_text: str, contract_type: str) -> Dict[str, Any]:
        """Comprehensive Kuwaiti law compliance check"""
        compliance_issues = []
        compliance_score = 100
        
        # Employment law specific checks
        if contract_type == "employment":
            # Check probation period
            if "probation" in contract_text.lower():
                probation_match = re.search(r'probation.*?(\d+).*?(month|year)', contract_text.lower())
                if probation_match:
                    period = int(probation_match.group(1))
                    unit = probation_match.group(2)
                    if unit == "year" or (unit == "month" and period > 6):
                        compliance_issues.append({
                            "issue": "Probation period exceeds legal limit",
                            "description": "Probation period cannot exceed 6 months under Kuwaiti Labor Law",
                            "law_reference": "Labor Law 6/2010 Article 35",
                            "severity": "high"
                        })
                        compliance_score -= 20
            
            # Check working hours
            hours_match = re.search(r'(\d+).*?hours.*?day', contract_text.lower())
            if hours_match:
                hours = int(hours_match.group(1))
                if hours > 8:
                    compliance_issues.append({
                        "issue": "Daily working hours exceed legal limit",
                        "description": "Daily working hours cannot exceed 8 hours",
                        "law_reference": "Labor Law 6/2010 Article 65",
                        "severity": "medium"
                    })
                    compliance_score -= 10
        
        # Commercial law specific checks
        elif contract_type == "commercial":
            if "governing law" not in contract_text.lower():
                compliance_issues.append({
                    "issue": "Missing governing law clause",
                    "description": "Commercial contracts should specify Kuwaiti law as governing law",
                    "law_reference": "Commercial Law 68/1980",
                    "severity": "medium"
                })
                compliance_score -= 15
        
        return {
            "overall_score": max(0, compliance_score),
            "compliance_level": "high" if compliance_score >= 80 else "medium" if compliance_score >= 60 else "low",
            "issues": compliance_issues,
            "total_issues": len(compliance_issues)
        }
    
    def _assess_specialized_risks(self, contract_text: str, contract_type: str) -> Dict[str, Any]:
        """Assess contract-specific risks"""
        if contract_type not in self.contract_patterns:
            return {"message": "No specialized risk assessment available"}
        
        patterns = self.contract_patterns[contract_type]
        risks = []
        risk_score = 0
        
        for risk_indicator in patterns["risk_indicators"]:
            risk_keywords = {
                "unclear_termination": ["termination", "dismissal", "without cause"],
                "below_minimum_wage": ["salary", "wage", "compensation"],
                "excessive_working_hours": ["hours", "overtime", "schedule"],
                "missing_benefits": ["benefits", "insurance", "leave"],
                "unlimited_liability": ["liability", "unlimited", "damages"],
                "unclear_payment_terms": ["payment", "due", "invoice"],
                "missing_force_majeure": ["force majeure", "act of god"],
                "weak_ip_protection": ["intellectual property", "confidential"],
                "unclear_boundaries": ["boundaries", "area", "dimensions"],
                "missing_approvals": ["approval", "permit", "license"],
                "payment_risks": ["payment", "default", "security"],
                "title_issues": ["title", "ownership", "encumbrance"]
            }
            
            keywords = risk_keywords.get(risk_indicator, [risk_indicator.replace("_", " ")])
            risk_present = self._check_risk_indicator(contract_text, risk_indicator, keywords)
            
            if risk_present:
                risks.append({
                    "risk": risk_indicator,
                    "risk_name": risk_indicator.replace("_", " ").title(),
                    "severity": "high" if risk_indicator in ["unlimited_liability", "below_minimum_wage"] else "medium",
                    "description": self._get_risk_description(risk_indicator)
                })
                risk_score += 15 if risk_indicator in ["unlimited_liability", "below_minimum_wage"] else 10
        
        return {
            "risk_score": min(100, risk_score),
            "risk_level": "high" if risk_score >= 50 else "medium" if risk_score >= 25 else "low",
            "identified_risks": risks,
            "total_risks": len(risks)
        }
    
    def _check_risk_indicator(self, contract_text: str, risk_indicator: str, keywords: List[str]) -> bool:
        """Check if a specific risk indicator is present"""
        text_lower = contract_text.lower()
        
        # Specific logic for different risk indicators
        if risk_indicator == "unclear_termination":
            return "termination" in text_lower and "notice" not in text_lower
        elif risk_indicator == "unlimited_liability":
            return "liability" in text_lower and "limitation" not in text_lower
        elif risk_indicator == "missing_force_majeure":
            return "force majeure" not in text_lower and "act of god" not in text_lower
        else:
            return any(keyword in text_lower for keyword in keywords)
    
    def _get_risk_description(self, risk_indicator: str) -> str:
        """Get description for risk indicator"""
        descriptions = {
            "unclear_termination": "Termination conditions are not clearly defined",
            "below_minimum_wage": "Salary may be below minimum wage requirements",
            "excessive_working_hours": "Working hours may exceed legal limits",
            "missing_benefits": "Required employee benefits are not specified",
            "unlimited_liability": "Liability is not limited, creating financial risk",
            "unclear_payment_terms": "Payment terms are ambiguous or unclear",
            "missing_force_majeure": "No protection against unforeseeable circumstances",
            "weak_ip_protection": "Intellectual property rights are not adequately protected",
            "unclear_boundaries": "Property boundaries are not clearly defined",
            "missing_approvals": "Required government approvals are not mentioned",
            "payment_risks": "Payment security mechanisms are insufficient",
            "title_issues": "Property title verification is inadequate"
        }
        return descriptions.get(risk_indicator, "Risk requires attention")
    
    def _find_relevant_precedents(self, contract_text: str, contract_type: str) -> List[Dict[str, Any]]:
        """Find relevant legal precedents"""
        if contract_type not in self.legal_precedents:
            return []
        
        relevant_precedents = []
        precedents = self.legal_precedents[contract_type]
        
        for precedent in precedents:
            # Simple keyword matching for relevance
            summary_keywords = precedent["summary"].lower().split()
            text_lower = contract_text.lower()
            
            relevance_score = sum(1 for keyword in summary_keywords if keyword in text_lower)
            
            if relevance_score > 2:  # Threshold for relevance
                relevant_precedents.append({
                    **precedent,
                    "relevance_score": relevance_score
                })
        
        # Sort by relevance
        relevant_precedents.sort(key=lambda x: x["relevance_score"], reverse=True)
        return relevant_precedents[:3]  # Return top 3 most relevant
    
    def _generate_improvements(self, contract_text: str, contract_type: str) -> List[Dict[str, Any]]:
        """Generate contract improvement suggestions"""
        improvements = []
        
        # Generic improvements
        if "force majeure" not in contract_text.lower():
            improvements.append({
                "category": "risk_mitigation",
                "suggestion": "Add force majeure clause",
                "description": "Include protection against unforeseeable circumstances",
                "priority": "medium",
                "kuwaiti_law_reference": "Civil Code 67/1980 Article 203"
            })
        
        if "governing law" not in contract_text.lower():
            improvements.append({
                "category": "legal_compliance",
                "suggestion": "Specify governing law",
                "description": "Clearly state that Kuwaiti law governs the contract",
                "priority": "high",
                "kuwaiti_law_reference": "Civil Code 67/1980"
            })
        
        # Contract-type specific improvements
        if contract_type == "employment":
            if "probation" not in contract_text.lower():
                improvements.append({
                    "category": "employment_terms",
                    "suggestion": "Define probation period",
                    "description": "Specify probation period (maximum 6 months)",
                    "priority": "medium",
                    "kuwaiti_law_reference": "Labor Law 6/2010 Article 35"
                })
        
        elif contract_type == "commercial":
            if "intellectual property" not in contract_text.lower():
                improvements.append({
                    "category": "ip_protection",
                    "suggestion": "Add intellectual property clause",
                    "description": "Protect intellectual property rights and confidential information",
                    "priority": "high",
                    "kuwaiti_law_reference": "Commercial Law 68/1980"
                })
        
        return improvements

class AIInsightsGenerator:
    """Generate AI-powered insights and recommendations"""
    
    def __init__(self):
        self.specialized_analyzer = SpecializedAnalyzer()
    
    def generate_executive_summary(self, analysis: Dict[str, Any]) -> str:
        """Generate executive summary of contract analysis"""
        if not analysis:
            return "No analysis data available"
        
        base_analysis = analysis.get("base_analysis", {})
        specialized = analysis.get("specialized_insights", {})
        compliance = analysis.get("compliance_check", {})
        risks = analysis.get("risk_assessment", {})
        
        summary_parts = []
        
        # Overall assessment
        completeness_score = specialized.get("completeness_score", 0)
        compliance_score = compliance.get("overall_score", 0)
        risk_score = risks.get("risk_score", 0)
        
        overall_score = (completeness_score + compliance_score + (100 - risk_score)) / 3
        
        summary_parts.append(f"📊 **Overall Contract Score: {overall_score:.1f}/100**")
        
        # Key findings
        summary_parts.append("\n🔍 **Key Findings:**")
        summary_parts.append(f"• Contract Completeness: {completeness_score:.1f}%")
        summary_parts.append(f"• Kuwaiti Law Compliance: {compliance_score:.1f}%")
        summary_parts.append(f"• Risk Level: {risks.get('risk_level', 'unknown').title()}")
        
        # Critical issues
        compliance_issues = compliance.get("issues", [])
        high_severity_issues = [issue for issue in compliance_issues if issue.get("severity") == "high"]
        
        if high_severity_issues:
            summary_parts.append(f"\n⚠️ **Critical Issues Found: {len(high_severity_issues)}**")
            for issue in high_severity_issues[:3]:  # Show top 3
                summary_parts.append(f"• {issue['issue']}")
        
        # Recommendations
        improvements = analysis.get("improvement_suggestions", [])
        high_priority = [imp for imp in improvements if imp.get("priority") == "high"]
        
        if high_priority:
            summary_parts.append(f"\n💡 **Priority Recommendations:**")
            for rec in high_priority[:3]:  # Show top 3
                summary_parts.append(f"• {rec['suggestion']}")
        
        return "\n".join(summary_parts)
    
    def generate_risk_insights(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed risk insights"""
        risks = analysis.get("risk_assessment", {})
        compliance = analysis.get("compliance_check", {})
        
        risk_insights = {
            "overall_risk_level": risks.get("risk_level", "unknown"),
            "risk_score": risks.get("risk_score", 0),
            "critical_risks": [],
            "compliance_risks": [],
            "mitigation_strategies": []
        }
        
        # Identify critical risks
        identified_risks = risks.get("identified_risks", [])
        risk_insights["critical_risks"] = [
            risk for risk in identified_risks if risk.get("severity") == "high"
        ]
        
        # Compliance risks
        compliance_issues = compliance.get("issues", [])
        risk_insights["compliance_risks"] = [
            issue for issue in compliance_issues if issue.get("severity") == "high"
        ]
        
        # Generate mitigation strategies
        for risk in risk_insights["critical_risks"]:
            strategy = self._generate_mitigation_strategy(risk["risk"])
            if strategy:
                risk_insights["mitigation_strategies"].append(strategy)
        
        return risk_insights
    
    def _generate_mitigation_strategy(self, risk_type: str) -> Optional[Dict[str, str]]:
        """Generate mitigation strategy for specific risk"""
        strategies = {
            "unlimited_liability": {
                "strategy": "Add liability limitation clause",
                "action": "Include specific monetary limits and exclude certain types of damages",
                "timeline": "Immediate - before contract execution"
            },
            "unclear_termination": {
                "strategy": "Define clear termination procedures",
                "action": "Specify notice periods, termination grounds, and procedures",
                "timeline": "Immediate - critical for legal compliance"
            },
            "below_minimum_wage": {
                "strategy": "Adjust compensation to meet legal requirements",
                "action": "Ensure salary meets minimum wage of 120 KWD for Kuwaiti nationals",
                "timeline": "Immediate - legal requirement"
            }
        }
        
        return strategies.get(risk_type)
