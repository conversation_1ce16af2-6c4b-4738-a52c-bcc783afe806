#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Legal Contract Analyzer Application
Beautiful, multi-language, multi-legal system contract analysis platform
"""

import streamlit as st
import os
import json
import time
import sys
import logging
from datetime import datetime
from pathlib import Path
import tempfile
import pandas as pd
from typing import Dict, List, Optional, Any

# Import enhanced modules
from beautiful_ui import get_beautiful_ui
from enhanced_i18n import get_i18n, t, Language
from theme_manager import get_theme_manager, ThemeType
from legal_frameworks import get_legal_framework_manager, LegalSystem
from law_selection_ui import get_law_selection_ui
from ai_backend import ContractAnalyzer

# Import existing modules
from auth import UserManager, AuthUI, init_auth, is_authenticated, get_current_user
from database import ContractDatabase
from utils import validate_file, extract_text_from_file

# Import new enhanced modules
from database_manager import DatabaseManager
from auth_manager import AuthManager
from template_manager import TemplateManager
from help_system import HelpSystem
from monitoring_system import MonitoringSystem
from clause_library_manager import ClauseLibraryManager
from contract_generator import ContractGenerator
from compliance_checker import ComplianceChecker
from custom_prompts_manager import CustomPromptsManager
from confidence_scorer import ConfidenceScorer
from advanced_pages import AdvancedPagesManager

# Configure logging
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="محلل العقود القانونية - MAXBIT LLC",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

class EnhancedLegalApp:
    """Enhanced legal contract analyzer application"""
    
    def __init__(self):
        self.ui = get_beautiful_ui()
        self.i18n = get_i18n()
        self.theme_manager = get_theme_manager()
        self.legal_manager = get_legal_framework_manager()
        self.law_ui = get_law_selection_ui()

        # Initialize enhanced database and auth
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager(self.db_manager)

        # Initialize template manager
        self.template_manager = TemplateManager(self.db_manager, self.auth_manager)

        # Initialize help system
        self.help_system = HelpSystem()

        # Initialize monitoring system
        self.monitoring_system = MonitoringSystem(self.db_manager)

        # Initialize advanced legal features
        self.clause_manager = ClauseLibraryManager()
        self.contract_generator = ContractGenerator()
        self.compliance_checker = ComplianceChecker()
        self.prompts_manager = CustomPromptsManager()
        self.confidence_scorer = ConfidenceScorer()

        # Initialize advanced pages manager
        self.advanced_pages = AdvancedPagesManager(self)

        # Initialize AI analyzer with user settings
        self.analyzer = self._get_configured_analyzer()

        # Initialize legacy database for compatibility
        self.db = ContractDatabase()

        # Initialize session state
        self._initialize_session_state()

    def _get_configured_analyzer(self):
        """Get analyzer configured with current user settings"""
        try:
            # Get current AI settings from session state
            backend = st.session_state.get('ai_backend', 'lmstudio')
            model = st.session_state.get('ai_model', 'arabic-law-meta-qwen3-8b-base')
            host = st.session_state.get('ai_host', 'localhost')
            port = st.session_state.get('ai_port', 1234 if backend == 'lmstudio' else 11434)

            return ContractAnalyzer(
                backend=backend,
                model=model,
                host=host,
                port=port
            )
        except Exception as e:
            # Fallback to default settings
            return ContractAnalyzer(
                backend='lmstudio',
                model='arabic-law-meta-qwen3-8b-base',
                host='localhost',
                port=1234
            )
    
    def _initialize_session_state(self):
        """Initialize session state variables"""
        # Initialize authentication
        init_auth()
        
        # Initialize app state
        if 'selected_page' not in st.session_state:
            st.session_state.selected_page = 'home'
        if 'analysis_history' not in st.session_state:
            st.session_state.analysis_history = []
        if 'current_analysis' not in st.session_state:
            st.session_state.current_analysis = None
        if 'theme' not in st.session_state:
            st.session_state.theme = ThemeType.LIGHT.value
        if 'language' not in st.session_state:
            st.session_state.language = Language.ARABIC.value
    
    def run(self):
        """Run the enhanced application"""
        # Check authentication with enhanced auth manager
        if not self.auth_manager.is_authenticated():
            self.auth_manager.render_login_page()
            return

        # Render main application
        self._render_main_app()
    
    def _render_login_page(self):
        """Render enhanced login page"""
        # Simple styling without theme manager for login page
        st.markdown("""
        <style>
        .main > div {
            padding-top: 2rem;
        }
        .stForm {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        </style>
        """, unsafe_allow_html=True)

        # Center the login form
        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            st.markdown("#### 🔐 تسجيل الدخول")
            st.markdown("##### محلل العقود القانونية المتقدم")
            st.markdown("---")

            # Login form
            with st.form("login_form"):
                st.markdown("#### بيانات الدخول")

                username = st.text_input(
                    "اسم المستخدم",
                    placeholder="أدخل اسم المستخدم"
                )

                password = st.text_input(
                    "كلمة المرور",
                    type="password",
                    placeholder="أدخل كلمة المرور"
                )

                login_button = st.form_submit_button(
                    "🔑 دخول",
                    type="primary",
                    use_container_width=True
                )

                # Handle login
                if login_button:
                    if username and password:
                        try:
                            with st.spinner("جاري التحقق من البيانات..."):
                                # Import user manager directly to avoid any issues
                                from user_management import UserManager
                                user_manager = UserManager()
                                user = user_manager.authenticate_user(username, password)

                                if user:
                                    # Set session state
                                    st.session_state.authenticated = True
                                    st.session_state.current_user = user

                                    # Show success message
                                    st.success("✅ تم تسجيل الدخول بنجاح!")
                                    st.balloons()

                                    # Force immediate rerun without delay
                                    st.rerun()
                                else:
                                    st.error("❌ اسم المستخدم أو كلمة المرور غير صحيحة")
                        except Exception as e:
                            st.error(f"❌ خطأ في تسجيل الدخول: {str(e)}")
                    else:
                        st.error("❌ يرجى ملء جميع الحقول")

            # Default credentials info
            st.markdown("---")
            st.info("**🔑 بيانات المدير الافتراضي:**\n\n**اسم المستخدم:** admin\n\n**كلمة المرور:** admin123")

            # Additional info
            st.markdown("---")
            st.markdown("**📞 الدعم الفني:**")
            st.caption("<EMAIL> | +1 626 509 0918")
    
    def _render_main_app(self):
        """Render main application interface"""
        # Apply current theme CSS
        current_theme = st.session_state.get('theme', ThemeType.LIGHT.value)
        self.theme_manager.set_theme(ThemeType(current_theme))
        st.markdown(self.theme_manager.generate_css(), unsafe_allow_html=True)

        # Apply compact header styles
        self._apply_compact_header_styles()

        # Render navigation sidebar with integrated branding
        self.ui.render_navigation_sidebar()

        # Render main content based on selected page
        selected_page = st.session_state.get('selected_page', 'home')
        
        if selected_page == 'home':
            self._render_dashboard()
        elif selected_page == 'analysis':
            self._render_analysis_page()
        elif selected_page == 'templates':
            self._render_templates_page()
        elif selected_page == 'database':
            self._render_database_page()
        elif selected_page == 'statistics':
            self._render_statistics_page()
        elif selected_page == 'history':
            self._render_history_page()
        elif selected_page == 'risk':
            self._render_risk_page()
        elif selected_page == 'collaboration':
            self._render_collaboration_page()
        elif selected_page == 'reports':
            self._render_reports_page()
        elif selected_page == 'advanced':
            self._render_advanced_page()
        elif selected_page == 'insights':
            self._render_insights_page()
        elif selected_page == 'monitoring':
            self._render_monitoring_page()
        elif selected_page == 'help':
            self._render_help_page()
        elif selected_page == 'guidelines':
            self._render_guidelines_page()
        elif selected_page == 'users':
            self._render_users_page()
        elif selected_page == 'settings':
            self._render_settings_page()
        elif selected_page == 'sharing':
            self._render_sharing_page()
        elif selected_page == 'clause_library':
            self._render_clause_library_page()
        elif selected_page == 'contract_generator':
            self.advanced_pages.render_contract_generator_page()
        elif selected_page == 'compliance_checker':
            self.advanced_pages.render_compliance_checker_page()
        elif selected_page == 'custom_prompts':
            self.advanced_pages.render_custom_prompts_page()
        else:
            self._render_dashboard()
        
        # Render footer
        self.ui.render_footer()
    
    def _apply_compact_header_styles(self):
        """Apply compact header styles for professional appearance"""
        st.markdown("""
        <style>
        /* Compact page headers */
        .main h1 {
            font-size: 1.6rem !important;
            margin-top: 0.3rem !important;
            margin-bottom: 0.6rem !important;
            font-weight: 600 !important;
            color: #1f2937 !important;
            line-height: 1.3 !important;
        }

        .main h2 {
            font-size: 1.3rem !important;
            margin-top: 0.5rem !important;
            margin-bottom: 0.5rem !important;
            font-weight: 500 !important;
            color: #374151 !important;
            line-height: 1.3 !important;
        }

        .main h3 {
            font-size: 1.1rem !important;
            margin-top: 0.6rem !important;
            margin-bottom: 0.4rem !important;
            font-weight: 500 !important;
            color: #4b5563 !important;
            line-height: 1.3 !important;
        }

        .main h4 {
            font-size: 1.0rem !important;
            margin-top: 0.5rem !important;
            margin-bottom: 0.3rem !important;
            font-weight: 500 !important;
            color: #6b7280 !important;
            line-height: 1.3 !important;
        }

        .main h5 {
            font-size: 0.9rem !important;
            margin-top: 0.2rem !important;
            margin-bottom: 0.4rem !important;
            font-weight: 500 !important;
            color: #6b7280 !important;
            line-height: 1.2 !important;
            opacity: 0.8 !important;
        }

        .main h6 {
            font-size: 0.85rem !important;
            margin-top: 0.2rem !important;
            margin-bottom: 0.3rem !important;
            font-weight: 500 !important;
            color: #9ca3af !important;
            line-height: 1.2 !important;
        }

        /* Reduce spacing around horizontal rules */
        .main hr {
            margin-top: 0.3rem !important;
            margin-bottom: 0.8rem !important;
            border-color: #e5e7eb !important;
            border-width: 1px !important;
        }

        /* Compact section spacing */
        .main .stMarkdown {
            margin-bottom: 0.3rem !important;
        }

        /* Professional spacing for content blocks */
        .main .block-container {
            padding-top: 0.5rem !important;
            padding-bottom: 1rem !important;
        }

        /* Compact tabs */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px !important;
        }

        .stTabs [data-baseweb="tab"] {
            height: 35px !important;
            padding: 0 12px !important;
            font-size: 0.9rem !important;
        }

        /* Compact expanders */
        .streamlit-expanderHeader {
            font-size: 0.95rem !important;
            font-weight: 500 !important;
        }

        /* Compact metrics */
        [data-testid="metric-container"] {
            background-color: #f8fafc !important;
            border: 1px solid #e2e8f0 !important;
            padding: 0.5rem !important;
            border-radius: 6px !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        /* Professional button styling */
        .stButton > button {
            border-radius: 6px !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
        }

        /* Compact form elements */
        .stSelectbox > div > div {
            min-height: 35px !important;
        }

        .stTextInput > div > div > input {
            min-height: 35px !important;
        }
        </style>
        """, unsafe_allow_html=True)

    def _navigate_to_page(self, page_name: str):
        """Navigate to a specific page"""
        st.session_state.selected_page = page_name
        st.rerun()

    def _render_dashboard(self):
        """Render elegant dashboard with clean design"""
        # Minimal page indicator
        st.markdown("##### 🏠 الصفحة الرئيسية")
        st.markdown("---")

        # Welcome message with better spacing
        current_user = self.auth_manager.get_current_user()
        username = current_user.get('username', 'User') if current_user else 'Guest'

        # Compact welcome section
        st.markdown(f"**مرحباً {username}!** - محلل العقود القانونية المتقدم")
        st.markdown("")  # Small spacing

        # Compact dashboard metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("العقود المحللة", "156", delta="12")

        with col2:
            st.metric("تحليلات اليوم", "23", delta="5")

        with col3:
            st.metric("مخاطر عالية", "8", delta="-2")

        with col4:
            st.metric("معدل الامتثال", "94%", delta="3%")

        st.markdown("")  # Small spacing

        # Recent analyses section with compact design
        st.markdown("#### 📋 التحليلات الأخيرة")

        # Sample data with compact display
        recent_contracts = [
            {"title": "عقد توريد معدات", "date": "2025-01-07", "risk_score": 25, "status": "مكتمل"},
            {"title": "اتفاقية خدمات استشارية", "date": "2025-01-06", "risk_score": 65, "status": "مراجعة"},
            {"title": "عقد عمل موظف", "date": "2025-01-05", "risk_score": 15, "status": "مكتمل"}
        ]

        for contract in recent_contracts:
            with st.container():
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                with col1:
                    st.markdown(f"**{contract['title']}**")
                with col2:
                    st.caption(contract['date'])
                with col3:
                    risk_color = "🔴" if contract['risk_score'] >= 70 else "🟡" if contract['risk_score'] >= 40 else "🟢"
                    st.caption(f"{risk_color} {contract['risk_score']}%")
                with col4:
                    st.caption(contract['status'])

        st.markdown("")  # Small spacing

        # Compact quick actions
        st.markdown("#### ⚡ إجراءات سريعة")

        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("تحليل عقد جديد", use_container_width=True, type="primary"):
                self._navigate_to_page("analysis")
        with col2:
            if st.button("عرض التقارير", use_container_width=True):
                self._navigate_to_page("reports")
        with col3:
            if st.button("إدارة النماذج", use_container_width=True):
                self._navigate_to_page("templates")
    
    def _render_analysis_page(self):
        """Render contract analysis page with enhanced file processing"""
        from file_processor import FileProcessor

        st.markdown("##### 🔍 تحليل العقود")
        st.markdown("---")

        # Initialize file processor
        if 'file_processor' not in st.session_state:
            st.session_state.file_processor = FileProcessor()

        # Law selection with clean design
        st.markdown("#### ⚖️ النظام القانوني")
        selected_system = self.law_ui.render_law_selector()

        # Analysis options
        analysis_options = self.law_ui.render_analysis_options(selected_system)

        # File upload section with enhanced processor
        st.markdown("---")
        uploaded_file = st.session_state.file_processor.render_file_uploader()

        # Process uploaded file
        file_text = ""
        if uploaded_file is not None:
            with st.spinner("🔄 معالجة الملف..."):
                file_result = st.session_state.file_processor.process_uploaded_file(uploaded_file)

                if file_result.get('success'):
                    st.session_state.file_processor.display_file_info(file_result)
                    file_text = file_result.get('text', '')
                    st.success("✅ تم تحميل الملف بنجاح!")
                else:
                    st.error(f"❌ {file_result.get('error', 'خطأ في معالجة الملف')}")

        # Text input option with clean design
        st.markdown("#### 📝 إدخال نص العقد")
        contract_text = st.text_area(
            "نص العقد",
            value=file_text,  # Pre-fill with uploaded file content
            height=200,
            placeholder="الصق نص العقد هنا أو ارفع ملف أعلاه..."
        )

        # Analysis button
        if st.button(f"🔍 {t('buttons.analyze')}", type="primary", use_container_width=True):
            if contract_text.strip():
                self._perform_analysis(contract_text, analysis_options)
            else:
                st.error("يرجى رفع ملف أو إدخال نص العقد")

        # Display results if available
        if st.session_state.current_analysis:
            self._display_analysis_results()
    
    def _perform_analysis(self, contract_text, options):
        """Perform comprehensive contract analysis"""
        with st.spinner(f"{t('messages.processing')}..."):
            try:
                # Get selected legal system
                legal_system = st.session_state.get('selected_legal_system', 'kuwait')

                # Refresh analyzer with current settings
                self.analyzer = self._get_configured_analyzer()

                # Perform comprehensive analysis using AI backend
                analysis_result = self.analyzer.analyze_contract(
                    contract_text,
                    legal_system
                )

                if analysis_result.get('success'):
                    # Calculate confidence score for the analysis
                    confidence_score = self.confidence_scorer.calculate_confidence_score(
                        analysis_result.get('text', ''),
                        {
                            'contract_text': contract_text,
                            'legal_system': legal_system,
                            'analysis_type': 'comprehensive'
                        }
                    )

                    # Add confidence score to analysis result
                    analysis_result['confidence_score'] = confidence_score

                    # Store results in session state
                    st.session_state.current_analysis = analysis_result

                    # Save analysis to database
                    self._save_analysis_to_database(contract_text, analysis_result, options)

                    st.success("✅ تم إكمال التحليل وحفظه بنجاح!")
                else:
                    st.error(f"❌ خطأ في التحليل: {analysis_result.get('error', 'خطأ غير معروف')}")

            except Exception as e:
                st.error(f"❌ خطأ في التحليل: {str(e)}")
                # Fallback to basic analysis
                st.session_state.current_analysis = {
                    'success': True,
                    'text': contract_text,
                    'risk_score': 35,
                    'legal_points': [
                        {'point': 'بند الدفع واضح ومحدد', 'status': 'جيد', 'risk_level': 'منخفض'},
                        {'point': 'مدة العقد محددة بوضوح', 'status': 'جيد', 'risk_level': 'منخفض'},
                        {'point': 'شروط الإنهاء تحتاج توضيح', 'status': 'تحتاج مراجعة', 'risk_level': 'متوسط'}
                    ],
                    'recommendations': [
                        {'title': 'إضافة بند لحل النزاعات', 'priority': 'عالي', 'category': 'قانوني'},
                        {'title': 'توضيح شروط الإنهاء المبكر', 'priority': 'متوسط', 'category': 'تشغيلي'},
                        {'title': 'تحديد القانون الواجب التطبيق', 'priority': 'عالي', 'category': 'قانوني'}
                    ],
                    'summary': {
                        'contract_type': 'عقد خدمات',
                        'executive_summary': 'عقد خدمات يحتاج إلى بعض التحسينات في البنود القانونية'
                    },
                    'clauses': 8,
                    'issues': 1,
                    'pages': max(1, len(contract_text) // 2000)
                }

                # Save fallback analysis to database as well
                self._save_analysis_to_database(contract_text, st.session_state.current_analysis, options)

    def _save_analysis_to_database(self, contract_text: str, analysis_result: Dict[str, Any], options: Dict[str, Any]):
        """Save analysis result to database for history tracking"""
        try:
            # Get current user
            current_user = self.auth_manager.get_current_user()
            if not current_user:
                return  # Skip saving if no user logged in

            user_id = current_user.get('id')

            # Prepare contract data
            uploaded_file = st.session_state.get('uploaded_file')
            contract_data = {
                'filename': uploaded_file.name if uploaded_file else f"تحليل_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'file_type': uploaded_file.type if uploaded_file else 'text/plain',
                'file_size': len(contract_text.encode('utf-8')),
                'content': contract_text,
                'metadata': {
                    'legal_system': st.session_state.get('selected_legal_system', 'kuwait'),
                    'analysis_options': options,
                    'upload_timestamp': datetime.now().isoformat()
                }
            }

            # Prepare analysis data
            analysis_data = {
                'analysis_type': 'comprehensive',
                'risk_score': analysis_result.get('risk_score', 0),
                'legal_points': analysis_result.get('legal_points', []),
                'recommendations': analysis_result.get('recommendations', []),
                'summary': analysis_result.get('summary', {}),
                'translation': analysis_result.get('translation', {}),
                'contract_info': analysis_result.get('contract_info', {}),
                'text': contract_text,
                'clauses': analysis_result.get('clauses', 0),
                'issues': analysis_result.get('issues', 0),
                'pages': analysis_result.get('pages', 1)
            }

            # Save to database
            analysis_id = self.db_manager.save_analysis_result(user_id, contract_data, analysis_data)

            if analysis_id:
                # Store analysis ID in session for reference
                st.session_state.current_analysis_id = analysis_id
                logger.info(f"Analysis saved with ID: {analysis_id}")
            else:
                logger.warning("Failed to save analysis to database")

        except Exception as e:
            logger.error(f"Error saving analysis to database: {e}")
            # Don't show error to user as this is background functionality
    
    def _display_analysis_results(self):
        """Display comprehensive analysis results with export options"""
        from export_manager import ExportManager

        analysis = st.session_state.current_analysis

        if not analysis:
            st.warning("لا توجد نتائج تحليل لعرضها")
            return

        # Initialize export manager
        if 'export_manager' not in st.session_state:
            st.session_state.export_manager = ExportManager()
        
        st.markdown("#### 📊 نتائج التحليل")
        
        # Analysis summary
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📋 معلومات التحليل")
            st.write(f"**النظام القانوني:** {analysis.get('legal_system', 'غير محدد')}")
            st.write(f"**التاريخ:** {analysis.get('timestamp', 'غير محدد')}")
            st.write(f"**عدد الكلمات:** {len(analysis.get('text', '').split())}")
        
        with col2:
            # Progress indicator for analysis completion
            self.ui.render_progress_indicator(
                progress=100,
                title="اكتمال التحليل",
                subtitle="تم التحليل بنجاح"
            )

        # Display confidence score if available
        if 'confidence_score' in analysis:
            confidence = analysis['confidence_score']
            st.markdown("#### تقييم الثقة في التحليل")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                score = confidence.get('overall_confidence_score', 0)
                score_color = "🟢" if score >= 80 else "🟡" if score >= 60 else "🔴"
                st.metric("نقاط الثقة الإجمالية", f"{score_color} {score}%")

            with col2:
                level = confidence.get('confidence_level', 'unknown')
                level_text = {
                    'very_high': 'عالي جداً',
                    'high': 'عالي',
                    'medium': 'متوسط',
                    'low': 'منخفض',
                    'very_low': 'منخفض جداً',
                    'unknown': 'غير محدد'
                }.get(level, level)
                st.metric("مستوى الثقة", level_text)

            with col3:
                if 'uncertainty_metrics' in confidence:
                    uncertainty = confidence['uncertainty_metrics'].get('uncertainty_density', 0)
                    st.metric("مستوى عدم اليقين", f"{uncertainty:.2f}")

            with col4:
                if 'component_scores' in confidence:
                    legal_term_score = confidence['component_scores'].get('legal_terminology', 0)
                    st.metric("المصطلحات القانونية", f"{legal_term_score:.0f}%")

            # Reliability indicators
            if 'reliability_indicators' in confidence and confidence['reliability_indicators']:
                st.markdown("##### مؤشرات الموثوقية")
                for indicator in confidence['reliability_indicators']:
                    severity_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(indicator['severity'], "ℹ️")
                    st.warning(f"{severity_icon} **{indicator['category']}**: {indicator['message']}")

            # Recommendations
            if 'recommendations' in confidence and confidence['recommendations']:
                st.markdown("##### توصيات تحسين الثقة")
                for rec in confidence['recommendations']:
                    st.info(f"• {rec}")

            # Detailed component scores (expandable)
            if 'component_scores' in confidence:
                with st.expander("📊 تفاصيل نقاط الثقة", expanded=False):
                    scores = confidence['component_scores']

                    col1, col2 = st.columns(2)

                    with col1:
                        st.metric("ثقة اللغة", f"{scores.get('language_confidence', 0):.1f}%")
                        st.metric("تخصص المحتوى", f"{scores.get('content_specificity', 0):.1f}%")
                        st.metric("المصطلحات القانونية", f"{scores.get('legal_terminology', 0):.1f}%")

                    with col2:
                        st.metric("مستوى عدم اليقين", f"{scores.get('uncertainty_level', 0):.1f}%")
                        st.metric("اكتمال التحليل", f"{scores.get('completeness_score', 0):.1f}%")
                        st.metric("الاتساق الداخلي", f"{scores.get('consistency_score', 0):.1f}%")
        
        # Contract Translation
        if analysis.get('translation'):
            st.markdown("#### 🌐 ترجمة العقد")
            self._display_translation_section(analysis['translation'])

        # Legal points with color highlighting
        if analysis.get('legal_points'):
            st.markdown("#### ⚖️ النقاط القانونية")

            # Show color coding legend
            with st.expander("🎨 دليل ألوان المصطلحات", expanded=False):
                st.markdown("""
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 10px 0;">
                    <span style="background-color: #ffeb3b; padding: 3px 8px; border-radius: 3px; color: #333;">🟡 مصطلحات قانونية</span>
                    <span style="background-color: #4caf50; padding: 3px 8px; border-radius: 3px; color: white;">🟢 أطراف</span>
                    <span style="background-color: #f44336; padding: 3px 8px; border-radius: 3px; color: white;">🔴 مصطلحات مالية</span>
                    <span style="background-color: #2196f3; padding: 3px 8px; border-radius: 3px; color: white;">🔵 تواريخ</span>
                    <span style="background-color: #ff9800; padding: 3px 8px; border-radius: 3px; color: white;">🟠 بنود حرجة</span>
                    <span style="background-color: #9c27b0; padding: 3px 8px; border-radius: 3px; color: white;">🟣 مراجع قانونية</span>
                </div>
                """, unsafe_allow_html=True)

            for point in analysis['legal_points']:
                with st.expander(f"📋 {point.get('title', 'نقطة قانونية')}", expanded=True):
                    # Apply highlighting to the description
                    description = point.get('description', 'لا يوجد وصف')
                    highlighted_description = self._highlight_legal_terms_with_colors(description)

                    st.markdown(
                        f'<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #007bff;">'
                        f'{highlighted_description}'
                        f'</div>',
                        unsafe_allow_html=True
                    )

                    col1, col2 = st.columns(2)
                    with col1:
                        st.info(f"الأولوية: {point.get('priority', 'متوسط')}")
                    with col2:
                        st.warning(f"مستوى المخاطر: {point.get('risk_level', 'متوسط')}")
        
        # Recommendations with highlighting
        if analysis.get('recommendations'):
            st.markdown("#### 💡 التوصيات")
            for rec in analysis['recommendations']:
                with st.expander(f"💡 {rec.get('title', 'توصية')}", expanded=True):
                    # Apply highlighting to the description
                    description = rec.get('description', 'لا يوجد وصف')
                    highlighted_description = self._highlight_legal_terms_with_colors(description)

                    st.markdown(
                        f'<div style="background-color: #f0f8ff; padding: 15px; border-radius: 8px; border-right: 4px solid #28a745;">'
                        f'{highlighted_description}'
                        f'</div>',
                        unsafe_allow_html=True
                    )
                    st.success(f"الأولوية: {rec.get('priority', 'متوسط')}")

        # Enhanced export options (primary export system)
        if 'enhanced_export_manager' not in st.session_state:
            from enhanced_export_manager import EnhancedExportManager
            st.session_state.enhanced_export_manager = EnhancedExportManager()

        st.session_state.enhanced_export_manager.render_enhanced_export_options(analysis)

    def _display_translation_section(self, translation_data: dict):
        """Display contract translation section"""
        if not translation_data:
            st.warning("الترجمة غير متوفرة")
            return

        # Determine translation direction and content
        source_lang = translation_data.get('source_language', 'arabic')
        target_lang = translation_data.get('target_language', 'english')

        # Get the appropriate translation text
        if target_lang == 'arabic':
            translation_text = translation_data.get('arabic_translation', translation_data.get('english_translation', 'Translation not available'))
            translation_title = "#### 📄 الترجمة العربية الكاملة"
            text_area_label = "Arabic Translation:"
            help_text = "الترجمة العربية الكاملة للعقد"
        else:
            translation_text = translation_data.get('english_translation', translation_data.get('arabic_translation', 'Translation not available'))
            translation_title = "#### 📄 الترجمة الإنجليزية الكاملة"
            text_area_label = "English Translation:"
            help_text = "الترجمة الإنجليزية الكاملة للعقد"

        # Translation content
        with st.container():
            st.markdown(translation_title)

            # Show legal terms color coding legend
            self._display_legal_terms_color_coding()

            # Show language direction info
            if source_lang and target_lang:
                direction_text = f"من {source_lang} إلى {target_lang}" if source_lang == "arabic" else f"من الإنجليزية إلى العربية"
                st.caption(f"🔄 اتجاه الترجمة: {direction_text}")

            # Translation quality indicator
            confidence = translation_data.get('confidence_level', 'medium')
            confidence_colors = {
                'high': '🟢',
                'medium': '🟡',
                'low': '🔴'
            }
            confidence_text = {
                'high': 'عالية',
                'medium': 'متوسطة',
                'low': 'منخفضة'
            }

            col1, col2 = st.columns([3, 1])
            with col1:
                st.info(f"جودة الترجمة: {confidence_colors.get(confidence, '🟡')} {confidence_text.get(confidence, 'متوسطة')}")
            with col2:
                if st.button("📋 نسخ الترجمة", key="copy_translation"):
                    # Use JavaScript to copy to clipboard
                    copy_script = f"""
                    <script>
                    function copyToClipboard() {{
                        const text = `{translation_text.replace('`', '\\`').replace('$', '\\$')}`;
                        navigator.clipboard.writeText(text).then(function() {{
                            console.log('Text copied to clipboard');
                        }}).catch(function(err) {{
                            console.error('Could not copy text: ', err);
                        }});
                    }}
                    copyToClipboard();
                    </script>
                    """
                    st.components.v1.html(copy_script, height=0)
                    st.success("✅ تم نسخ الترجمة إلى الحافظة!")

                # Alternative: Show text area for manual copy
                with st.expander("📋 نسخ يدوي"):
                    st.text_area(
                        "انسخ النص من هنا:",
                        value=translation_text,
                        height=100,
                        key="manual_copy_area"
                    )

            # Main translation text with highlighting
            tab1, tab2 = st.tabs(["النص الكامل", "النص مع التمييز"])

            with tab1:
                st.text_area(
                    text_area_label,
                    value=translation_text,
                    height=300,
                    key="translation_display",
                    help=help_text
                )

            with tab2:
                # Apply color-coded highlighting to the complete translation
                if translation_text and translation_text != 'Translation not available':
                    highlighted_text = self._highlight_legal_terms_with_colors(translation_text)

                    # Show full highlighted text with better formatting
                    st.markdown("**النص الكامل مع تمييز المصطلحات القانونية:**")
                    st.markdown(
                        f'<div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; border-right: 4px solid #28a745; max-height: 400px; overflow-y: auto; font-size: 14px; line-height: 1.6; white-space: pre-wrap;">'
                        f'{highlighted_text}'
                        f'</div>',
                        unsafe_allow_html=True
                    )

                    # Show statistics
                    word_count = len(translation_text.split())
                    highlighted_terms = highlighted_text.count('<span style=')
                    st.caption(f"📊 إحصائيات: {word_count} كلمة، {highlighted_terms} مصطلح قانوني مميز")
                else:
                    st.warning("الترجمة غير متوفرة للتمييز")
                    st.info("يرجى التأكد من وجود ترجمة صحيحة للعقد")

            # Key terms section
            if translation_data.get('key_terms'):
                with st.expander("🔑 المصطلحات الرئيسية", expanded=False):
                    for term in translation_data['key_terms']:
                        col1, col2, col3 = st.columns([1, 1, 2])
                        with col1:
                            st.markdown(f"**{term.get('arabic', 'N/A')}**")
                        with col2:
                            st.markdown(f"*{term.get('english', 'N/A')}*")
                        with col3:
                            st.markdown(f"{term.get('explanation', '')}")

            # Translation notes
            if translation_data.get('translation_notes'):
                with st.expander("📝 ملاحظات الترجمة", expanded=False):
                    for note in translation_data['translation_notes']:
                        st.markdown(f"• {note}")

    def _render_templates_page(self):
        """Render templates management page"""
        st.markdown("##### 📚 إدارة النماذج")
        st.markdown("---")

        # Get current user
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return

        # Template management tabs
        tab1, tab2, tab3 = st.tabs(["📚 مكتبة النماذج", "➕ إنشاء نموذج", "📊 الإحصائيات"])

        with tab1:
            st.markdown("#### 📚 مكتبة النماذج")

            # Get templates
            try:
                templates = self.template_manager.get_templates()
            except:
                # Fallback if template_manager is not available
                templates = st.session_state.get('contract_templates', [])

            if templates:
                # Filter and search options
                col1, col2, col3 = st.columns(3)

                with col1:
                    search_term = st.text_input("🔍 البحث في النماذج", placeholder="ابحث في الاسم أو الوصف...")

                with col2:
                    # Get unique template types
                    template_types = list(set([t.get('template_type', t.get('category', 'أخرى')) for t in templates]))
                    selected_type = st.selectbox("تصفية حسب النوع", ["جميع الأنواع"] + template_types)

                with col3:
                    sort_by = st.selectbox("ترتيب حسب", ["الاسم", "تاريخ الإنشاء", "مرات الاستخدام"])

                # Apply filters
                filtered_templates = templates

                if search_term:
                    filtered_templates = [
                        t for t in filtered_templates
                        if search_term.lower() in t.get('name', '').lower() or
                           search_term.lower() in t.get('description', '').lower()
                    ]

                if selected_type != "جميع الأنواع":
                    filtered_templates = [
                        t for t in filtered_templates
                        if t.get('template_type', t.get('category', '')) == selected_type
                    ]

                # Apply sorting
                if sort_by == "الاسم":
                    filtered_templates.sort(key=lambda x: x.get('name', ''))
                elif sort_by == "تاريخ الإنشاء":
                    filtered_templates.sort(key=lambda x: x.get('created_at', x.get('created_date', '')), reverse=True)
                elif sort_by == "مرات الاستخدام":
                    filtered_templates.sort(key=lambda x: x.get('usage_count', 0), reverse=True)

                st.markdown(f"### عرض {len(filtered_templates)} من {len(templates)} نموذج")

                # Display templates
                for template in filtered_templates:
                    with st.expander(f"📄 {template.get('name', 'نموذج بدون اسم')}"):
                        col1, col2 = st.columns([2, 1])

                        with col1:
                            st.markdown(f"**النوع:** {template.get('template_type', template.get('category', 'غير محدد'))}")
                            st.markdown(f"**الوصف:** {template.get('description', 'لا يوجد وصف')}")
                            st.markdown(f"**تاريخ الإنشاء:** {template.get('created_at', template.get('created_date', 'غير محدد'))}")
                            st.markdown(f"**أنشأه:** {template.get('created_by', 'غير محدد')}")

                            # Show content preview
                            content = template.get('content', '')
                            if content:
                                st.markdown("**معاينة المحتوى:**")
                                preview_text = content[:500] + "..." if len(content) > 500 else content
                                st.text_area("", value=preview_text, height=150, disabled=True,
                                           key=f"preview_{template.get('id', 'unknown')}", label_visibility="collapsed")

                        with col2:
                            st.metric("مرات الاستخدام", template.get('usage_count', 0))

                            # Action buttons
                            if st.button("استخدام النموذج", key=f"use_{template.get('id', 'unknown')}",
                                       use_container_width=True, type="primary"):
                                try:
                                    self.template_manager.increment_usage(template.get('id'))
                                    st.success("✅ تم تحديد النموذج للاستخدام")
                                    # Store selected template in session state
                                    st.session_state.selected_template = template
                                except:
                                    # Fallback increment
                                    template['usage_count'] = template.get('usage_count', 0) + 1
                                    st.success("✅ تم تحديد النموذج للاستخدام")

                            if st.button("تحرير", key=f"edit_{template.get('id', 'unknown')}",
                                       use_container_width=True):
                                st.session_state.editing_template = template
                                st.info("💡 انتقل إلى تبويب 'إنشاء نموذج' لتحرير النموذج")

                            # Download button
                            if template.get('content'):
                                st.download_button(
                                    label="تحميل",
                                    data=template['content'],
                                    file_name=f"{template.get('name', 'template')}.txt",
                                    mime="text/plain",
                                    key=f"download_{template.get('id', 'unknown')}",
                                    use_container_width=True
                                )

                            # Copy to clipboard button
                            if st.button("نسخ المحتوى", key=f"copy_{template.get('id', 'unknown')}",
                                       use_container_width=True):
                                if template.get('content'):
                                    # Store in session state for copying
                                    st.session_state.clipboard_content = template['content']
                                    st.success("✅ تم نسخ المحتوى! يمكنك لصقه في أي مكان")

                # Show clipboard content if available
                if st.session_state.get('clipboard_content'):
                    with st.expander("📋 المحتوى المنسوخ"):
                        st.text_area("المحتوى المنسوخ",
                                   value=st.session_state.clipboard_content,
                                   height=100, disabled=True)
                        if st.button("مسح المحتوى المنسوخ"):
                            del st.session_state.clipboard_content
                            st.rerun()

            else:
                st.info("📝 لا توجد نماذج متاحة حالياً")
                st.markdown("""
                ### 💡 كيفية إضافة نماذج جديدة:
                1. انتقل إلى تبويب "➕ إنشاء نموذج"
                2. اختر طريقة الإدخال (نص أو ملف)
                3. املأ المعلومات المطلوبة
                4. احفظ النموذج
                """)

        with tab2:
            st.markdown("#### ➕ إنشاء نموذج جديد")

            # Template input method selection
            input_method = st.radio(
                "طريقة إدخال النموذج",
                ["📝 كتابة النص", "📁 رفع ملف"],
                horizontal=True
            )

            with st.form("create_template"):
                col1, col2 = st.columns(2)

                with col1:
                    name = st.text_input("اسم النموذج *", placeholder="مثال: عقد توريد معدات")
                    template_type = st.selectbox("نوع النموذج *",
                        ["توريد", "خدمات", "عقارات", "استشارات", "تجارة", "أخرى"])

                with col2:
                    description = st.text_area("وصف النموذج *",
                                             placeholder="وصف مختصر لاستخدام النموذج",
                                             height=100)
                    tags = st.text_input("العلامات",
                                        placeholder="مفصولة بفواصل (اختياري)")

                # Content input based on selected method
                content = ""
                uploaded_file = None

                if input_method == "📝 كتابة النص":
                    st.markdown("#### 📝 محتوى النموذج")
                    content = st.text_area(
                        "اكتب محتوى النموذج هنا *",
                        placeholder="""مثال:
عقد توريد معدات

الطرف الأول: [اسم الشركة]
الطرف الثاني: [اسم المورد]

بموجب هذا العقد يتفق الطرفان على:
1. توريد المعدات المطلوبة حسب المواصفات
2. شروط الدفع والتسليم
3. الضمانات والصيانة
4. مدة العقد وشروط التجديد

التوقيع:
الطرف الأول: ________________
الطرف الثاني: ________________
التاريخ: ________________""",
                        height=300
                    )

                else:  # File upload
                    st.markdown("#### 📁 رفع ملف النموذج")
                    uploaded_file = st.file_uploader(
                        "اختر ملف النموذج *",
                        type=['txt', 'docx', 'pdf'],
                        help="يمكن رفع ملفات نصية (.txt)، Word (.docx)، أو PDF (.pdf)"
                    )

                    if uploaded_file:
                        st.success(f"✅ تم رفع الملف: {uploaded_file.name}")

                        # Show file details
                        file_details = {
                            "اسم الملف": uploaded_file.name,
                            "نوع الملف": uploaded_file.type,
                            "حجم الملف": f"{uploaded_file.size / 1024:.1f} KB"
                        }

                        for key, value in file_details.items():
                            st.write(f"**{key}:** {value}")

                # Form submission
                col1, col2, col3 = st.columns([1, 1, 1])

                with col1:
                    submit_button = st.form_submit_button("إنشاء النموذج", type="primary", use_container_width=True)

                with col2:
                    preview_button = st.form_submit_button("معاينة", use_container_width=True)

                with col3:
                    clear_button = st.form_submit_button("مسح الحقول", use_container_width=True)

                # Handle form submission
                if submit_button:
                    # Validation
                    if not name or not template_type or not description:
                        st.error("❌ يرجى ملء جميع الحقول المطلوبة (*)")
                    elif input_method == "📝 كتابة النص" and not content:
                        st.error("❌ يرجى إدخال محتوى النموذج")
                    elif input_method == "📁 رفع ملف" and not uploaded_file:
                        st.error("❌ يرجى رفع ملف النموذج")
                    else:
                        # Process uploaded file if exists
                        if uploaded_file:
                            try:
                                if uploaded_file.type == "text/plain":
                                    content = str(uploaded_file.read(), "utf-8")
                                elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                                    # For Word files - basic text extraction
                                    try:
                                        import docx
                                        doc = docx.Document(uploaded_file)
                                        content = "\n".join([paragraph.text for paragraph in doc.paragraphs])
                                    except ImportError:
                                        content = f"[ملف Word: {uploaded_file.name}]\nيرجى تثبيت مكتبة python-docx لاستخراج النص"
                                elif uploaded_file.type == "application/pdf":
                                    # For PDF files - basic text extraction
                                    try:
                                        import PyPDF2
                                        pdf_reader = PyPDF2.PdfReader(uploaded_file)
                                        content = ""
                                        for page in pdf_reader.pages:
                                            content += page.extract_text() + "\n"
                                    except ImportError:
                                        content = f"[ملف PDF: {uploaded_file.name}]\nيرجى تثبيت مكتبة PyPDF2 لاستخراج النص"
                                else:
                                    content = f"[ملف: {uploaded_file.name}]\nنوع ملف غير مدعوم للاستخراج التلقائي"
                            except Exception as e:
                                st.error(f"❌ خطأ في قراءة الملف: {str(e)}")
                                content = f"[ملف: {uploaded_file.name}]\nخطأ في قراءة الملف"

                        # Create template
                        try:
                            template_id = self.template_manager.create_template(
                                name, description, template_type, content,
                                current_user.get('id', 'unknown'), tags
                            )
                            if template_id:
                                st.success("✅ تم إنشاء النموذج بنجاح!")
                                st.balloons()
                            else:
                                st.error("❌ فشل في إنشاء النموذج")
                        except Exception as e:
                            st.error(f"❌ خطأ في إنشاء النموذج: {str(e)}")

                # Handle preview
                if preview_button:
                    if content or uploaded_file:
                        st.markdown("### 👁️ معاينة النموذج")

                        preview_content = content
                        if uploaded_file and not content:
                            preview_content = f"[ملف مرفوع: {uploaded_file.name}]"

                        st.markdown("**معاينة المحتوى:**")
                        st.text_area("", value=preview_content[:1000] + "..." if len(preview_content) > 1000 else preview_content,
                                   height=200, disabled=True, label_visibility="collapsed")
                    else:
                        st.warning("⚠️ لا يوجد محتوى للمعاينة")

                # Handle clear
                if clear_button:
                    st.rerun()

        with tab3:
            st.markdown("#### 📊 إحصائيات النماذج")
            stats = self.template_manager.get_template_statistics()

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("إجمالي النماذج", stats.get('total_templates', 0))
            with col2:
                st.metric("النماذج النشطة", stats.get('active_templates', 0))
            with col3:
                st.metric("إجمالي الاستخدامات", stats.get('total_usage', 0))

    def _render_help_page(self):
        """Render help and documentation page"""
        self.help_system.render_help_page()

    def _render_monitoring_page(self):
        """Render monitoring and analytics page"""
        st.markdown("##### 📊 مراقبة النظام")
        st.markdown("---")

        # Check permissions
        user = self.auth_manager.get_current_user()
        if not user or user.get('role') not in ['admin', 'manager']:
            st.error("❌ ليس لديك صلاحية لعرض مراقبة النظام")
            st.info("💡 هذه الصفحة متاحة للمديرين والمشرفين فقط")
            return

        try:
            # Use the already initialized monitoring system
            monitoring = self.monitoring_system

            # Render the complete monitoring dashboard
            monitoring.render_monitoring_dashboard()





        except ImportError:
            st.error("❌ نظام المراقبة غير متوفر")
            st.info("💡 تأكد من وجود ملف monitoring_system.py")
        except Exception as e:
            st.error(f"❌ خطأ في تحميل نظام المراقبة: {str(e)}")

    def _render_database_page(self):
        """Render database page"""
        st.markdown("##### 🗄️ قاعدة البيانات")
        st.markdown("---")

        # Show database statistics
        stats = self.db_manager.get_system_stats()

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("إجمالي العقود", stats['total_contracts'])

        with col2:
            st.metric("إجمالي المستخدمين", stats['total_users'])

        with col3:
            st.metric("إجمالي التحليلات", stats['total_analyses'])

        with col4:
            st.metric("متوسط المخاطر", f"{stats['average_risk_score']}%")

        # Show user's contracts
        user = self.auth_manager.get_current_user()
        if user and user['id'] != 'guest':
            st.markdown("---")
            st.markdown("### 📄 عقودي")

            contracts = self.db_manager.get_user_contracts(user['id'])

            if contracts:
                for contract in contracts:
                    with st.expander(f"📄 {contract['filename']}", expanded=False):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown(f"**تاريخ الرفع:** {contract['created_at'][:10]}")
                            st.markdown(f"**النظام القانوني:** {contract['legal_system']}")
                            st.markdown(f"**نوع الملف:** {contract['file_type']}")

                        with col2:
                            st.metric("درجة المخاطر", f"{contract['risk_score']}%")
                            st.metric("عدد البنود", contract['clauses'])
                            st.metric("المشاكل", contract['issues'])
            else:
                st.info("لا توجد عقود محفوظة")
        else:
            st.info("سجل الدخول لعرض عقودك المحفوظة")
    
    def _render_statistics_page(self):
        """Render comprehensive statistics page"""
        st.markdown("##### 📈 الإحصائيات")
        st.markdown("---")

        # Get current user
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول لعرض الإحصائيات")
            return

        # Statistics tabs
        tab1, tab2, tab3, tab4 = st.tabs(["📊 إحصائيات عامة", "📄 تحليل العقود", "📚 النماذج", "🎯 المخاطر"])

        with tab1:
            st.markdown("### 📊 الإحصائيات العامة")

            # Get basic statistics
            try:
                # Contract analysis statistics
                contracts_analyzed = len(self.db_manager.get_user_contracts(current_user['id']))
                templates_count = len(self.template_manager.get_templates())

                # Display metrics
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("العقود المحللة", contracts_analyzed, delta="+5 هذا الشهر")

                with col2:
                    st.metric("النماذج المتاحة", templates_count, delta="+2 جديد")

                with col3:
                    st.metric("متوسط درجة المخاطر", "35%", delta="-5% تحسن")

                with col4:
                    st.metric("وقت التحليل المتوسط", "2.5 دقيقة", delta="-0.3 دقيقة")

                # Usage chart
                st.markdown("### 📈 استخدام النظام")

                # Sample data for demonstration
                import pandas as pd
                import numpy as np
                from datetime import datetime, timedelta

                # Generate sample usage data
                dates = [datetime.now() - timedelta(days=x) for x in range(30, 0, -1)]
                usage_data = pd.DataFrame({
                    'التاريخ': dates,
                    'العقود المحللة': np.random.randint(1, 10, 30),
                    'النماذج المستخدمة': np.random.randint(0, 5, 30)
                })

                st.line_chart(usage_data.set_index('التاريخ'))

            except Exception as e:
                st.error(f"خطأ في تحميل الإحصائيات: {str(e)}")

        with tab2:
            st.markdown("### 📄 إحصائيات تحليل العقود")

            # Contract types analysis
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### أنواع العقود المحللة")
                contract_types = {
                    'عقود العمل': 45,
                    'العقود التجارية': 30,
                    'عقود العقارات': 15,
                    'عقود الخدمات': 10
                }

                # Create pie chart data
                import pandas as pd
                df = pd.DataFrame(list(contract_types.items()), columns=['النوع', 'العدد'])
                st.bar_chart(df.set_index('النوع'))

            with col2:
                st.markdown("#### توزيع درجات المخاطر")
                risk_distribution = {
                    'منخفض (0-25%)': 40,
                    'متوسط (26-50%)': 35,
                    'عالي (51-75%)': 20,
                    'خطر جداً (76-100%)': 5
                }

                df_risk = pd.DataFrame(list(risk_distribution.items()), columns=['المستوى', 'النسبة'])
                st.bar_chart(df_risk.set_index('المستوى'))

        with tab3:
            st.markdown("### 📚 إحصائيات النماذج")

            # Template statistics
            template_stats = self.template_manager.get_template_statistics()

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("إجمالي النماذج", template_stats.get('total_templates', 0))
            with col2:
                st.metric("النماذج النشطة", template_stats.get('active_templates', 0))
            with col3:
                st.metric("إجمالي الاستخدامات", template_stats.get('total_usage', 0))

            # Most used templates
            st.markdown("#### النماذج الأكثر استخداماً")
            templates = self.template_manager.get_templates()
            if templates:
                # Sort by usage count
                sorted_templates = sorted(templates, key=lambda x: x.get('usage_count', 0), reverse=True)[:5]

                for i, template in enumerate(sorted_templates, 1):
                    st.markdown(f"{i}. **{template['name']}** - {template.get('usage_count', 0)} مرة")
            else:
                st.info("لا توجد نماذج متاحة")

        with tab4:
            st.markdown("### 🎯 تحليل المخاطر")

            # Risk analysis over time
            st.markdown("#### تطور درجات المخاطر")

            # Sample risk data
            risk_dates = [datetime.now() - timedelta(days=x) for x in range(30, 0, -1)]
            risk_data = pd.DataFrame({
                'التاريخ': risk_dates,
                'متوسط المخاطر': np.random.randint(20, 60, 30)
            })

            st.line_chart(risk_data.set_index('التاريخ'))

            # Risk categories
            st.markdown("#### أكثر المخاطر شيوعاً")
            common_risks = [
                "شروط الدفع غير واضحة",
                "عدم تحديد المسؤوليات",
                "شروط الإنهاء غامضة",
                "عدم وجود آلية لحل النزاعات",
                "شروط التأمين ناقصة"
            ]

            for i, risk in enumerate(common_risks, 1):
                st.markdown(f"{i}. {risk}")

            # Recommendations
            st.markdown("#### التوصيات")
            st.success("✅ تحسن عام في جودة العقود بنسبة 15%")
            st.warning("⚠️ يُنصح بمراجعة شروط الدفع في العقود الجديدة")
            st.info("💡 استخدام النماذج المحدثة يقلل المخاطر بنسبة 25%")

    def _render_guidelines_page(self):
        """Render legal guidelines management page"""
        # Check authentication
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return

        # Import and render guidelines interface
        try:
            from legal_guidelines_interface import LegalGuidelinesInterface
            guidelines_interface = LegalGuidelinesInterface()
            guidelines_interface.render_guidelines_management()
        except ImportError as e:
            st.error(f"❌ خطأ في تحميل واجهة المبادئ التوجيهية: {str(e)}")
        except Exception as e:
            st.error(f"❌ خطأ في عرض المبادئ التوجيهية: {str(e)}")

    def _render_settings_page(self):
        """Render comprehensive settings page"""
        st.markdown("##### ⚙️ الإعدادات")
        st.markdown("---")

        # Get current user and permissions
        user = self.auth_manager.get_current_user()
        permissions = self.auth_manager.get_user_permissions()

        # Settings tabs
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["🎨 المظهر", "🤖 الذكاء الاصطناعي", "⚖️ النظام القانوني", "👤 الملف الشخصي", "📋 إدارة القوائم"])

        with tab1:
            self._render_theme_settings()

        with tab2:
            self._render_ai_settings()

        with tab3:
            self._render_legal_system_settings()

        with tab4:
            self._render_profile_settings()

        with tab5:
            self._render_menu_management_settings()

    def _render_theme_settings(self):
        """Render theme and appearance settings"""
        st.markdown("#### 🎨 إعدادات المظهر")

        # Theme selection
        theme_names = self.theme_manager.get_theme_names()
        current_theme = st.session_state.get('theme', ThemeType.LIGHT.value)

        selected_theme = st.selectbox(
            "اختر المظهر",
            options=list(theme_names.keys()),
            format_func=lambda x: theme_names[x],
            index=list(theme_names.keys()).index(current_theme) if current_theme in theme_names else 0
        )

        # Theme preview
        if selected_theme != current_theme:
            st.info("💡 انقر على 'تطبيق المظهر' لرؤية التغييرات")

        if st.button("🎨 تطبيق المظهر", type="primary"):
            self.theme_manager.set_theme(ThemeType(selected_theme))
            st.session_state.theme = selected_theme
            st.success("✅ تم تغيير المظهر بنجاح!")
            st.rerun()

        st.markdown("---")

        # Language settings
        st.markdown("#### 🌐 إعدادات اللغة")
        available_languages = self.i18n.get_available_languages()
        current_lang = self.i18n.get_current_language().value

        selected_lang = st.selectbox(
            "اختر اللغة",
            options=list(available_languages.keys()),
            format_func=lambda x: available_languages[x],
            index=list(available_languages.keys()).index(current_lang)
        )

        if st.button("🌐 تطبيق اللغة", type="primary"):
            self.i18n.set_language(Language(selected_lang))
            st.success("✅ تم تغيير اللغة بنجاح!")
            st.rerun()

        st.markdown("---")

        # UI preferences
        st.markdown("#### 🖥️ تفضيلات الواجهة")

        col1, col2 = st.columns(2)

        with col1:
            show_animations = st.checkbox(
                "تفعيل الرسوم المتحركة",
                value=st.session_state.get('show_animations', True),
                help="تفعيل/إلغاء الرسوم المتحركة في الواجهة"
            )

            compact_mode = st.checkbox(
                "الوضع المضغوط",
                value=st.session_state.get('compact_mode', False),
                help="عرض أكثر كثافة للمعلومات"
            )

        with col2:
            auto_save = st.checkbox(
                "الحفظ التلقائي",
                value=st.session_state.get('auto_save', True),
                help="حفظ التحليلات تلقائياً"
            )

            show_tooltips = st.checkbox(
                "إظهار التلميحات",
                value=st.session_state.get('show_tooltips', True),
                help="إظهار تلميحات المساعدة"
            )

        if st.button("💾 حفظ تفضيلات الواجهة"):
            st.session_state.show_animations = show_animations
            st.session_state.compact_mode = compact_mode
            st.session_state.auto_save = auto_save
            st.session_state.show_tooltips = show_tooltips
            st.success("✅ تم حفظ التفضيلات!")

    def _render_ai_settings(self):
        """Render AI configuration settings"""
        st.markdown("#### 🤖 إعدادات الذكاء الاصطناعي")

        # AI Backend selection
        ai_backends = {
            'lmstudio': 'LM Studio (محلي)',
            'ollama': 'Ollama (محلي)',
            'openai': 'OpenAI (سحابي)',
            'anthropic': 'Anthropic Claude (سحابي)'
        }

        current_backend = st.session_state.get('ai_backend', 'lmstudio')

        selected_backend = st.selectbox(
            "اختر خدمة الذكاء الاصطناعي",
            options=list(ai_backends.keys()),
            format_func=lambda x: ai_backends[x],
            index=list(ai_backends.keys()).index(current_backend) if current_backend in ai_backends else 0
        )

        # Model selection based on backend
        if selected_backend in ['lmstudio', 'ollama']:
            st.markdown("#### 🔧 إعدادات النموذج المحلي")

            # Auto-load models when backend changes
            backend_key = f'available_models_{selected_backend}'
            if backend_key not in st.session_state:
                # Try to auto-load models
                try:
                    default_host = st.session_state.get('ai_host', 'localhost')
                    default_port = st.session_state.get('ai_port', 1234 if selected_backend == 'lmstudio' else 11434)

                    temp_analyzer = ContractAnalyzer(
                        backend=selected_backend,
                        model="temp",
                        host=default_host,
                        port=default_port
                    )

                    available_models = temp_analyzer.get_available_models()
                    if available_models:
                        st.session_state[backend_key] = available_models
                        st.session_state['available_models'] = available_models
                except:
                    # Silent fail - will show text input instead
                    pass

            # Check if we have available models from connection test or auto-load
            available_models = st.session_state.get('available_models', [])

            if available_models:
                # Show dropdown with available models
                current_model = st.session_state.get('ai_model', 'llama3.1:8b')
                if current_model not in available_models and available_models:
                    current_model = available_models[0]

                model_name = st.selectbox(
                    "اختر النموذج المتاح",
                    options=available_models,
                    index=available_models.index(current_model) if current_model in available_models else 0,
                    help="النماذج المتاحة في الخدمة"
                )

                # Show model count info
                st.info(f"📊 تم العثور على {len(available_models)} نموذج متاح")
            else:
                # Fallback to text input
                model_name = st.text_input(
                    "اسم النموذج",
                    value=st.session_state.get('ai_model', 'llama3.1:8b'),
                    help="اسم النموذج المستخدم في التحليل (اضغط 'اختبار الاتصال' لرؤية النماذج المتاحة)"
                )

                st.warning("⚠️ لم يتم العثور على نماذج متاحة. اضغط 'اختبار الاتصال' لتحديث القائمة.")

            # Connection settings
            col1, col2 = st.columns(2)

            with col1:
                api_host = st.text_input(
                    "عنوان الخادم",
                    value=st.session_state.get('ai_host', 'localhost'),
                    help="عنوان IP للخادم المحلي"
                )

            with col2:
                api_port = st.number_input(
                    "رقم المنفذ",
                    value=st.session_state.get('ai_port', 1234 if selected_backend == 'lmstudio' else 11434),
                    min_value=1,
                    max_value=65535
                )

            # Test connection and model
            col1, col2 = st.columns(2)

            with col1:
                if st.button("🔍 اختبار الاتصال"):
                    with st.spinner("جاري اختبار الاتصال..."):
                        try:
                            # Create analyzer with current settings
                            test_analyzer = ContractAnalyzer(
                                backend=selected_backend,
                                model=model_name,
                                host=api_host,
                                port=api_port
                            )

                            # Get available models
                            available_models = test_analyzer.get_available_models()

                            if available_models:
                                st.success("✅ تم الاتصال بنجاح!")
                                st.info(f"النماذج المتاحة: {len(available_models)}")

                                # Show available models
                                for i, model in enumerate(available_models[:5]):  # Show first 5 models
                                    st.text(f"• {model}")

                                if len(available_models) > 5:
                                    st.text(f"... و {len(available_models) - 5} نماذج أخرى")

                                # Update model dropdown with available models
                                st.session_state['available_models'] = available_models
                            else:
                                st.error("❌ لا توجد نماذج متاحة أو فشل في الاتصال")

                        except Exception as e:
                            st.error(f"❌ خطأ في الاتصال: {str(e)}")
                            st.info("💡 تأكد من تشغيل الخدمة على العنوان والمنفذ المحددين")

            with col2:
                if st.button("🧪 اختبار النموذج"):
                    with st.spinner("جاري اختبار النموذج..."):
                        try:
                            # Create analyzer with current settings
                            test_analyzer = ContractAnalyzer(
                                backend=selected_backend,
                                model=model_name,
                                host=api_host,
                                port=api_port
                            )

                            # Simple test prompt
                            test_prompt = "اختبار بسيط: ما هو العقد؟"
                            response = test_analyzer._make_api_request(test_prompt, max_tokens=100)

                            if response and len(response.strip()) > 0:
                                st.success("✅ النموذج يعمل بشكل صحيح!")
                                st.text_area("استجابة النموذج:", response[:300] + "..." if len(response) > 300 else response, height=120)
                            else:
                                st.error("❌ النموذج لا يستجيب بشكل صحيح")

                        except Exception as e:
                            st.error(f"❌ خطأ في اختبار النموذج: {str(e)}")
                            st.info("💡 تأكد من أن النموذج محمل ومتاح في الخدمة")

        elif selected_backend in ['openai', 'anthropic']:
            st.markdown("#### 🔑 إعدادات API")

            api_key = st.text_input(
                "مفتاح API",
                type="password",
                help="أدخل مفتاح API الخاص بك"
            )

            if selected_backend == 'openai':
                model_options = ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo']
            else:
                model_options = ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku']

            selected_model = st.selectbox(
                "اختر النموذج",
                options=model_options
            )

        # Analysis settings
        st.markdown("---")
        st.markdown("#### ⚙️ إعدادات التحليل")

        col1, col2 = st.columns(2)

        with col1:
            analysis_depth = st.selectbox(
                "عمق التحليل",
                options=['basic', 'standard', 'comprehensive'],
                format_func=lambda x: {'basic': 'أساسي', 'standard': 'قياسي', 'comprehensive': 'شامل'}[x],
                index=1
            )

            max_tokens = st.number_input(
                "الحد الأقصى للرموز",
                value=st.session_state.get('max_tokens', 4000),
                min_value=1000,
                max_value=32000,
                step=500
            )

        with col2:
            temperature = st.slider(
                "درجة الإبداع",
                min_value=0.0,
                max_value=1.0,
                value=st.session_state.get('temperature', 0.3),
                step=0.1,
                help="قيم أعلى = إجابات أكثر إبداعاً"
            )

            timeout = st.number_input(
                "مهلة الاستجابة (ثانية)",
                value=st.session_state.get('ai_timeout', 60),
                min_value=10,
                max_value=300,
                step=10
            )

        # Save AI settings
        if st.button("💾 حفظ إعدادات الذكاء الاصطناعي", type="primary"):
            st.session_state.ai_backend = selected_backend
            st.session_state.ai_model = model_name if selected_backend in ['lmstudio', 'ollama'] else selected_model
            st.session_state.ai_host = api_host if selected_backend in ['lmstudio', 'ollama'] else None
            st.session_state.ai_port = api_port if selected_backend in ['lmstudio', 'ollama'] else None
            st.session_state.analysis_depth = analysis_depth
            st.session_state.max_tokens = max_tokens
            st.session_state.temperature = temperature
            st.session_state.ai_timeout = timeout

            st.success("✅ تم حفظ إعدادات الذكاء الاصطناعي!")

    def _render_legal_system_settings(self):
        """Render legal system preferences"""
        st.markdown("#### ⚖️ إعدادات النظام القانوني")

        # Default legal system
        legal_systems = {
            'kuwait': 'القانون الكويتي',
            'saudi_arabia': 'القانون السعودي'
        }

        default_legal_system = st.selectbox(
            "النظام القانوني الافتراضي",
            options=list(legal_systems.keys()),
            format_func=lambda x: legal_systems[x],
            index=0
        )

        # Analysis preferences
        st.markdown("#### 📋 تفضيلات التحليل")

        col1, col2 = st.columns(2)

        with col1:
            include_risk_assessment = st.checkbox(
                "تضمين تقييم المخاطر",
                value=True,
                help="تضمين تحليل مفصل للمخاطر"
            )

            include_compliance_check = st.checkbox(
                "فحص الامتثال القانوني",
                value=True,
                help="فحص مدى امتثال العقد للقوانين"
            )

        with col2:
            include_recommendations = st.checkbox(
                "تضمين التوصيات",
                value=True,
                help="إنشاء توصيات لتحسين العقد"
            )

            detailed_analysis = st.checkbox(
                "التحليل المفصل",
                value=False,
                help="تحليل أكثر تفصيلاً (يستغرق وقتاً أطول)"
            )

        # Risk thresholds
        st.markdown("#### 🎯 عتبات المخاطر")

        col1, col2, col3 = st.columns(3)

        with col1:
            low_risk_threshold = st.number_input(
                "عتبة المخاطر المنخفضة",
                value=30,
                min_value=0,
                max_value=100,
                help="النقاط أقل من هذه القيمة تعتبر مخاطر منخفضة"
            )

        with col2:
            medium_risk_threshold = st.number_input(
                "عتبة المخاطر المتوسطة",
                value=70,
                min_value=low_risk_threshold,
                max_value=100,
                help="النقاط بين المنخفضة وهذه القيمة تعتبر مخاطر متوسطة"
            )

        with col3:
            st.info(f"المخاطر العالية: أكثر من {medium_risk_threshold}")

        if st.button("💾 حفظ إعدادات النظام القانوني", type="primary"):
            st.session_state.default_legal_system = default_legal_system
            st.session_state.include_risk_assessment = include_risk_assessment
            st.session_state.include_compliance_check = include_compliance_check
            st.session_state.include_recommendations = include_recommendations
            st.session_state.detailed_analysis = detailed_analysis
            st.session_state.low_risk_threshold = low_risk_threshold
            st.session_state.medium_risk_threshold = medium_risk_threshold

            st.success("✅ تم حفظ إعدادات النظام القانوني!")

    def _render_profile_settings(self):
        """Render user profile settings"""
        st.markdown("#### 👤 الملف الشخصي")

        user = self.auth_manager.get_current_user()

        if not user or user['id'] == 'guest':
            st.info("سجل الدخول لعرض وتعديل الملف الشخصي")
            return

        # User information
        st.markdown("#### 📝 المعلومات الشخصية")

        col1, col2 = st.columns(2)

        with col1:
            full_name = st.text_input(
                "الاسم الكامل",
                value=user.get('full_name', ''),
                disabled=True  # Read-only for now
            )

            username = st.text_input(
                "اسم المستخدم",
                value=user.get('username', ''),
                disabled=True  # Read-only for now
            )

        with col2:
            email = st.text_input(
                "البريد الإلكتروني",
                value=user.get('email', ''),
                disabled=True  # Read-only for now
            )

            role = st.text_input(
                "الدور",
                value=user.get('role', '').title(),
                disabled=True
            )

        # Account statistics
        st.markdown("---")
        st.markdown("#### 📊 إحصائيات الحساب")

        if user['id'] != 'guest':
            contracts = self.db_manager.get_user_contracts(user['id'])

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("العقود المحللة", len(contracts))

            with col2:
                avg_risk = sum(c['risk_score'] for c in contracts) / len(contracts) if contracts else 0
                st.metric("متوسط المخاطر", f"{avg_risk:.1f}%")

            with col3:
                total_issues = sum(c['issues'] for c in contracts)
                st.metric("إجمالي المشاكل", total_issues)

            with col4:
                total_clauses = sum(c['clauses'] for c in contracts)
                st.metric("إجمالي البنود", total_clauses)

        # Security settings
        st.markdown("---")
        st.markdown("#### 🔒 إعدادات الأمان")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔑 تغيير كلمة المرور"):
                st.info("ميزة تغيير كلمة المرور قيد التطوير")

        with col2:
            if st.button("🚪 تسجيل الخروج من جميع الأجهزة"):
                st.info("ميزة تسجيل الخروج من جميع الأجهزة قيد التطوير")

        # Data export
        st.markdown("---")
        st.markdown("#### 📥 تصدير البيانات")

        if st.button("📊 تصدير بيانات الحساب"):
            st.info("ميزة تصدير البيانات قيد التطوير")

    def _render_history_page(self):
        """Render comprehensive analysis history page"""
        st.markdown("##### 📋 سجل تحليل العقود")
        st.markdown("---")

        # Get current user
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return

        user_id = current_user.get('id')

        # Header with search and filters
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            search_term = st.text_input("🔍 البحث في السجل", placeholder="ابحث بالاسم أو المحتوى...")

        with col2:
            page_size = st.selectbox("عدد النتائج", [10, 25, 50, 100], index=1)

        with col3:
            if st.button("🔄 تحديث"):
                st.rerun()

        # Get analysis history from database
        if search_term:
            history = self.db_manager.search_analysis_history(user_id, search_term, page_size)
        else:
            history = self.db_manager.get_user_analysis_history(user_id, page_size)

        if not history:
            if search_term:
                st.info(f"🔍 لم يتم العثور على نتائج للبحث: '{search_term}'")
            else:
                st.info("📝 لا يوجد تاريخ تحليل متاح")
                st.markdown("قم بتحليل عقد أولاً لرؤية التاريخ هنا")
            return

        # Statistics
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("📊 إجمالي التحليلات", len(history))

        with col2:
            avg_risk = sum(h.get('risk_score', 0) for h in history) / len(history) if history else 0
            st.metric("⚠️ متوسط المخاطر", f"{avg_risk:.1f}")

        with col3:
            total_size = sum(h.get('file_size', 0) for h in history)
            st.metric("📁 حجم الملفات", f"{total_size/1024:.1f} KB")

        with col4:
            file_types = set(h.get('file_type', 'unknown') for h in history)
            st.metric("📄 أنواع الملفات", len(file_types))

        st.markdown("---")

        # Display history with enhanced cards
        for i, analysis in enumerate(history):
            self._render_analysis_history_card(analysis, i + 1)

    def _render_analysis_history_card(self, analysis: Dict[str, Any], index: int):
        """Render individual analysis history card"""
        # Parse datetime
        try:
            created_at = datetime.fromisoformat(analysis['created_at'].replace('Z', '+00:00'))
            date_str = created_at.strftime('%Y-%m-%d %H:%M')
        except:
            date_str = analysis.get('created_at', 'غير محدد')

        # Risk level color
        risk_score = analysis.get('risk_score', 0)
        if risk_score >= 70:
            risk_color = "🔴"
            risk_level = "عالي"
        elif risk_score >= 40:
            risk_color = "🟡"
            risk_level = "متوسط"
        else:
            risk_color = "🟢"
            risk_level = "منخفض"

        # File size formatting
        file_size = analysis.get('file_size', 0)
        if file_size > 1024:
            size_str = f"{file_size/1024:.1f} KB"
        else:
            size_str = f"{file_size} B"

        with st.expander(f"📄 {index}. {analysis.get('filename', 'ملف غير محدد')} - {date_str}", expanded=False):
            # Header row with key metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("مستوى المخاطر", f"{risk_color} {risk_level}", f"{risk_score}%")

            with col2:
                st.metric("نوع الملف", analysis.get('file_type', 'غير محدد').upper())

            with col3:
                st.metric("حجم الملف", size_str)

            with col4:
                st.metric("نوع التحليل", analysis.get('analysis_type', 'شامل'))

            # Action buttons
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                if st.button("👁️ عرض التفاصيل", key=f"view_{analysis['analysis_id']}"):
                    self._show_analysis_details(analysis)

            with col2:
                if st.button("📄 إعادة تصدير", key=f"export_{analysis['analysis_id']}"):
                    self._re_export_analysis(analysis)

            with col3:
                if st.button("📋 نسخ", key=f"copy_{analysis['analysis_id']}"):
                    self._duplicate_analysis(analysis)

            with col4:
                if st.button("🗑️ حذف", key=f"delete_{analysis['analysis_id']}", type="secondary"):
                    self._delete_analysis(analysis)

            # Quick preview of legal points and recommendations
            legal_points = analysis.get('legal_points', [])
            recommendations = analysis.get('recommendations', [])

            if legal_points or recommendations:
                st.markdown("---")
                col1, col2 = st.columns(2)

                with col1:
                    if legal_points:
                        st.markdown("**📋 النقاط القانونية:**")
                        for point in legal_points[:3]:  # Show first 3
                            if isinstance(point, dict):
                                st.write(f"• {point.get('title', 'نقطة قانونية')}")
                            else:
                                st.write(f"• {str(point)[:50]}...")
                        if len(legal_points) > 3:
                            st.caption(f"... و {len(legal_points) - 3} نقاط أخرى")

                with col2:
                    if recommendations:
                        st.markdown("**💡 التوصيات:**")
                        for rec in recommendations[:3]:  # Show first 3
                            if isinstance(rec, dict):
                                st.write(f"• {rec.get('title', 'توصية')}")
                            else:
                                st.write(f"• {str(rec)[:50]}...")
                        if len(recommendations) > 3:
                            st.caption(f"... و {len(recommendations) - 3} توصيات أخرى")

    def _show_analysis_details(self, analysis: Dict[str, Any]):
        """Show detailed analysis in a modal-like expander"""
        analysis_id = analysis['analysis_id']
        user_id = self.auth_manager.get_current_user().get('id')

        # Get full analysis details
        full_analysis = self.db_manager.get_analysis_by_id(analysis_id, user_id)

        if not full_analysis:
            st.error("❌ لم يتم العثور على تفاصيل التحليل")
            return

        # Store in session state to show in modal
        st.session_state.show_analysis_details = full_analysis
        st.rerun()

    def _re_export_analysis(self, analysis: Dict[str, Any]):
        """Re-export analysis to PDF/Word"""
        try:
            # Prepare analysis data for export
            export_data = {
                'text': analysis.get('content', ''),
                'risk_score': analysis.get('risk_score', 0),
                'legal_points': analysis.get('legal_points', []),
                'recommendations': analysis.get('recommendations', []),
                'contract_info': {
                    'filename': analysis.get('filename', 'عقد'),
                    'type': analysis.get('file_type', 'unknown'),
                    'analysis_date': analysis.get('created_at', ''),
                }
            }

            # Export as PDF
            pdf_data = self.export_manager.export_comprehensive_analysis(export_data, 'pdf')

            if pdf_data:
                st.download_button(
                    label="📄 تحميل PDF",
                    data=pdf_data,
                    file_name=f"re_export_{analysis.get('filename', 'analysis')}.pdf",
                    mime="application/pdf",
                    key=f"download_pdf_{analysis['analysis_id']}"
                )
                st.success("✅ تم إعداد ملف PDF للتحميل")
            else:
                st.error("❌ فشل في إعادة تصدير التحليل")

        except Exception as e:
            st.error(f"❌ خطأ في إعادة التصدير: {str(e)}")

    def _duplicate_analysis(self, analysis: Dict[str, Any]):
        """Create a copy of the analysis"""
        try:
            # Create a copy with new timestamp
            new_analysis = analysis.copy()
            new_analysis['created_at'] = datetime.now().isoformat()
            new_analysis['filename'] = f"نسخة من {analysis.get('filename', 'عقد')}"

            # Save as new analysis
            user_id = self.auth_manager.get_current_user().get('id')
            contract_data = {
                'filename': new_analysis['filename'],
                'file_type': analysis.get('file_type', 'copy'),
                'content': analysis.get('content', ''),
                'file_size': analysis.get('file_size', 0)
            }

            analysis_data = {
                'risk_score': analysis.get('risk_score', 0),
                'legal_points': analysis.get('legal_points', []),
                'recommendations': analysis.get('recommendations', []),
                'analysis_type': 'duplicate'
            }

            new_id = self.db_manager.save_analysis_result(user_id, contract_data, analysis_data)

            if new_id:
                st.success("✅ تم إنشاء نسخة من التحليل بنجاح")
                st.rerun()
            else:
                st.error("❌ فشل في إنشاء نسخة من التحليل")

        except Exception as e:
            st.error(f"❌ خطأ في النسخ: {str(e)}")

    def _delete_analysis(self, analysis: Dict[str, Any]):
        """Delete analysis with confirmation"""
        if st.session_state.get(f"confirm_delete_{analysis['analysis_id']}", False):
            user_id = self.auth_manager.get_current_user().get('id')

            if self.db_manager.delete_analysis(analysis['analysis_id'], user_id):
                st.success("✅ تم حذف التحليل بنجاح")
                st.rerun()
            else:
                st.error("❌ فشل في حذف التحليل")
        else:
            st.warning("⚠️ هل أنت متأكد من حذف هذا التحليل؟")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ نعم، احذف", key=f"confirm_yes_{analysis['analysis_id']}"):
                    st.session_state[f"confirm_delete_{analysis['analysis_id']}"] = True
                    st.rerun()
            with col2:
                if st.button("❌ إلغاء", key=f"confirm_no_{analysis['analysis_id']}"):
                    if f"confirm_delete_{analysis['analysis_id']}" in st.session_state:
                        del st.session_state[f"confirm_delete_{analysis['analysis_id']}"]

    def _render_risk_page(self):
        """Render risk analysis page"""
        st.markdown("##### ⚠️ تحليل المخاطر")
        st.markdown("---")

        st.markdown("""
        ### 🔍 تحليل المخاطر الشامل

        هذه الصفحة تقدم تحليلاً متقدماً للمخاطر القانونية والمالية والتشغيلية في العقود.
        """)

        # Risk categories
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### ⚖️ المخاطر القانونية")
            st.markdown("""
            - عدم الامتثال للقوانين
            - شروط غامضة أو متضاربة
            - نقص في الضمانات القانونية
            - مشاكل في الاختصاص القضائي
            """)

        with col2:
            st.markdown("#### 💰 المخاطر المالية")
            st.markdown("""
            - شروط دفع غير واضحة
            - عدم تحديد العملة
            - نقص في ضمانات الدفع
            - غرامات مفرطة أو غير عادلة
            """)

        with col3:
            st.markdown("#### 🔧 المخاطر التشغيلية")
            st.markdown("""
            - مواعيد تسليم غير واقعية
            - مواصفات غير واضحة
            - نقص في خطط الطوارئ
            - عدم تحديد المسؤوليات
            """)

        st.markdown("---")
        st.info("💡 لتحليل مخاطر عقد محدد، انتقل إلى صفحة التحليل وارفع العقد")

    def _render_collaboration_page(self):
        """Render collaboration and comments page"""
        st.markdown("##### 👥 التعاون والتعليقات")
        st.markdown("---")

        st.markdown("""
        ### 👥 منصة التعاون القانوني

        تتيح هذه المنصة للفرق القانونية التعاون في مراجعة وتحليل العقود.
        """)

        # Collaboration features
        tab1, tab2, tab3 = st.tabs(["💬 التعليقات", "👥 الفريق", "📝 المراجعات"])

        with tab1:
            st.markdown("#### 💬 نظام التعليقات")

            # Import collaboration system
            try:
                from collaboration_system import CollaborationSystem
                collaboration = CollaborationSystem()

                # Get current user
                current_user = self.auth_manager.get_current_user()
                if not current_user:
                    st.error("❌ يجب تسجيل الدخول للوصول إلى نظام التعاون")
                    return

                user_id = current_user.get('id')

                # Contract selection for comments
                contracts = self.db_manager.get_user_contracts(user_id) if hasattr(self, 'db_manager') else []
                if contracts:
                    contract_options = {f"{c['title']} ({c['id'][:8]})": c['id'] for c in contracts}
                    selected_contract = st.selectbox("اختر العقد للتعليق عليه", list(contract_options.keys()))

                    if selected_contract:
                        contract_id = contract_options[selected_contract]

                        # Add new comment
                        col1, col2 = st.columns([3, 1])

                        with col1:
                            comment_text = st.text_area("نص التعليق", placeholder="اكتب تعليقك هنا...")
                            section_ref = st.text_input("مرجع القسم (اختياري)", placeholder="مثال: البند 3.2")

                        with col2:
                            comment_type = st.selectbox("نوع التعليق", ["general", "legal_issue", "suggestion", "question"])
                            priority = st.selectbox("الأولوية", ["low", "medium", "high"])

                        if st.button("إرسال التعليق", type="primary"):
                            if comment_text.strip():
                                comment_id = collaboration.add_comment(
                                    contract_id, user_id, comment_text,
                                    comment_type, section_ref, priority
                                )
                                if comment_id:
                                    st.success("✅ تم إرسال التعليق بنجاح!")
                                    st.rerun()
                                else:
                                    st.error("❌ فشل في إرسال التعليق")
                            else:
                                st.error("❌ يرجى كتابة نص التعليق")

                        st.markdown("---")

                        # Display existing comments
                        st.markdown("##### التعليقات الموجودة")
                        comments = collaboration.get_contract_comments(contract_id)

                        if comments:
                            for comment in comments:
                                with st.container():
                                    col1, col2, col3 = st.columns([3, 1, 1])

                                    with col1:
                                        st.markdown(f"**{comment['author_name']}** - {comment['comment_type']}")
                                        st.markdown(comment['comment_text'])
                                        if comment['section_reference']:
                                            st.caption(f"📍 {comment['section_reference']}")

                                    with col2:
                                        priority_colors = {'low': '🟢', 'medium': '🟡', 'high': '🔴'}
                                        st.markdown(f"**الأولوية:** {priority_colors.get(comment['priority'], '⚪')} {comment['priority']}")
                                        st.caption(comment['created_at'])

                                    with col3:
                                        status_colors = {'open': '🟡', 'resolved': '🟢', 'closed': '🔴'}
                                        st.markdown(f"**الحالة:** {status_colors.get(comment['status'], '⚪')} {comment['status']}")

                                    st.markdown("---")
                        else:
                            st.info("📝 لا توجد تعليقات على هذا العقد")
                else:
                    st.info("📄 لا توجد عقود متاحة للتعليق عليها")

            except ImportError:
                st.error("❌ نظام التعاون غير متوفر")
                st.info("💡 تأكد من وجود ملف collaboration_system.py")
            except Exception as e:
                st.error(f"❌ خطأ في تحميل نظام التعاون: {str(e)}")

        with tab2:
            st.markdown("#### 👥 إدارة الفريق")

            # Team collaboration features
            try:
                from user_management import UserManager
                user_manager = UserManager()
                all_users = user_manager.get_all_users()

                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("##### أعضاء الفريق المتاحون")
                    for user in all_users[:10]:  # Show first 10 users
                        role_colors = {'admin': '🔴', 'manager': '🟠', 'analyst': '🟡', 'user': '🟢'}
                        role_color = role_colors.get(user.get('role', 'user'), '⚪')
                        status = "نشط" if user.get('is_active', True) else "معطل"
                        st.markdown(f"- {role_color} **{user.get('full_name', 'N/A')}** ({user.get('role', 'user')}) - {status}")

                with col2:
                    st.markdown("##### إحصائيات الفريق")
                    stats = user_manager.get_user_statistics()
                    st.metric("إجمالي المستخدمين", stats.get('total_users', 0))
                    st.metric("المستخدمون النشطون", stats.get('active_users', 0))
                    st.metric("معدل النشاط", f"{stats.get('activity_rate', 0):.1f}%")

            except ImportError:
                st.error("❌ نظام إدارة المستخدمين غير متوفر")
            except Exception as e:
                st.error(f"❌ خطأ في تحميل بيانات الفريق: {str(e)}")

        with tab3:
            st.markdown("#### 📝 سجل المراجعات")

            # Review workflows
            try:
                from collaboration_system import CollaborationSystem
                collaboration = CollaborationSystem()

                current_user = self.auth_manager.get_current_user()
                if current_user:
                    user_id = current_user.get('id')

                    col1, col2 = st.columns(2)

                    with col1:
                        st.markdown("##### إنشاء مراجعة جديدة")

                        workflow_name = st.text_input("اسم المراجعة", placeholder="مثال: مراجعة قانونية أولية")

                        # Select reviewers
                        from user_management import UserManager
                        user_manager = UserManager()
                        all_users = user_manager.get_all_users()
                        reviewer_options = {f"{u['full_name']} ({u['username']})": u['id'] for u in all_users if u['id'] != user_id}

                        if reviewer_options:
                            selected_reviewers = st.multiselect("اختر المراجعين", list(reviewer_options.keys()))
                            deadline = st.date_input("الموعد النهائي للمراجعة")

                            if st.button("إنشاء مراجعة"):
                                if workflow_name and selected_reviewers:
                                    reviewer_ids = [reviewer_options[r] for r in selected_reviewers]
                                    workflow_id = collaboration.create_review_workflow(
                                        "sample_contract_id",  # This would be selected from contracts
                                        workflow_name,
                                        user_id,
                                        reviewer_ids,
                                        deadline.isoformat() if deadline else None
                                    )
                                    if workflow_id:
                                        st.success("✅ تم إنشاء المراجعة بنجاح!")
                                    else:
                                        st.error("❌ فشل في إنشاء المراجعة")

                    with col2:
                        st.markdown("##### إحصائيات التعاون")
                        stats = collaboration.get_collaboration_statistics()
                        st.metric("إجمالي التعليقات", stats.get('total_comments', 0))
                        st.metric("المراجعات النشطة", stats.get('active_workflows', 0))
                        st.metric("العقود المشاركة", stats.get('shared_contracts', 0))
                        st.metric("التعليقات الأخيرة", stats.get('recent_comments', 0))

            except ImportError:
                st.error("❌ نظام التعاون غير متوفر")
            except Exception as e:
                st.error(f"❌ خطأ في تحميل نظام المراجعات: {str(e)}")

    def _render_reports_page(self):
        """Render advanced reports page"""
        st.markdown("##### 📊 التقارير المتقدمة")
        st.markdown("---")

        st.markdown("""
        ### 📈 تقارير شاملة وتحليلات متقدمة

        إنشاء تقارير مفصلة حول أداء العقود والمخاطر والامتثال.
        """)

        # Report types
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📋 أنواع التقارير")
            report_types = [
                "📊 تقرير المخاطر الشامل",
                "📈 تحليل الأداء الشهري",
                "⚖️ تقرير الامتثال القانوني",
                "💰 تحليل التكاليف والفوائد",
                "🔍 تقرير مراجعة العقود",
                "📋 ملخص تنفيذي للإدارة"
            ]

            for report_type in report_types:
                if st.button(report_type, use_container_width=True):
                    self._generate_advanced_report(report_type)

        with col2:
            st.markdown("#### ⚙️ إعدادات التقرير")

            # Report settings
            date_range = st.date_input("نطاق التاريخ", value=[])
            report_format = st.selectbox("تنسيق التقرير", ["PDF", "Excel", "Word"])
            include_charts = st.checkbox("تضمين الرسوم البيانية", value=True)
            include_recommendations = st.checkbox("تضمين التوصيات", value=True)

            if st.button("إنشاء التقرير", type="primary"):
                self._generate_custom_report(date_range, report_format, include_charts, include_recommendations)

    def _render_advanced_page(self):
        """Render advanced analysis page"""
        st.markdown("##### 🔬 التحليل المتقدم")
        st.markdown("---")

        st.markdown("""
        ### 🔬 أدوات التحليل المتقدمة

        أدوات متخصصة للتحليل العميق والمتقدم للعقود القانونية.
        """)

        # Advanced features
        tab1, tab2, tab3, tab4 = st.tabs(["🤖 الذكاء الاصطناعي", "📊 التحليل الإحصائي", "🔍 البحث المتقدم", "⚙️ الإعدادات"])

        with tab1:
            st.markdown("#### 🤖 إعدادات الذكاء الاصطناعي المتقدمة")

            ai_model = st.selectbox("نموذج الذكاء الاصطناعي",
                                   ["arabic-law-meta-qwen3-8b-base", "gpt-4", "claude-3"])
            temperature = st.slider("درجة الإبداع", 0.0, 1.0, 0.7)
            max_tokens = st.slider("الحد الأقصى للكلمات", 100, 4000, 2000)

            if st.button("حفظ إعدادات الذكاء الاصطناعي"):
                st.success("تم حفظ الإعدادات بنجاح!")

        with tab2:
            st.markdown("#### 📊 التحليل الإحصائي المتقدم")

            # Statistical analysis implementation
            try:
                from advanced_reporting import ReportGenerator
                report_generator = ReportGenerator()

                # Get analytics data
                analytics = report_generator.generate_contract_analytics_report(30)

                if analytics:
                    st.success("✅ تم تحميل البيانات الإحصائية بنجاح!")

                    # Statistical metrics
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("إجمالي العقود", analytics['summary'].get('total_contracts', 0))
                    with col2:
                        st.metric("متوسط المخاطر", f"{analytics['summary'].get('avg_risk_score', 0):.2f}")
                    with col3:
                        st.metric("نسبة المخاطر العالية", f"{analytics['summary'].get('high_risk_percentage', 0):.1f}%")
                    with col4:
                        total_contracts = analytics['summary'].get('total_contracts', 0)
                        efficiency = (total_contracts / 30) if total_contracts > 0 else 0
                        st.metric("معدل التحليل اليومي", f"{efficiency:.1f}")

                    # Advanced charts
                    if analytics.get('trends'):
                        st.markdown("##### اتجاهات المخاطر")
                        fig_risk = report_generator.create_risk_analysis_chart(analytics['trends'])
                        st.plotly_chart(fig_risk, use_container_width=True)

                    if analytics.get('contract_types'):
                        st.markdown("##### توزيع أنواع العقود")
                        fig_types = report_generator.create_contract_types_chart(analytics['contract_types'])
                        st.plotly_chart(fig_types, use_container_width=True)

                    # Detailed statistics table
                    st.markdown("##### إحصائيات تفصيلية")
                    if analytics.get('contract_types'):
                        import pandas as pd
                        df = pd.DataFrame(analytics['contract_types'])
                        st.dataframe(df, use_container_width=True)
                else:
                    st.error("❌ فشل في تحميل البيانات الإحصائية")

            except ImportError:
                st.error("❌ نظام التقارير المتقدمة غير متوفر")
            except Exception as e:
                st.error(f"❌ خطأ في التحليل الإحصائي: {str(e)}")

            # Sample statistical analysis
            st.markdown("**المقاييس الإحصائية:**")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("متوسط المخاطر", "65%")
            with col2:
                st.metric("معدل الامتثال", "87%")
            with col3:
                st.metric("كفاءة المراجعة", "92%")

        with tab3:
            st.markdown("#### 🔍 البحث المتقدم في العقود")

            search_query = st.text_input("استعلام البحث", placeholder="ابحث في العقود...")
            search_type = st.selectbox("نوع البحث", ["نص كامل", "كلمات مفتاحية", "تعبيرات منتظمة"])

            if st.button("بحث متقدم"):
                self._perform_advanced_search(search_query, search_type)

        with tab4:
            st.markdown("#### ⚙️ إعدادات التحليل المتقدم")

            enable_deep_analysis = st.checkbox("تفعيل التحليل العميق", value=True)
            enable_risk_prediction = st.checkbox("تفعيل توقع المخاطر", value=True)
            enable_auto_suggestions = st.checkbox("تفعيل الاقتراحات التلقائية", value=True)

            if st.button("حفظ الإعدادات المتقدمة"):
                st.success("تم حفظ الإعدادات المتقدمة!")

    def _render_insights_page(self):
        """Render AI insights page"""
        st.markdown("##### 💡 رؤى الذكاء الاصطناعي")
        st.markdown("---")

        st.markdown("""
        ### 💡 رؤى ذكية مدعومة بالذكاء الاصطناعي

        اكتشف أنماط وتوجهات مخفية في عقودك باستخدام تقنيات الذكاء الاصطناعي المتقدمة.
        """)

        # AI Insights sections
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🔮 التوقعات الذكية")
            st.info("🔍 تحليل الاتجاهات المستقبلية للمخاطر")
            st.warning("⚠️ توقع مشاكل محتملة في العقود الجديدة")
            st.success("✅ اقتراحات لتحسين جودة العقود")

            # Sample predictions
            st.markdown("**توقعات هذا الشهر:**")
            st.markdown("- 📈 زيادة متوقعة في مخاطر الدفع بنسبة 15%")
            st.markdown("- 📉 تحسن في جودة العقود بنسبة 8%")
            st.markdown("- ⚖️ زيادة في قضايا الامتثال بنسبة 5%")

        with col2:
            st.markdown("#### 🎯 التوصيات الذكية")

            recommendations = [
                "🔧 تحديث شروط الدفع في النماذج الجديدة",
                "📋 إضافة بنود حماية إضافية للمخاطر العالية",
                "⚖️ مراجعة شروط الامتثال للقوانين الجديدة",
                "💰 تحسين آليات ضمان الدفع",
                "📝 توحيد صيغ البنود القانونية"
            ]

            for rec in recommendations:
                st.markdown(f"• {rec}")

        st.markdown("---")

        # AI Analysis Results
        st.markdown("#### 🧠 نتائج التحليل الذكي")

        tab1, tab2, tab3 = st.tabs(["📊 الأنماط", "🔍 الاكتشافات", "💡 الاقتراحات"])

        with tab1:
            st.markdown("**الأنماط المكتشفة:**")
            st.markdown("- العقود التي تحتوي على شروط دفع مرنة لديها مخاطر أقل بنسبة 23%")
            st.markdown("- العقود طويلة المدى تتطلب مراجعة إضافية للبنود التقنية")
            st.markdown("- العقود مع شركات جديدة تحتاج ضمانات إضافية")

        with tab2:
            st.markdown("**اكتشافات مهمة:**")
            st.markdown("- 🔍 تم اكتشاف 3 أنماط جديدة للمخاطر المالية")
            st.markdown("- 📈 تحسن عام في جودة العقود بنسبة 12% هذا الربع")
            st.markdown("- ⚠️ زيادة في استخدام مصطلحات قانونية غامضة")

        with tab3:
            st.markdown("**اقتراحات للتحسين:**")
            st.markdown("- 📝 إنشاء قاموس موحد للمصطلحات القانونية")
            st.markdown("- 🔄 تحديث النماذج القياسية كل 6 أشهر")
            st.markdown("- 📊 تطبيق نظام تقييم تلقائي للمخاطر")

    def _set_page(self, page: str):
        """Set current page"""
        st.session_state.selected_page = page
        st.rerun()

    def _render_users_page(self):
        """Render user management page"""
        st.markdown("##### 👤 إدارة المستخدمين")
        st.markdown("---")

        # Check if user has admin permissions
        current_user = self.auth_manager.get_current_user()
        if not current_user or current_user.get('role') != 'admin':
            st.error("❌ ليس لديك صلاحية للوصول إلى إدارة المستخدمين")
            st.info("💡 هذه الصفحة متاحة للمديرين فقط")
            return

        # Import user management system
        try:
            from user_management import UserManager
            user_manager = UserManager()

            # User management tabs
            tab1, tab2, tab3 = st.tabs(["👥 قائمة المستخدمين", "➕ إضافة مستخدم", "📊 الإحصائيات"])

            with tab1:
                st.markdown("### 👥 قائمة المستخدمين")

                # Get all users
                users = user_manager.get_all_users()

                if users:
                    st.markdown(f"**إجمالي المستخدمين: {len(users)}**")

                    # Display users
                    for user in users:
                        with st.container():
                            col1, col2, col3, col4 = st.columns([2, 2, 1, 1])

                            with col1:
                                st.markdown(f"**{user.get('username', 'N/A')}**")
                                st.caption(f"📧 {user.get('email', 'N/A')}")

                            with col2:
                                st.markdown(f"**الاسم:** {user.get('full_name', 'N/A')}")
                                role_colors = {'admin': '🔴', 'manager': '🟠', 'analyst': '🟡', 'user': '🟢'}
                                role_color = role_colors.get(user.get('role', 'user'), '⚪')
                                st.markdown(f"**الدور:** {role_color} {user.get('role', 'user')}")

                            with col3:
                                status = "نشط" if user.get('is_active', True) else "معطل"
                                status_color = "🟢" if user.get('is_active', True) else "🔴"
                                st.markdown(f"**الحالة:** {status_color} {status}")

                            with col4:
                                if st.button("تعديل", key=f"edit_{user.get('id')}", use_container_width=True):
                                    st.session_state.editing_user = user
                                    st.info("💡 انتقل إلى تبويب 'إضافة مستخدم' لتعديل البيانات")

                            st.markdown("---")
                else:
                    st.info("📝 لا يوجد مستخدمون مسجلون")

            with tab2:
                st.markdown("### ➕ إضافة مستخدم جديد")

                # Check if editing existing user
                editing_user = st.session_state.get('editing_user')
                if editing_user:
                    st.info(f"🔧 تعديل بيانات المستخدم: {editing_user.get('username')}")

                # User form
                with st.form("user_form"):
                    col1, col2 = st.columns(2)

                    with col1:
                        username = st.text_input("اسم المستخدم", value=editing_user.get('username', '') if editing_user else '')
                        email = st.text_input("البريد الإلكتروني", value=editing_user.get('email', '') if editing_user else '')
                        full_name = st.text_input("الاسم الكامل", value=editing_user.get('full_name', '') if editing_user else '')

                    with col2:
                        role = st.selectbox("الدور", ["user", "analyst", "manager", "admin"],
                                          index=["user", "analyst", "manager", "admin"].index(editing_user.get('role', 'user')) if editing_user else 0)
                        is_active = st.checkbox("حساب نشط", value=editing_user.get('is_active', True) if editing_user else True)
                        if not editing_user:
                            password = st.text_input("كلمة المرور", type="password")
                            confirm_password = st.text_input("تأكيد كلمة المرور", type="password")

                    # Submit button
                    submit_button = st.form_submit_button("حفظ المستخدم" if editing_user else "إضافة مستخدم", type="primary")

                    if submit_button:
                        if editing_user:
                            # Update existing user
                            success = user_manager.update_user(
                                editing_user.get('id'),
                                username=username,
                                email=email,
                                full_name=full_name,
                                role=role,
                                is_active=is_active
                            )
                            if success:
                                st.success("✅ تم تحديث بيانات المستخدم بنجاح")
                                del st.session_state.editing_user
                                st.rerun()
                            else:
                                st.error("❌ فشل في تحديث بيانات المستخدم")
                        else:
                            # Create new user
                            if password != confirm_password:
                                st.error("❌ كلمات المرور غير متطابقة")
                            elif len(password) < 6:
                                st.error("❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                            else:
                                user_id = user_manager.create_user(
                                    username=username,
                                    password=password,
                                    email=email,
                                    full_name=full_name,
                                    role=role,
                                    is_active=is_active
                                )
                                if user_id:
                                    st.success("✅ تم إنشاء المستخدم بنجاح")
                                    st.rerun()
                                else:
                                    st.error("❌ فشل في إنشاء المستخدم")

                # Clear editing state
                if editing_user and st.button("إلغاء التعديل"):
                    del st.session_state.editing_user
                    st.rerun()

            with tab3:
                st.markdown("### 📊 إحصائيات المستخدمين")

                users = user_manager.get_all_users()
                if users:
                    # User statistics
                    total_users = len(users)
                    active_users = len([u for u in users if u.get('is_active', True)])
                    admin_users = len([u for u in users if u.get('role') == 'admin'])

                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("إجمالي المستخدمين", total_users)
                    with col2:
                        st.metric("المستخدمون النشطون", active_users)
                    with col3:
                        st.metric("المديرون", admin_users)
                    with col4:
                        st.metric("معدل النشاط", f"{(active_users/total_users*100):.1f}%")

                    # Role distribution
                    st.markdown("#### توزيع الأدوار")
                    role_counts = {}
                    for user in users:
                        role = user.get('role', 'user')
                        role_counts[role] = role_counts.get(role, 0) + 1

                    for role, count in role_counts.items():
                        st.markdown(f"**{role}:** {count} مستخدم")
                else:
                    st.info("📝 لا توجد إحصائيات متاحة")

        except ImportError:
            st.error("❌ نظام إدارة المستخدمين غير متوفر")
            st.info("💡 تأكد من وجود ملف user_management.py")
        except Exception as e:
            st.error(f"❌ خطأ في تحميل نظام إدارة المستخدمين: {str(e)}")

    def _generate_advanced_report(self, report_type: str):
        """Generate advanced report based on type"""
        try:
            from advanced_reporting import ReportGenerator
            report_generator = ReportGenerator()

            with st.spinner(f"جاري إنشاء {report_type}..."):
                if "تحليل المخاطر" in report_type:
                    # Risk analysis report
                    analytics = report_generator.generate_contract_analytics_report(30)

                    if analytics:
                        st.success("✅ تم إنشاء تقرير تحليل المخاطر بنجاح!")

                        # Display summary
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("إجمالي العقود", analytics['summary'].get('total_contracts', 0))
                        with col2:
                            st.metric("متوسط المخاطر", f"{analytics['summary'].get('avg_risk_score', 0):.1f}")
                        with col3:
                            st.metric("نسبة المخاطر العالية", f"{analytics['summary'].get('high_risk_percentage', 0):.1f}%")

                        # Risk trends chart
                        if analytics.get('trends'):
                            fig = report_generator.create_risk_analysis_chart(analytics['trends'])
                            st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.error("❌ فشل في إنشاء تقرير تحليل المخاطر")

                elif "الامتثال" in report_type:
                    # Compliance report
                    compliance_data = report_generator.generate_compliance_report(30)

                    if compliance_data:
                        st.success("✅ تم إنشاء تقرير الامتثال بنجاح!")

                        # Display compliance distribution
                        if compliance_data.get('compliance_distribution'):
                            st.markdown("#### توزيع حالة الامتثال")
                            for item in compliance_data['compliance_distribution']:
                                st.markdown(f"- **{item['compliance_status']}**: {item['count']} عقد")
                    else:
                        st.error("❌ فشل في إنشاء تقرير الامتثال")

                elif "النماذج" in report_type:
                    # Template usage report
                    template_data = report_generator.generate_template_usage_report(30)

                    if template_data:
                        st.success("✅ تم إنشاء تقرير استخدام النماذج بنجاح!")

                        # Display top templates
                        if template_data.get('template_usage'):
                            st.markdown("#### النماذج الأكثر استخداماً")
                            for template in template_data['template_usage'][:10]:
                                st.markdown(f"- **{template['name']}**: {template['usage_count']} مرة")
                    else:
                        st.error("❌ فشل في إنشاء تقرير النماذج")

                else:
                    # Executive summary
                    analytics = report_generator.generate_executive_summary()

                    if analytics:
                        st.success("✅ تم إنشاء الملخص التنفيذي بنجاح!")

                        # Display key metrics
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("إجمالي العقود", analytics.get('total_contracts', 0))
                        with col2:
                            st.metric("متوسط المخاطر", f"{analytics.get('avg_risk_score', 0):.1f}")
                        with col3:
                            st.metric("معدل الامتثال", f"{analytics.get('compliance_rate', 0):.1f}%")
                        with col4:
                            st.metric("العقود المعلقة", analytics.get('pending_contracts', 0))
                    else:
                        st.error("❌ فشل في إنشاء الملخص التنفيذي")

        except ImportError:
            st.error("❌ نظام التقارير المتقدمة غير متوفر")
            st.info("💡 تأكد من وجود ملف advanced_reporting.py")
        except Exception as e:
            st.error(f"❌ خطأ في إنشاء التقرير: {str(e)}")

    def _generate_custom_report(self, date_range: int, report_format: str,
                               include_charts: bool, include_recommendations: bool):
        """Generate custom report with specified parameters"""
        try:
            from advanced_reporting import ReportGenerator
            report_generator = ReportGenerator()

            with st.spinner("جاري إنشاء التقرير المخصص..."):
                # Generate comprehensive analytics
                analytics = report_generator.generate_contract_analytics_report(date_range)

                if analytics:
                    st.success("✅ تم إنشاء التقرير المخصص بنجاح!")

                    # Display report content
                    st.markdown("### 📊 التقرير المخصص")

                    # Summary section
                    st.markdown("#### الملخص التنفيذي")
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("إجمالي العقود", analytics['summary'].get('total_contracts', 0))
                    with col2:
                        st.metric("متوسط المخاطر", f"{analytics['summary'].get('avg_risk_score', 0):.1f}")
                    with col3:
                        st.metric("نسبة المخاطر العالية", f"{analytics['summary'].get('high_risk_percentage', 0):.1f}%")

                    # Charts section
                    if include_charts and analytics.get('trends'):
                        st.markdown("#### الرسوم البيانية")

                        # Risk trends chart
                        fig_risk = report_generator.create_risk_analysis_chart(analytics['trends'])
                        st.plotly_chart(fig_risk, use_container_width=True)

                        # Contract types chart
                        if analytics.get('contract_types'):
                            fig_types = report_generator.create_contract_types_chart(analytics['contract_types'])
                            st.plotly_chart(fig_types, use_container_width=True)

                        # User activity chart
                        if analytics.get('user_activity'):
                            fig_users = report_generator.create_user_activity_chart(analytics['user_activity'])
                            st.plotly_chart(fig_users, use_container_width=True)

                    # Recommendations section
                    if include_recommendations:
                        st.markdown("#### التوصيات")
                        st.markdown("- 📈 مراقبة العقود عالية المخاطر بشكل دوري")
                        st.markdown("- 🔍 تحسين عمليات المراجعة للعقود المعلقة")
                        st.markdown("- 📚 تحديث النماذج القانونية بناءً على الاستخدام")
                        st.markdown("- 👥 تدريب الفريق على أدوات التحليل المتقدمة")

                    # Export options
                    st.markdown("#### تصدير التقرير")
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        if st.button("تصدير PDF", use_container_width=True):
                            # Export as PDF
                            st.info("سيتم تصدير التقرير كملف PDF")

                    with col2:
                        if st.button("تصدير Excel", use_container_width=True):
                            # Export as Excel
                            export_data = report_generator.export_report_data(analytics, "excel")
                            if export_data:
                                st.download_button(
                                    label="تحميل ملف Excel",
                                    data=export_data,
                                    file_name=f"legal_report_{datetime.now().strftime('%Y%m%d')}.xlsx",
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )

                    with col3:
                        if st.button("تصدير CSV", use_container_width=True):
                            # Export as CSV
                            export_data = report_generator.export_report_data(analytics, "csv")
                            if export_data:
                                st.download_button(
                                    label="تحميل ملف CSV",
                                    data=export_data,
                                    file_name=f"legal_report_{datetime.now().strftime('%Y%m%d')}.csv",
                                    mime="text/csv"
                                )
                else:
                    st.error("❌ فشل في إنشاء التقرير المخصص")

        except ImportError:
            st.error("❌ نظام التقارير المتقدمة غير متوفر")
        except Exception as e:
            st.error(f"❌ خطأ في إنشاء التقرير المخصص: {str(e)}")

    def _perform_advanced_search(self, search_query: str, search_type: str):
        """Perform advanced search in contracts"""
        if not search_query.strip():
            st.error("❌ يرجى إدخال استعلام البحث")
            return

        try:
            with st.spinner("جاري البحث..."):
                # Simulate advanced search results
                search_results = []

                # Mock search results based on query
                if "عقد" in search_query:
                    search_results = [
                        {
                            'id': 'contract_001',
                            'title': 'عقد توريد معدات',
                            'content_snippet': 'يتضمن هذا العقد توريد معدات تقنية متطورة...',
                            'risk_score': 65,
                            'match_score': 95,
                            'created_at': '2024-01-15'
                        },
                        {
                            'id': 'contract_002',
                            'title': 'عقد خدمات استشارية',
                            'content_snippet': 'عقد تقديم خدمات استشارية قانونية...',
                            'risk_score': 45,
                            'match_score': 87,
                            'created_at': '2024-01-20'
                        }
                    ]
                elif "مخاطر" in search_query:
                    search_results = [
                        {
                            'id': 'contract_003',
                            'title': 'عقد عالي المخاطر',
                            'content_snippet': 'يحتوي على بنود عالية المخاطر تتطلب مراجعة...',
                            'risk_score': 85,
                            'match_score': 92,
                            'created_at': '2024-01-10'
                        }
                    ]

                if search_results:
                    st.success(f"✅ تم العثور على {len(search_results)} نتيجة")

                    # Display search results
                    for result in search_results:
                        with st.container():
                            col1, col2, col3 = st.columns([3, 1, 1])

                            with col1:
                                st.markdown(f"### 📄 {result['title']}")
                                st.markdown(result['content_snippet'])
                                st.caption(f"معرف العقد: {result['id']}")

                            with col2:
                                # Risk score with color coding
                                risk_score = result['risk_score']
                                if risk_score >= 70:
                                    risk_color = "🔴"
                                elif risk_score >= 40:
                                    risk_color = "🟡"
                                else:
                                    risk_color = "🟢"

                                st.metric("درجة المخاطر", f"{risk_color} {risk_score}%")
                                st.metric("دقة المطابقة", f"{result['match_score']}%")

                            with col3:
                                st.markdown(f"**تاريخ الإنشاء:**")
                                st.markdown(result['created_at'])

                                if st.button("عرض التفاصيل", key=f"view_{result['id']}"):
                                    st.info(f"عرض تفاصيل {result['title']}")

                            st.markdown("---")

                    # Search statistics
                    st.markdown("#### إحصائيات البحث")
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("إجمالي النتائج", len(search_results))
                    with col2:
                        avg_risk = sum(r['risk_score'] for r in search_results) / len(search_results)
                        st.metric("متوسط المخاطر", f"{avg_risk:.1f}%")
                    with col3:
                        avg_match = sum(r['match_score'] for r in search_results) / len(search_results)
                        st.metric("متوسط المطابقة", f"{avg_match:.1f}%")
                    with col4:
                        high_risk_count = len([r for r in search_results if r['risk_score'] >= 70])
                        st.metric("عقود عالية المخاطر", high_risk_count)

                else:
                    st.warning("⚠️ لم يتم العثور على نتائج مطابقة")
                    st.info("💡 جرب استخدام كلمات مفتاحية مختلفة أو تغيير نوع البحث")

        except Exception as e:
            st.error(f"❌ خطأ في البحث: {str(e)}")

    def _display_legal_terms_color_coding(self):
        """Display legal terms color coding legend"""
        with st.expander("🎨 دليل ألوان المصطلحات القانونية", expanded=False):
            st.markdown("### ترميز المصطلحات القانونية بالألوان")

            # Create color-coded examples
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("#### الفئات والألوان:")
                st.markdown("""
                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #ffeb3b; padding: 3px 8px; border-radius: 3px; color: #333;">🟡 المصطلحات القانونية</span><br>
                    <small>مثل: عقد، التزام، حق، واجب، مسؤولية</small>
                </div>

                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #4caf50; padding: 3px 8px; border-radius: 3px; color: white;">🟢 الأطراف</span><br>
                    <small>مثل: الطرف الأول، الطرف الثاني، الشركة</small>
                </div>

                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #f44336; padding: 3px 8px; border-radius: 3px; color: white;">🔴 المصطلحات المالية</span><br>
                    <small>مثل: دينار، ريال، راتب، تعويض، غرامة</small>
                </div>
                """, unsafe_allow_html=True)

            with col2:
                st.markdown("#### المزيد من الفئات:")
                st.markdown("""
                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #2196f3; padding: 3px 8px; border-radius: 3px; color: white;">🔵 التواريخ والمواعيد</span><br>
                    <small>مثل: تاريخ البدء، تاريخ الانتهاء، مدة العقد</small>
                </div>

                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #ff9800; padding: 3px 8px; border-radius: 3px; color: white;">🟠 البنود الحرجة</span><br>
                    <small>مثل: فسخ، إنهاء، قوة قاهرة، نزاع</small>
                </div>

                <div style="padding: 10px; border-radius: 5px; margin: 5px 0;">
                    <span style="background-color: #9c27b0; padding: 3px 8px; border-radius: 3px; color: white;">🟣 المراجع القانونية</span><br>
                    <small>مثل: القانون المدني، قانون العمل، المحكمة</small>
                </div>
                """, unsafe_allow_html=True)

            st.info("💡 هذا الترميز يساعد في تحديد أنواع المصطلحات المختلفة بسرعة أثناء مراجعة العقود والترجمات")

    def _highlight_legal_terms_with_colors(self, text: str) -> str:
        """Apply comprehensive color-coded highlighting to legal terms"""
        if not text:
            return text

        # Define comprehensive legal terms with their categories and colors
        legal_terms_categories = {
            # Legal Terms - Yellow
            'legal_terms': {
                'color': '#ffeb3b',
                'text_color': '#333',
                'terms': [
                    'عقد', 'اتفاقية', 'التزام', 'حق', 'واجب', 'مسؤولية', 'ضمان', 'كفالة',
                    'تعويض', 'أضرار', 'إخلال', 'انتهاك', 'مخالفة', 'قانون', 'نظام', 'لائحة',
                    'contract', 'agreement', 'obligation', 'right', 'duty', 'liability', 'warranty',
                    'guarantee', 'compensation', 'damages', 'breach', 'violation', 'law', 'regulation'
                ]
            },
            # Parties - Green
            'parties': {
                'color': '#4caf50',
                'text_color': 'white',
                'terms': [
                    'الطرف الأول', 'الطرف الثاني', 'المتعاقد', 'المتعاقدان', 'الشركة', 'المؤسسة',
                    'الموظف', 'العامل', 'صاحب العمل', 'المقاول', 'المورد', 'العميل', 'الزبون',
                    'first party', 'second party', 'contractor', 'company', 'organization',
                    'employee', 'worker', 'employer', 'supplier', 'client', 'customer'
                ]
            },
            # Financial Terms - Red
            'financial': {
                'color': '#f44336',
                'text_color': 'white',
                'terms': [
                    'دينار', 'ريال', 'دولار', 'راتب', 'أجر', 'مكافأة', 'علاوة', 'تعويض',
                    'غرامة', 'خصم', 'مبلغ', 'قيمة', 'ثمن', 'سعر', 'تكلفة', 'رسوم',
                    'dinar', 'riyal', 'dollar', 'salary', 'wage', 'bonus', 'allowance',
                    'compensation', 'fine', 'penalty', 'amount', 'value', 'price', 'cost', 'fees'
                ]
            },
            # Dates & Deadlines - Blue
            'dates': {
                'color': '#2196f3',
                'text_color': 'white',
                'terms': [
                    'تاريخ', 'موعد', 'مدة', 'فترة', 'بداية', 'نهاية', 'انتهاء', 'تجديد',
                    'يوم', 'شهر', 'سنة', 'أسبوع', 'ساعة', 'دقيقة', 'مهلة', 'أجل',
                    'date', 'deadline', 'duration', 'period', 'start', 'end', 'expiry',
                    'renewal', 'day', 'month', 'year', 'week', 'hour', 'minute', 'term'
                ]
            },
            # Critical Clauses - Orange
            'critical': {
                'color': '#ff9800',
                'text_color': 'white',
                'terms': [
                    'فسخ', 'إنهاء', 'إلغاء', 'إبطال', 'قوة قاهرة', 'ظروف استثنائية', 'نزاع',
                    'خلاف', 'تحكيم', 'محكمة', 'قضاء', 'دعوى', 'طعن', 'استئناف',
                    'termination', 'cancellation', 'void', 'force majeure', 'dispute',
                    'conflict', 'arbitration', 'court', 'litigation', 'appeal'
                ]
            },
            # Legal References - Purple
            'references': {
                'color': '#9c27b0',
                'text_color': 'white',
                'terms': [
                    'القانون المدني', 'قانون العمل', 'القانون التجاري', 'قانون الشركات',
                    'المحكمة العليا', 'محكمة الاستئناف', 'المحكمة الابتدائية', 'النيابة العامة',
                    'civil law', 'labor law', 'commercial law', 'company law',
                    'supreme court', 'court of appeal', 'primary court', 'public prosecution'
                ]
            }
        }

        highlighted_text = text

        # Apply highlighting for each category
        for category, config in legal_terms_categories.items():
            color = config['color']
            text_color = config['text_color']

            for term in config['terms']:
                # Create highlighted span with category-specific color
                highlighted_span = f'<span style="background-color: {color}; color: {text_color}; padding: 2px 6px; border-radius: 3px; margin: 0 1px; font-weight: 500;">{term}</span>'

                # Replace term (case-insensitive for English, exact match for Arabic)
                if any(char in term for char in 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'):
                    # English term - case insensitive
                    import re
                    pattern = re.compile(re.escape(term), re.IGNORECASE)
                    highlighted_text = pattern.sub(highlighted_span, highlighted_text)
                else:
                    # Arabic term - exact match
                    highlighted_text = highlighted_text.replace(term, highlighted_span)

        return highlighted_text

    def _render_menu_management_settings(self):
        """Render menu management settings for administrators"""
        st.markdown("### 📋 إدارة عناصر القائمة الجانبية")

        # Check if user has admin permissions
        user = self.auth_manager.get_current_user()
        if not user or user.get('role') != 'admin':
            st.warning("⚠️ هذه الإعدادات متاحة للمديرين فقط")
            return

        # Get current menu preferences
        menu_preferences = self.db_manager.get_menu_preferences()

        st.info("💡 يمكنك إظهار أو إخفاء عناصر القائمة الجانبية للمستخدمين")

        # Menu items with their Arabic labels
        menu_items = {
            "home": "🏠 الرئيسية",
            "analysis": "📊 تحليل العقود",
            "database": "🗄️ قاعدة البيانات",
            "statistics": "📈 الإحصائيات",
            "templates": "📄 القوالب",
            "history": "📚 السجل",
            "risk": "⚠️ تقييم المخاطر",
            "collaboration": "👥 التعاون",
            "reports": "📋 التقارير",
            "advanced": "🔧 متقدم",
            "insights": "💡 الرؤى",
            "monitoring": "📊 المراقبة",
            "guidelines": "📚 المبادئ التوجيهية",
            "users": "👥 إدارة المستخدمين",
            "settings": "⚙️ الإعدادات"
        }

        # Create two columns for better layout
        col1, col2 = st.columns(2)

        updated_preferences = {}

        # Split menu items into two columns
        items_list = list(menu_items.items())
        mid_point = len(items_list) // 2

        with col1:
            st.markdown("#### العمود الأول")
            for key, label in items_list[:mid_point]:
                current_value = menu_preferences.get(key, True)
                updated_preferences[key] = st.checkbox(
                    label,
                    value=current_value,
                    key=f"menu_{key}"
                )

        with col2:
            st.markdown("#### العمود الثاني")
            for key, label in items_list[mid_point:]:
                current_value = menu_preferences.get(key, True)
                updated_preferences[key] = st.checkbox(
                    label,
                    value=current_value,
                    key=f"menu_{key}"
                )

        st.markdown("---")

        # Action buttons
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("💾 حفظ الإعدادات", type="primary"):
                if self.db_manager.update_menu_preferences(updated_preferences):
                    st.success("✅ تم حفظ إعدادات القائمة بنجاح!")
                    st.rerun()
                else:
                    st.error("❌ فشل في حفظ الإعدادات")

        with col2:
            if st.button("🔄 إعادة تعيين"):
                if self.db_manager.reset_menu_preferences():
                    st.success("✅ تم إعادة تعيين القائمة إلى الإعدادات الافتراضية!")
                    st.rerun()
                else:
                    st.error("❌ فشل في إعادة التعيين")

        with col3:
            if st.button("👁️ معاينة التغييرات"):
                st.info("💡 احفظ الإعدادات أولاً لرؤية التغييرات في القائمة الجانبية")

        # Show current status
        st.markdown("---")
        st.markdown("#### 📊 حالة عناصر القائمة الحالية")

        visible_count = sum(1 for v in menu_preferences.values() if v)
        total_count = len(menu_preferences)

        st.metric(
            "عناصر القائمة المرئية",
            f"{visible_count} من {total_count}",
            f"{(visible_count/total_count)*100:.1f}%"
        )

        # Show hidden items
        hidden_items = [menu_items[k] for k, v in menu_preferences.items() if not v and k in menu_items]
        if hidden_items:
            st.warning(f"⚠️ العناصر المخفية: {', '.join(hidden_items)}")
        else:
            st.success("✅ جميع عناصر القائمة مرئية")

    def _render_sharing_page(self):
        """Render analysis sharing and export/import page"""
        st.markdown("##### 📤 المشاركة والتصدير")
        st.markdown("---")

        # Get current user
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return

        user_id = current_user.get('id')

        # Create tabs for different functionalities
        tab1, tab2, tab3, tab4 = st.tabs(["🔗 إنشاء رابط مشاركة", "📋 الروابط المشتركة", "📤 تصدير التحليلات", "📥 استيراد التحليلات"])

        with tab1:
            self._render_create_share_link_tab(user_id)

        with tab2:
            self._render_shared_links_tab(user_id)

        with tab3:
            self._render_export_tab(user_id)

        with tab4:
            self._render_import_tab(user_id)

    def _render_create_share_link_tab(self, user_id: str):
        """Render create share link tab"""
        st.markdown("### 🔗 إنشاء رابط مشاركة جديد")

        # Get user's analyses
        history = self.db_manager.get_user_analysis_history(user_id, limit=100)

        if not history:
            st.info("📝 لا توجد تحليلات متاحة للمشاركة")
            return

        # Analysis selection
        analysis_options = {f"{h['filename']} - {h['created_at'][:10]}": h['analysis_id'] for h in history}
        selected_analysis_name = st.selectbox(
            "اختر التحليل للمشاركة",
            options=list(analysis_options.keys())
        )

        if selected_analysis_name:
            selected_analysis_id = analysis_options[selected_analysis_name]

            # Share settings
            col1, col2 = st.columns(2)

            with col1:
                access_type = st.selectbox(
                    "نوع الوصول",
                    options=["read_only", "download"],
                    format_func=lambda x: "قراءة فقط" if x == "read_only" else "قراءة وتحميل"
                )

            with col2:
                expires_hours = st.selectbox(
                    "مدة انتهاء الصلاحية",
                    options=[None, 1, 24, 168, 720],
                    format_func=lambda x: "بدون انتهاء" if x is None else f"{x} ساعة"
                )

            max_access = st.number_input(
                "الحد الأقصى لعدد الوصولات (-1 = بلا حدود)",
                min_value=-1,
                value=-1
            )

            if st.button("🔗 إنشاء رابط المشاركة", type="primary"):
                share_code = self.db_manager.create_share_link(
                    selected_analysis_id,
                    user_id,
                    access_type,
                    expires_hours,
                    max_access
                )

                if share_code:
                    # Generate full URL
                    base_url = "http://localhost:8575"  # Default app URL
                    share_url = f"{base_url}?share={share_code}"

                    st.success("✅ تم إنشاء رابط المشاركة بنجاح!")

                    # Display share information
                    st.markdown("### 📋 معلومات المشاركة")
                    st.code(share_url, language="text")

                    col1, col2 = st.columns(2)
                    with col1:
                        st.info(f"🔑 كود المشاركة: `{share_code}`")
                    with col2:
                        st.info(f"🔒 نوع الوصول: {access_type}")
                else:
                    st.error("❌ فشل في إنشاء رابط المشاركة")

    def _render_shared_links_tab(self, user_id: str):
        """Render shared links management tab"""
        st.markdown("### 📋 إدارة الروابط المشتركة")

        # Get user's shared links
        shared_links = self.db_manager.get_user_shared_links(user_id)

        if not shared_links:
            st.info("🔗 لا توجد روابط مشتركة حالياً")
            return

        st.markdown(f"**إجمالي الروابط: {len(shared_links)}**")

        # Display shared links
        for link in shared_links:
            # Use the correct field names from the database
            filename = link.get('filename', 'تحليل')
            created_date = link.get('created_at', '')[:10] if link.get('created_at') else ''

            with st.expander(f"🔗 {filename} - {created_date}", expanded=False):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.markdown(f"**كود المشاركة:** `{link.get('share_code', '')}`")
                    st.markdown(f"**نوع الوصول:** {link.get('access_type', 'قراءة فقط')}")

                    if link.get('expires_at'):
                        st.markdown(f"**تنتهي في:** {link['expires_at']}")
                    else:
                        st.markdown("**الصلاحية:** بدون انتهاء")

                    st.markdown(f"**عدد الوصولات:** {link.get('access_count', 0)}")

                    if link.get('max_access_count') and link.get('max_access_count') > 0:
                        st.markdown(f"**الحد الأقصى:** {link['max_access_count']}")

                    if link.get('risk_score'):
                        st.markdown(f"**نقاط المخاطر:** {link['risk_score']}")

                with col2:
                    # Use share_id instead of id
                    share_id = link.get('share_id', link.get('id', ''))

                    if st.button(f"🗑️ حذف", key=f"delete_link_{share_id}"):
                        if self.db_manager.delete_shared_link(share_id, user_id):
                            st.success("✅ تم حذف الرابط!")
                            st.rerun()
                        else:
                            st.error("❌ فشل في حذف الرابط")

                    if st.button(f"📋 نسخ الرابط", key=f"copy_link_{share_id}"):
                        base_url = "http://localhost:8575"
                        share_url = f"{base_url}?share={link.get('share_code', '')}"
                        st.code(share_url)
                        st.info("💡 انسخ الرابط أعلاه")

    def _render_export_tab(self, user_id: str):
        """Render export tab"""
        try:
            from analytics_export_manager import AnalyticsExportManager

            # Initialize analytics export manager
            if 'analytics_export_manager' not in st.session_state:
                st.session_state.analytics_export_manager = AnalyticsExportManager(self.db_manager)

            # Render the analytics export interface
            st.session_state.analytics_export_manager.render_analytics_export_interface(user_id)

        except ImportError as e:
            st.error(f"❌ خطأ في تحميل نظام تصدير التحليلات: {str(e)}")
            st.info("🚧 تأكد من وجود ملف analytics_export_manager.py")
        except Exception as e:
            st.error(f"❌ خطأ في عرض واجهة تصدير التحليلات: {str(e)}")
            logger.error(f"Analytics export interface error: {e}")

    def _render_import_tab(self, user_id: str):
        """Render import tab"""
        st.markdown("### 📥 استيراد التحليلات")
        st.markdown("استيراد البيانات والتحليلات من ملفات خارجية")

        # File upload section
        st.markdown("#### 📁 رفع الملفات")
        uploaded_file = st.file_uploader(
            "اختر ملف للاستيراد",
            type=['json', 'csv', 'xlsx'],
            help="الصيغ المدعومة: JSON, CSV, Excel"
        )

        if uploaded_file is not None:
            try:
                # Display file info
                st.info(f"📄 الملف: {uploaded_file.name} ({uploaded_file.size} بايت)")

                # Import options
                st.markdown("#### ⚙️ خيارات الاستيراد")

                col1, col2 = st.columns(2)
                with col1:
                    merge_data = st.checkbox("دمج مع البيانات الموجودة", value=True)
                with col2:
                    validate_data = st.checkbox("التحقق من صحة البيانات", value=True)

                # Preview data
                if st.button("👁️ معاينة البيانات"):
                    self._preview_import_data(uploaded_file)

                # Import button
                if st.button("🚀 بدء الاستيراد", type="primary"):
                    self._perform_data_import(uploaded_file, user_id, merge_data, validate_data)

            except Exception as e:
                st.error(f"❌ خطأ في معالجة الملف: {str(e)}")

        # Import history
        st.markdown("---")
        st.markdown("#### 📋 سجل الاستيراد")

        # Sample import history (this would come from database in real implementation)
        import_history = [
            {"date": "2024-01-15", "file": "analytics_data.json", "status": "نجح", "records": 150},
            {"date": "2024-01-10", "file": "contracts_export.csv", "status": "نجح", "records": 75},
            {"date": "2024-01-05", "file": "system_data.xlsx", "status": "فشل", "records": 0}
        ]

        if import_history:
            import pandas as pd
            df = pd.DataFrame(import_history)
            df.columns = ["التاريخ", "الملف", "الحالة", "عدد السجلات"]
            st.dataframe(df, use_container_width=True)
        else:
            st.info("📝 لا يوجد سجل استيراد متاح")

    def _preview_import_data(self, uploaded_file):
        """Preview imported data"""
        try:
            if uploaded_file.name.endswith('.json'):
                import json
                data = json.load(uploaded_file)
                st.json(data)
            elif uploaded_file.name.endswith('.csv'):
                import pandas as pd
                df = pd.read_csv(uploaded_file)
                st.dataframe(df.head(10))
            elif uploaded_file.name.endswith('.xlsx'):
                import pandas as pd
                df = pd.read_excel(uploaded_file)
                st.dataframe(df.head(10))

            # Reset file pointer
            uploaded_file.seek(0)

        except Exception as e:
            st.error(f"❌ خطأ في معاينة البيانات: {str(e)}")

    def _perform_data_import(self, uploaded_file, user_id: str, merge_data: bool, validate_data: bool):
        """Perform data import operation"""
        try:
            with st.spinner("🔄 جاري استيراد البيانات..."):
                # Simulate import process
                import time
                time.sleep(2)

                # In a real implementation, this would:
                # 1. Parse the uploaded file
                # 2. Validate the data structure
                # 3. Check for duplicates if merging
                # 4. Insert data into database
                # 5. Log the import operation

                st.success("✅ تم استيراد البيانات بنجاح!")
                st.info("📊 تم استيراد 150 سجل جديد")

                # Show import summary
                with st.expander("📋 ملخص الاستيراد"):
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("السجلات المستوردة", "150")
                    with col2:
                        st.metric("السجلات المرفوضة", "5")
                    with col3:
                        st.metric("معدل النجاح", "96.8%")

        except Exception as e:
            st.error(f"❌ خطأ في استيراد البيانات: {str(e)}")
            logger.error(f"Data import error: {e}")

    def _render_clause_library_page(self):
        """Render legal clauses library page"""
        st.markdown("##### 📖 مكتبة البنود القانونية")
        st.markdown("---")

        # Get current user
        current_user = self.auth_manager.get_current_user()
        if not current_user:
            st.error("❌ يجب تسجيل الدخول أولاً")
            return

        user_id = current_user.get('id')

        # Create tabs
        tab1, tab2, tab3 = st.tabs(["🔍 البحث والاستعراض", "➕ إضافة بند جديد", "📊 الإحصائيات"])

        with tab1:
            self._render_clause_search_tab()

        with tab2:
            self._render_add_clause_tab(user_id)

        with tab3:
            self._render_clause_statistics_tab()

    def _render_clause_search_tab(self):
        """Render clause search and browse tab"""
        st.markdown("### 🔍 البحث في مكتبة البنود")

        # Search filters
        col1, col2, col3 = st.columns(3)

        with col1:
            legal_system = st.selectbox(
                "النظام القانوني",
                options=["", "kuwait", "saudi"],
                format_func=lambda x: {"": "جميع الأنظمة", "kuwait": "الكويت", "saudi": "السعودية"}[x]
            )

        with col2:
            categories = self.clause_manager.db_manager.get_clause_categories(legal_system if legal_system else None)
            category_options = [""] + [cat['category'] for cat in categories]
            selected_category = st.selectbox("الفئة", options=category_options)

        with col3:
            search_term = st.text_input("البحث في النص", placeholder="ادخل كلمات البحث...")

        # Get clauses
        clauses = self.clause_manager.search_clauses(
            search_term=search_term if search_term else None,
            legal_system=legal_system if legal_system else None,
            category=selected_category if selected_category else None
        )

        st.markdown(f"**النتائج: {len(clauses)} بند**")

        # Display clauses
        for clause in clauses:
            with st.expander(f"📋 {clause['title']} - {clause['category']}", expanded=False):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.markdown(f"**المحتوى:**")
                    st.write(clause['content'])

                    if clause['description']:
                        st.markdown(f"**الوصف:** {clause['description']}")

                    if clause['usage_notes']:
                        st.markdown(f"**ملاحظات الاستخدام:** {clause['usage_notes']}")

                with col2:
                    # Risk indicator
                    risk_color = {"low": "🟢", "medium": "🟡", "high": "🔴"}[clause['risk_level']]
                    st.metric("مستوى المخاطر", f"{risk_color} {clause['risk_level']}")
                    st.metric("نقاط المخاطر", f"{clause['risk_score']}%")

                    # System and category
                    st.info(f"**النظام:** {clause['legal_system']}")
                    st.info(f"**الفئة:** {clause['category']}")

                    # Action buttons
                    if st.button(f"📋 نسخ البند", key=f"copy_clause_{clause['id']}"):
                        st.code(clause['content'])
                        st.success("تم نسخ البند!")

    def _render_add_clause_tab(self, user_id: str):
        """Render add new clause tab"""
        st.markdown("### ➕ إضافة بند قانوني جديد")

        with st.form("add_clause_form"):
            col1, col2 = st.columns(2)

            with col1:
                title = st.text_input("عنوان البند *", placeholder="مثال: بند المسؤولية المحدودة")
                category = st.text_input("الفئة *", placeholder="مثال: المسؤولية")
                subcategory = st.text_input("الفئة الفرعية", placeholder="مثال: تحديد المسؤولية")

            with col2:
                legal_system = st.selectbox("النظام القانوني *", options=["kuwait", "saudi"],
                                          format_func=lambda x: {"kuwait": "الكويت", "saudi": "السعودية"}[x])
                risk_level = st.selectbox("مستوى المخاطر", options=["low", "medium", "high"],
                                        format_func=lambda x: {"low": "منخفض", "medium": "متوسط", "high": "عالي"}[x])
                risk_score = st.slider("نقاط المخاطر", 0, 100, 50)

            content = st.text_area("محتوى البند *", height=150,
                                 placeholder="اكتب النص الكامل للبند القانوني...")

            description = st.text_area("الوصف", height=100,
                                     placeholder="وصف مختصر لاستخدام البند...")

            usage_notes = st.text_area("ملاحظات الاستخدام", height=100,
                                     placeholder="ملاحظات حول متى وكيف يُستخدم هذا البند...")

            tags = st.text_input("الكلمات المفتاحية", placeholder="مثال: مسؤولية,تحديد,أضرار")

            submitted = st.form_submit_button("➕ إضافة البند", type="primary")

            if submitted:
                if title and category and content and legal_system:
                    clause_id = self.clause_manager.add_custom_clause(
                        title=title,
                        content=content,
                        category=category,
                        legal_system=legal_system,
                        subcategory=subcategory,
                        risk_level=risk_level,
                        risk_score=risk_score,
                        description=description,
                        usage_notes=usage_notes,
                        tags=tags,
                        created_by=user_id
                    )

                    if clause_id:
                        st.success("✅ تم إضافة البند بنجاح!")
                        st.rerun()
                    else:
                        st.error("❌ فشل في إضافة البند")
                else:
                    st.error("❌ يرجى ملء جميع الحقول المطلوبة (*)")

    def _render_clause_statistics_tab(self):
        """Render clause statistics tab"""
        st.markdown("### 📊 إحصائيات مكتبة البنود")

        # Get statistics for both systems
        kuwait_stats = self.clause_manager.get_clause_statistics('kuwait')
        saudi_stats = self.clause_manager.get_clause_statistics('saudi')
        overall_stats = self.clause_manager.get_clause_statistics()

        # Overall metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("إجمالي البنود", overall_stats['total_clauses'])

        with col2:
            st.metric("بنود كويتية", kuwait_stats['total_clauses'])

        with col3:
            st.metric("بنود سعودية", saudi_stats['total_clauses'])

        with col4:
            st.metric("متوسط المخاطر", f"{overall_stats['average_risk_score']}%")

def main():
    """Main application entry point"""
    app = EnhancedLegalApp()
    app.run()

if __name__ == "__main__":
    main()
