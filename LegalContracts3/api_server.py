"""
RESTful API Server for Kuwaiti Legal Contract Analysis
Developed by MAXBIT LLC © 2025
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, UploadFile, File, Form, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, FileResponse
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import uvicorn
import jwt
import hashlib
from datetime import datetime, timedelta
import tempfile
import os
import json

# Import our existing modules
from auth import UserManager
from database import ContractDatabase, ContractType, ContractStatus
from ai_backend import ContractAnalyzer
from risk_assessment import RiskAssessment
from collaboration import CollaborationManager
from template_library import TemplateLibrary
from advanced_reporting import ReportGenerator
from utils import extract_text_from_file, validate_file

# API Configuration
API_VERSION = "v1"
SECRET_KEY = "maxbit_legal_contracts_api_2025_secret_key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

app = FastAPI(
    title="Kuwaiti Legal Contract Analysis API",
    description="Professional API for analyzing English contracts according to Kuwaiti law",
    version="1.0.0",
    docs_url=f"/api/{API_VERSION}/docs",
    redoc_url=f"/api/{API_VERSION}/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Pydantic Models
class UserLogin(BaseModel):
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")

class UserCreate(BaseModel):
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    email: str = Field(..., description="Email address")
    full_name: str = Field(..., description="Full name")
    role: str = Field(default="client", description="User role")

class ContractAnalysisRequest(BaseModel):
    contract_text: str = Field(..., description="Contract text to analyze")
    contract_type: str = Field(default="other", description="Type of contract")
    title: str = Field(..., description="Contract title")

class ContractResponse(BaseModel):
    id: str
    title: str
    contract_type: str
    status: str
    created_at: str
    risk_score: Optional[float]
    analysis: Optional[Dict[str, Any]]

class CommentCreate(BaseModel):
    contract_id: str = Field(..., description="Contract ID")
    content: str = Field(..., description="Comment content")
    comment_type: str = Field(default="general", description="Comment type")
    section: Optional[str] = Field(None, description="Contract section")

class TemplateUse(BaseModel):
    template_id: str = Field(..., description="Template ID")
    variables: Dict[str, str] = Field(..., description="Template variables")

class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None

# Initialize services
user_manager = UserManager()
contract_db = ContractDatabase()
contract_analyzer = ContractAnalyzer()
risk_assessment = RiskAssessment()
collaboration_manager = CollaborationManager()
template_library = TemplateLibrary()
report_generator = ReportGenerator()

# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify JWT token"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return username
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_user(username: str = Depends(verify_token)):
    """Get current authenticated user"""
    user = user_manager.get_user(username)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    return user

# API Endpoints

@app.get(f"/api/{API_VERSION}/")
async def root():
    """API root endpoint"""
    return {
        "message": "Kuwaiti Legal Contract Analysis API",
        "version": "1.0.0",
        "developer": "MAXBIT LLC",
        "year": "2025",
        "docs": f"/api/{API_VERSION}/docs"
    }

@app.post(f"/api/{API_VERSION}/auth/login")
async def login(user_login: UserLogin):
    """Authenticate user and return access token"""
    user = user_manager.authenticate_user(user_login.username, user_login.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "user": {
            "username": user["username"],
            "email": user["email"],
            "full_name": user["full_name"],
            "role": user["role"]
        }
    }

@app.post(f"/api/{API_VERSION}/auth/register")
async def register(user_create: UserCreate, current_user: dict = Depends(get_current_user)):
    """Register new user (admin only)"""
    if current_user["role"] != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create new users"
        )
    
    success = user_manager.create_user(
        user_create.username,
        user_create.password,
        user_create.email,
        user_create.full_name,
        user_create.role,
        current_user["username"]
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already exists"
        )
    
    return APIResponse(
        success=True,
        message="User created successfully",
        data={"username": user_create.username}
    )

@app.post(f"/api/{API_VERSION}/contracts/analyze")
async def analyze_contract(
    request: ContractAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyze contract text"""
    try:
        # Perform analysis
        analysis = contract_analyzer.analyze_contract(request.contract_text)
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to analyze contract"
            )
        
        # Calculate risk score
        risk_score = risk_assessment.calculate_risk_score(analysis)
        analysis["risk_assessment"] = {
            "risk_score": risk_score,
            "risk_level": risk_assessment.get_risk_level(risk_score),
            "risk_factors": risk_assessment.identify_risk_factors(analysis)
        }
        
        # Save to database
        contract_id = contract_db.save_contract(
            request.title,
            request.contract_text,
            analysis,
            current_user["id"],
            request.contract_type
        )
        
        return APIResponse(
            success=True,
            message="Contract analyzed successfully",
            data={
                "contract_id": contract_id,
                "analysis": analysis,
                "risk_score": risk_score
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analysis failed: {str(e)}"
        )

@app.post(f"/api/{API_VERSION}/contracts/upload")
async def upload_contract(
    file: UploadFile = File(...),
    title: str = Form(...),
    contract_type: str = Form(default="other"),
    current_user: dict = Depends(get_current_user)
):
    """Upload and analyze contract file"""
    try:
        # Validate file
        if not validate_file(file):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file type"
            )
        
        # Extract text
        contract_text = extract_text_from_file(file)
        if not contract_text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not extract text from file"
            )
        
        # Analyze contract
        analysis_request = ContractAnalysisRequest(
            contract_text=contract_text,
            contract_type=contract_type,
            title=title
        )
        
        return await analyze_contract(analysis_request, current_user)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )

@app.get(f"/api/{API_VERSION}/contracts", response_model=List[ContractResponse])
async def list_contracts(
    limit: int = 50,
    offset: int = 0,
    contract_type: Optional[str] = None,
    status: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """List user's contracts"""
    user_id = None if current_user["role"] == "admin" else current_user["id"]
    contracts = contract_db.list_contracts(
        user_id=user_id,
        limit=limit,
        offset=offset,
        contract_type=contract_type,
        status=status
    )
    
    return [ContractResponse(**contract) for contract in contracts]

@app.get(f"/api/{API_VERSION}/contracts/{{contract_id}}")
async def get_contract(
    contract_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get specific contract"""
    contract = contract_db.get_contract(contract_id)
    if not contract:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contract not found"
        )
    
    # Check permissions
    if current_user["role"] != "admin" and contract["created_by"] != current_user["id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return APIResponse(
        success=True,
        message="Contract retrieved successfully",
        data=contract
    )

@app.post(f"/api/{API_VERSION}/contracts/{{contract_id}}/comments")
async def add_comment(
    contract_id: str,
    comment: CommentCreate,
    current_user: dict = Depends(get_current_user)
):
    """Add comment to contract"""
    # Verify contract exists and user has access
    contract = contract_db.get_contract(contract_id)
    if not contract:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contract not found"
        )
    
    comment_id = collaboration_manager.add_comment(
        contract_id,
        current_user["id"],
        current_user["full_name"],
        comment.content,
        comment.comment_type,
        comment.section
    )
    
    return APIResponse(
        success=True,
        message="Comment added successfully",
        data={"comment_id": comment_id}
    )

@app.get(f"/api/{API_VERSION}/contracts/{{contract_id}}/comments")
async def get_comments(
    contract_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get contract comments"""
    comments = collaboration_manager.get_comments(contract_id)
    
    return APIResponse(
        success=True,
        message="Comments retrieved successfully",
        data=comments
    )

@app.get(f"/api/{API_VERSION}/templates")
async def list_templates(
    category: Optional[str] = None,
    complexity: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """List available templates"""
    templates = template_library.search_templates(
        category=category,
        complexity=complexity
    )
    
    return APIResponse(
        success=True,
        message="Templates retrieved successfully",
        data=templates
    )

@app.post(f"/api/{API_VERSION}/templates/{{template_id}}/generate")
async def generate_from_template(
    template_id: str,
    template_use: TemplateUse,
    current_user: dict = Depends(get_current_user)
):
    """Generate contract from template"""
    contract_content = template_library.generate_contract_from_template(
        template_id,
        template_use.variables
    )
    
    if not contract_content:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )
    
    return APIResponse(
        success=True,
        message="Contract generated successfully",
        data={"content": contract_content}
    )

@app.get(f"/api/{API_VERSION}/reports/executive")
async def get_executive_report(
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get executive summary report"""
    user_id = None if current_user["role"] == "admin" else current_user["id"]
    report = report_generator.generate_executive_summary(user_id, days)
    
    return APIResponse(
        success=True,
        message="Executive report generated successfully",
        data=report
    )

@app.get(f"/api/{API_VERSION}/reports/risk")
async def get_risk_report(current_user: dict = Depends(get_current_user)):
    """Get risk analysis report"""
    user_id = None if current_user["role"] == "admin" else current_user["id"]
    report = report_generator.generate_risk_analysis_report(user_id)
    
    return APIResponse(
        success=True,
        message="Risk report generated successfully",
        data=report
    )

@app.get(f"/api/{API_VERSION}/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": "operational",
            "ai_backend": "operational",
            "authentication": "operational"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
