"""
Configuration Management for Production Deployment
Developed by MAXBIT LLC © 2025
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "legal_contracts"
    username: str = "postgres"
    password: str = "password"
    
    @property
    def url(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

@dataclass
class RedisConfig:
    """Redis configuration"""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    
    @property
    def url(self) -> str:
        auth = f":{self.password}@" if self.password else ""
        return f"redis://{auth}{self.host}:{self.port}/{self.db}"

@dataclass
class AIConfig:
    """AI backend configuration"""
    backend: str = "lmstudio"  # lmstudio, ollama, openai
    base_url: str = "http://localhost:1234"
    model_name: str = "law"
    api_key: Optional[str] = None
    timeout: int = 300
    max_tokens: int = 2000
    temperature: float = 0.1

@dataclass
class SecurityConfig:
    """Security configuration"""
    secret_key: str = "maxbit_legal_contracts_secret_2025"
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    password_min_length: int = 8
    max_login_attempts: int = 5
    session_timeout_minutes: int = 60

@dataclass
class AppConfig:
    """Application configuration"""
    environment: str = "development"  # development, staging, production
    debug: bool = True
    host: str = "localhost"
    port: int = 8501
    api_port: int = 8000
    max_file_size_mb: int = 50
    allowed_file_types: list = None
    
    def __post_init__(self):
        if self.allowed_file_types is None:
            self.allowed_file_types = ['txt', 'doc', 'docx', 'pdf']

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/app.log"
    max_file_size_mb: int = 10
    backup_count: int = 5

class ConfigManager:
    """Centralized configuration management"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.json"
        self._load_config()
    
    def _load_config(self):
        """Load configuration from environment variables and config file"""
        # Load from config file if exists
        config_data = {}
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
        
        # Database configuration
        self.database = DatabaseConfig(
            host=os.getenv("DB_HOST", config_data.get("database", {}).get("host", "localhost")),
            port=int(os.getenv("DB_PORT", config_data.get("database", {}).get("port", 5432))),
            database=os.getenv("DB_NAME", config_data.get("database", {}).get("database", "legal_contracts")),
            username=os.getenv("DB_USER", config_data.get("database", {}).get("username", "postgres")),
            password=os.getenv("DB_PASSWORD", config_data.get("database", {}).get("password", "password"))
        )
        
        # Redis configuration
        self.redis = RedisConfig(
            host=os.getenv("REDIS_HOST", config_data.get("redis", {}).get("host", "localhost")),
            port=int(os.getenv("REDIS_PORT", config_data.get("redis", {}).get("port", 6379))),
            password=os.getenv("REDIS_PASSWORD", config_data.get("redis", {}).get("password")),
            db=int(os.getenv("REDIS_DB", config_data.get("redis", {}).get("db", 0)))
        )
        
        # AI configuration
        self.ai = AIConfig(
            backend=os.getenv("AI_BACKEND", config_data.get("ai", {}).get("backend", "lmstudio")),
            base_url=os.getenv("AI_BASE_URL", config_data.get("ai", {}).get("base_url", "http://localhost:1234")),
            model_name=os.getenv("AI_MODEL_NAME", config_data.get("ai", {}).get("model_name", "law")),
            api_key=os.getenv("AI_API_KEY", config_data.get("ai", {}).get("api_key")),
            timeout=int(os.getenv("AI_TIMEOUT", config_data.get("ai", {}).get("timeout", 300))),
            max_tokens=int(os.getenv("AI_MAX_TOKENS", config_data.get("ai", {}).get("max_tokens", 2000))),
            temperature=float(os.getenv("AI_TEMPERATURE", config_data.get("ai", {}).get("temperature", 0.1)))
        )
        
        # Security configuration
        self.security = SecurityConfig(
            secret_key=os.getenv("SECRET_KEY", config_data.get("security", {}).get("secret_key", "maxbit_legal_contracts_secret_2025")),
            jwt_algorithm=os.getenv("JWT_ALGORITHM", config_data.get("security", {}).get("jwt_algorithm", "HS256")),
            access_token_expire_minutes=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", config_data.get("security", {}).get("access_token_expire_minutes", 30))),
            password_min_length=int(os.getenv("PASSWORD_MIN_LENGTH", config_data.get("security", {}).get("password_min_length", 8))),
            max_login_attempts=int(os.getenv("MAX_LOGIN_ATTEMPTS", config_data.get("security", {}).get("max_login_attempts", 5))),
            session_timeout_minutes=int(os.getenv("SESSION_TIMEOUT_MINUTES", config_data.get("security", {}).get("session_timeout_minutes", 60)))
        )
        
        # Application configuration
        self.app = AppConfig(
            environment=os.getenv("ENVIRONMENT", config_data.get("app", {}).get("environment", "development")),
            debug=os.getenv("DEBUG", "true").lower() == "true",
            host=os.getenv("APP_HOST", config_data.get("app", {}).get("host", "localhost")),
            port=int(os.getenv("APP_PORT", config_data.get("app", {}).get("port", 8501))),
            api_port=int(os.getenv("API_PORT", config_data.get("app", {}).get("api_port", 8000))),
            max_file_size_mb=int(os.getenv("MAX_FILE_SIZE_MB", config_data.get("app", {}).get("max_file_size_mb", 50))),
            allowed_file_types=config_data.get("app", {}).get("allowed_file_types", ['txt', 'doc', 'docx', 'pdf'])
        )
        
        # Logging configuration
        self.logging = LoggingConfig(
            level=os.getenv("LOG_LEVEL", config_data.get("logging", {}).get("level", "INFO")),
            format=os.getenv("LOG_FORMAT", config_data.get("logging", {}).get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")),
            file_path=os.getenv("LOG_FILE_PATH", config_data.get("logging", {}).get("file_path", "logs/app.log")),
            max_file_size_mb=int(os.getenv("LOG_MAX_FILE_SIZE_MB", config_data.get("logging", {}).get("max_file_size_mb", 10))),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", config_data.get("logging", {}).get("backup_count", 5)))
        )
    
    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            "database": {
                "host": self.database.host,
                "port": self.database.port,
                "database": self.database.database,
                "username": self.database.username,
                "password": self.database.password
            },
            "redis": {
                "host": self.redis.host,
                "port": self.redis.port,
                "password": self.redis.password,
                "db": self.redis.db
            },
            "ai": {
                "backend": self.ai.backend,
                "base_url": self.ai.base_url,
                "model_name": self.ai.model_name,
                "api_key": self.ai.api_key,
                "timeout": self.ai.timeout,
                "max_tokens": self.ai.max_tokens,
                "temperature": self.ai.temperature
            },
            "security": {
                "secret_key": self.security.secret_key,
                "jwt_algorithm": self.security.jwt_algorithm,
                "access_token_expire_minutes": self.security.access_token_expire_minutes,
                "password_min_length": self.security.password_min_length,
                "max_login_attempts": self.security.max_login_attempts,
                "session_timeout_minutes": self.security.session_timeout_minutes
            },
            "app": {
                "environment": self.app.environment,
                "debug": self.app.debug,
                "host": self.app.host,
                "port": self.app.port,
                "api_port": self.app.api_port,
                "max_file_size_mb": self.app.max_file_size_mb,
                "allowed_file_types": self.app.allowed_file_types
            },
            "logging": {
                "level": self.logging.level,
                "format": self.logging.format,
                "file_path": self.logging.file_path,
                "max_file_size_mb": self.logging.max_file_size_mb,
                "backup_count": self.logging.backup_count
            }
        }
        
        # Create directory if it doesn't exist
        config_dir = os.path.dirname(self.config_file)
        if config_dir:  # Only create directory if path has a directory component
            os.makedirs(config_dir, exist_ok=True)
        
        with open(self.config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.app.environment.lower() == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.app.environment.lower() == "development"
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        return self.database.url
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL"""
        return self.redis.url
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return validation results"""
        issues = []
        warnings = []
        
        # Database validation
        if not self.database.password or self.database.password == "password":
            if self.is_production():
                issues.append("Database password should be changed in production")
            else:
                warnings.append("Using default database password")
        
        # Security validation
        if self.security.secret_key == "maxbit_legal_contracts_secret_2025":
            if self.is_production():
                issues.append("Secret key should be changed in production")
            else:
                warnings.append("Using default secret key")
        
        if self.security.password_min_length < 8:
            warnings.append("Password minimum length is less than 8 characters")
        
        # AI validation
        if not self.ai.base_url:
            issues.append("AI base URL is not configured")
        
        # File size validation
        if self.app.max_file_size_mb > 100:
            warnings.append("Maximum file size is very large (>100MB)")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings
        }

# Global configuration instance
config = ConfigManager()

def get_config() -> ConfigManager:
    """Get global configuration instance"""
    return config

def reload_config():
    """Reload configuration from file and environment"""
    global config
    config = ConfigManager()
    return config
