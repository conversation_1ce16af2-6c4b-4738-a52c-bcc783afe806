#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility Functions Module
Handles file validation, text extraction, and other utility functions
"""

import streamlit as st
import tempfile
import os
from pathlib import Path
from typing import Optional, Dict, Any, List
import json
from datetime import datetime

# Import libraries for file processing
try:
    import docx
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

def validate_file(uploaded_file) -> bool:
    """
    Validate uploaded file type and size
    
    Args:
        uploaded_file: Streamlit uploaded file object
        
    Returns:
        bool: True if file is valid, False otherwise
    """
    if uploaded_file is None:
        return False
    
    # Check file extension
    allowed_extensions = ['.txt', '.doc', '.docx', '.pdf']
    file_extension = Path(uploaded_file.name).suffix.lower()
    
    if file_extension not in allowed_extensions:
        st.error(f"نوع الملف غير مدعوم: {file_extension}")
        st.info("الأنواع المدعومة: TXT, DOC, DOCX, PDF")
        return False
    
    # Check file size (max 10MB)
    max_size = 10 * 1024 * 1024  # 10MB in bytes
    if uploaded_file.size > max_size:
        st.error(f"حجم الملف كبير جداً: {uploaded_file.size / (1024*1024):.1f} MB")
        st.info("الحد الأقصى للحجم: 10 MB")
        return False
    
    return True

def extract_text_from_file(uploaded_file) -> Optional[str]:
    """
    Extract text content from uploaded file
    
    Args:
        uploaded_file: Streamlit uploaded file object
        
    Returns:
        str: Extracted text content or None if extraction fails
    """
    if not uploaded_file:
        return None
    
    file_extension = Path(uploaded_file.name).suffix.lower()
    
    try:
        if file_extension == '.txt':
            return extract_text_from_txt(uploaded_file)
        elif file_extension in ['.doc', '.docx']:
            return extract_text_from_docx(uploaded_file)
        elif file_extension == '.pdf':
            return extract_text_from_pdf(uploaded_file)
        else:
            st.error(f"نوع الملف غير مدعوم: {file_extension}")
            return None
            
    except Exception as e:
        st.error(f"خطأ في استخراج النص: {str(e)}")
        return None

def extract_text_from_txt(uploaded_file) -> str:
    """Extract text from TXT file"""
    try:
        # Try different encodings
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1256']
        
        for encoding in encodings:
            try:
                content = uploaded_file.read().decode(encoding)
                uploaded_file.seek(0)  # Reset file pointer
                return content
            except UnicodeDecodeError:
                uploaded_file.seek(0)  # Reset file pointer
                continue
        
        # If all encodings fail, use utf-8 with error handling
        content = uploaded_file.read().decode('utf-8', errors='ignore')
        return content
        
    except Exception as e:
        raise Exception(f"فشل في قراءة ملف TXT: {str(e)}")

def extract_text_from_docx(uploaded_file) -> str:
    """Extract text from DOCX file"""
    if not PYTHON_DOCX_AVAILABLE:
        raise Exception("مكتبة python-docx غير متوفرة. قم بتثبيتها: pip install python-docx")
    
    try:
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as tmp_file:
            tmp_file.write(uploaded_file.read())
            tmp_file_path = tmp_file.name
        
        # Extract text using python-docx
        doc = docx.Document(tmp_file_path)
        
        text_content = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text)
        
        # Extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_content.append(cell.text)
        
        # Clean up temporary file
        os.unlink(tmp_file_path)
        
        return '\n'.join(text_content)
        
    except Exception as e:
        raise Exception(f"فشل في قراءة ملف DOCX: {str(e)}")

def extract_text_from_pdf(uploaded_file) -> str:
    """Extract text from PDF file"""
    text_content = []
    
    # Try pdfplumber first (better for complex layouts)
    if PDFPLUMBER_AVAILABLE:
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                tmp_file.write(uploaded_file.read())
                tmp_file_path = tmp_file.name
            
            import pdfplumber
            with pdfplumber.open(tmp_file_path) as pdf:
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            os.unlink(tmp_file_path)
            
            if text_content:
                return '\n'.join(text_content)
                
        except Exception as e:
            st.warning(f"فشل استخدام pdfplumber: {str(e)}")
    
    # Fallback to PyPDF2
    if PYPDF2_AVAILABLE:
        try:
            uploaded_file.seek(0)  # Reset file pointer
            
            pdf_reader = PyPDF2.PdfReader(uploaded_file)
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text = page.extract_text()
                if text:
                    text_content.append(text)
            
            if text_content:
                return '\n'.join(text_content)
                
        except Exception as e:
            st.warning(f"فشل استخدام PyPDF2: {str(e)}")
    
    # If both methods fail
    if not text_content:
        raise Exception("فشل في استخراج النص من ملف PDF. تأكد من أن الملف يحتوي على نص قابل للاستخراج.")
    
    return '\n'.join(text_content)

def save_analysis_history(analysis: Dict[str, Any]):
    """Save analysis to session history"""
    if 'analysis_history' not in st.session_state:
        st.session_state.analysis_history = []
    
    # Add timestamp if not present
    if 'timestamp' not in analysis:
        analysis['timestamp'] = datetime.now().isoformat()
    
    # Limit history to last 10 analyses
    st.session_state.analysis_history.append(analysis)
    if len(st.session_state.analysis_history) > 10:
        st.session_state.analysis_history = st.session_state.analysis_history[-10:]

def export_analysis_history() -> str:
    """Export analysis history as JSON"""
    if 'analysis_history' not in st.session_state:
        return json.dumps([], ensure_ascii=False, indent=2)
    
    return json.dumps(st.session_state.analysis_history, ensure_ascii=False, indent=2)

def import_analysis_history(json_data: str) -> bool:
    """Import analysis history from JSON"""
    try:
        history = json.loads(json_data)
        if isinstance(history, list):
            st.session_state.analysis_history = history
            return True
        else:
            return False
    except json.JSONDecodeError:
        return False

def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove excessive whitespace
    text = ' '.join(text.split())
    
    # Remove special characters that might cause issues
    text = text.replace('\x00', '')  # Remove null characters
    text = text.replace('\ufeff', '')  # Remove BOM
    
    return text.strip()

def truncate_text(text: str, max_length: int = 1000) -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length] + "..."

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_file_info(uploaded_file) -> Dict[str, Any]:
    """Get comprehensive file information"""
    if not uploaded_file:
        return {}
    
    return {
        'name': uploaded_file.name,
        'size': uploaded_file.size,
        'size_formatted': format_file_size(uploaded_file.size),
        'type': uploaded_file.type,
        'extension': Path(uploaded_file.name).suffix.lower()
    }

def validate_analysis_result(analysis: Dict[str, Any]) -> bool:
    """Validate analysis result structure"""
    required_fields = ['filename', 'timestamp']
    optional_fields = ['translation', 'legal_points', 'recommendations', 'original_text']
    
    # Check required fields
    for field in required_fields:
        if field not in analysis:
            return False
    
    # Check that at least one analysis result is present
    has_results = any(field in analysis and analysis[field] for field in optional_fields)
    
    return has_results

def create_analysis_summary(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Create a summary of analysis results"""
    summary = {
        'filename': analysis.get('filename', 'Unknown'),
        'timestamp': analysis.get('timestamp', ''),
        'has_translation': bool(analysis.get('translation')),
        'legal_points_count': len(analysis.get('legal_points', [])),
        'recommendations_count': len(analysis.get('recommendations', [])),
        'original_text_length': len(analysis.get('original_text', ''))
    }
    
    # Count priority levels for legal points
    legal_points = analysis.get('legal_points', [])
    summary['high_priority_points'] = len([p for p in legal_points if p.get('priority') == 'high'])
    summary['medium_priority_points'] = len([p for p in legal_points if p.get('priority') == 'medium'])
    summary['low_priority_points'] = len([p for p in legal_points if p.get('priority') == 'low'])
    
    # Count urgency levels for recommendations
    recommendations = analysis.get('recommendations', [])
    summary['urgent_recommendations'] = len([r for r in recommendations if r.get('urgency') == 'urgent'])
    summary['important_recommendations'] = len([r for r in recommendations if r.get('urgency') == 'important'])
    summary['optional_recommendations'] = len([r for r in recommendations if r.get('urgency') == 'optional'])
    
    return summary

def search_in_analysis(analysis: Dict[str, Any], search_term: str) -> List[Dict[str, Any]]:
    """Search for a term within analysis results"""
    results = []
    search_term = search_term.lower()
    
    # Search in translation
    if analysis.get('translation') and search_term in analysis['translation'].lower():
        results.append({
            'section': 'translation',
            'title': 'الترجمة',
            'content': analysis['translation']
        })
    
    # Search in legal points
    for point in analysis.get('legal_points', []):
        if (search_term in point.get('title', '').lower() or 
            search_term in point.get('description', '').lower()):
            results.append({
                'section': 'legal_points',
                'title': point.get('title', ''),
                'content': point.get('description', ''),
                'priority': point.get('priority', '')
            })
    
    # Search in recommendations
    for rec in analysis.get('recommendations', []):
        if (search_term in rec.get('title', '').lower() or 
            search_term in rec.get('description', '').lower()):
            results.append({
                'section': 'recommendations',
                'title': rec.get('title', ''),
                'content': rec.get('description', ''),
                'urgency': rec.get('urgency', '')
            })
    
    return results
