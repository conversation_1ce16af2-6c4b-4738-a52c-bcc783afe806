#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI Components Module
Handles custom styling, sidebar, header, and other UI elements
"""

import streamlit as st
from datetime import datetime
from typing import List, Dict, Any
from ai_backend import ContractAnalyzer

def load_custom_css():
    """Load custom CSS for modern Arabic UI design"""
    css = """
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
    
    /* Global styles */
    .stApp {
        direction: rtl;
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        background: linear-gradient(135deg, #f4f7fa 0%, #e8f4f8 100%);
    }
    
    /* Main content styling */
    .main-content {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(52, 152, 219, 0.1);
        border: 1px solid rgba(52, 152, 219, 0.1);
    }
    
    /* Header styling */
    .app-header {
        background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(52, 152, 219, 0.3);
    }
    
    .app-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .app-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        font-weight: 300;
    }
    
    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
        border-right: 3px solid #3498db;
    }
    
    .sidebar-content {
        padding: 1rem;
        background: white;
        border-radius: 10px;
        margin: 1rem 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
    }
    
    /* File uploader styling */
    .stFileUploader {
        background: #f8f9fa;
        border: 2px dashed #3498db;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stFileUploader:hover {
        border-color: #2980b9;
        background: #e3f2fd;
    }
    
    /* Tab styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 0.5rem;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: white;
        border-radius: 8px;
        color: #2c3e50;
        font-weight: 600;
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
    }
    
    /* Translation content styling */
    .translation-content {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        border-right: 4px solid #3498db;
        line-height: 1.8;
        font-size: 1.1rem;
        direction: rtl;
        text-align: right;
    }
    
    .legal-term {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
        margin: 0 2px;
    }
    
    /* Progress bar styling */
    .stProgress > div > div > div > div {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        border-radius: 10px;
    }
    
    /* Alert styling */
    .stAlert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    /* Expander styling */
    .streamlit-expanderHeader {
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        font-family: 'Noto Sans Arabic', Arial, sans-serif;
        font-weight: 600;
    }
    
    /* Metric styling */
    .metric-container {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #3498db;
        margin: 0.5rem 0;
    }
    
    /* Dark mode styles */
    .dark-mode {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
    }
    
    .dark-mode .main-content {
        background: #34495e;
        color: #ecf0f1;
        border-color: #3498db;
    }
    
    .dark-mode .translation-content {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .app-title {
            font-size: 2rem;
        }

        .main-content {
            padding: 1rem;
            margin: 0.5rem 0;
        }

        .stButton > button {
            width: 100%;
            margin: 0.25rem 0;
        }

        .app-header > div {
            flex-direction: column;
            text-align: center;
        }

        .metric-container {
            margin: 0.5rem 0;
            padding: 1rem;
        }

        .stTabs [data-baseweb="tab-list"] {
            flex-wrap: wrap;
        }

        .stTabs [data-baseweb="tab"] {
            min-width: auto;
            flex: 1;
            margin: 0.25rem;
        }
    }
    
    /* Animation classes */
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .slide-in-right {
        animation: slideInRight 0.5s ease-out;
    }
    
    @keyframes slideInRight {
        from { transform: translateX(100px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    /* Status indicators */
    .status-success {
        color: #27ae60;
        font-weight: 600;
    }
    
    .status-warning {
        color: #f39c12;
        font-weight: 600;
    }
    
    .status-error {
        color: #e74c3c;
        font-weight: 600;
    }
    
    /* Loading spinner */
    .loading-spinner {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    </style>
    """
    
    st.markdown(css, unsafe_allow_html=True)

def create_header():
    """Create application header with MAXBIT branding"""
    st.markdown("""
    <div class="app-header fade-in">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <div>
                <div class="app-title">⚖️ محلل العقود القانونية الكويتية</div>
                <div class="app-subtitle">تحليل العقود الإنجليزية وفقاً للقانون الكويتي باستخدام الذكاء الاصطناعي المحلي</div>
            </div>
            <div style="text-align: right;">
                <img src="https://maxbit.net/wp-content/uploads/2022/08/logo.svg"
                     alt="MAXBIT LLC"
                     style="height: 60px; margin-bottom: 5px;">
                <div style="font-size: 12px; color: #666; text-align: center;">
                    Powered by <strong>MAXBIT LLC</strong><br>
                    © 2025
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_sidebar():
    """Create application sidebar with settings and options"""
    with st.sidebar:
        st.markdown('<div class="sidebar-content">', unsafe_allow_html=True)
        
        # App info
        st.markdown("### ⚙️ إعدادات التطبيق")
        
        # AI Backend selection
        st.markdown("#### 🤖 نظام الذكاء الاصطناعي")
        backend_options = {
            "ollama": "Ollama (محلي)",
            "lmstudio": "LM Studio (محلي)"
        }
        
        selected_backend = st.selectbox(
            "اختر نظام الذكاء الاصطناعي:",
            options=list(backend_options.keys()),
            format_func=lambda x: backend_options[x],
            index=1 if st.session_state.ai_backend == "lmstudio" else 0
        )
        
        if selected_backend != st.session_state.ai_backend:
            st.session_state.ai_backend = selected_backend
            st.rerun()
        
        # Model selection
        st.markdown("#### 🧠 نموذج الذكاء الاصطناعي")
        
        # Test connection and get available models
        analyzer = ContractAnalyzer(backend=st.session_state.ai_backend)
        
        if analyzer.test_connection():
            st.success("✅ متصل بنجاح")
            
            available_models = analyzer.get_available_models()
            if available_models:
                selected_model = st.selectbox(
                    "اختر النموذج:",
                    options=available_models,
                    index=0 if st.session_state.selected_model not in available_models else available_models.index(st.session_state.selected_model)
                )
                
                if selected_model != st.session_state.selected_model:
                    st.session_state.selected_model = selected_model
            else:
                st.warning("⚠️ لا توجد نماذج متاحة")
                st.session_state.selected_model = "llama3.1:8b"
        else:
            st.error("❌ فشل الاتصال")
            st.info("تأكد من تشغيل Ollama أو LM Studio")
        
        st.markdown("---")
        
        # Theme toggle
        st.markdown("#### 🎨 المظهر")
        dark_mode = st.toggle("الوضع الليلي", value=st.session_state.dark_mode)
        
        if dark_mode != st.session_state.dark_mode:
            st.session_state.dark_mode = dark_mode
            st.rerun()
        
        st.markdown("---")
        
        # Analysis history
        st.markdown("#### 📚 سجل التحليلات")
        
        if st.session_state.analysis_history:
            st.write(f"عدد التحليلات: {len(st.session_state.analysis_history)}")
            
            # Show recent analyses
            for i, analysis in enumerate(st.session_state.analysis_history[-3:]):
                with st.expander(f"📄 {analysis.get('filename', f'تحليل {i+1}')}"):
                    st.write(f"التاريخ: {format_timestamp(analysis.get('timestamp'))}")
                    if st.button(f"عرض التحليل {i+1}", key=f"view_{i}"):
                        st.session_state.current_analysis = analysis
                        st.rerun()
            
            if st.button("🗑️ مسح السجل"):
                st.session_state.analysis_history = []
                st.success("تم مسح السجل")
                st.rerun()
        else:
            st.info("لا توجد تحليلات سابقة")
        
        st.markdown("---")
        
        # Help section
        st.markdown("#### ❓ المساعدة")
        
        with st.expander("📖 كيفية الاستخدام"):
            st.markdown("""
            **خطوات التحليل:**
            1. ارفع ملف العقد (TXT, DOC, DOCX, PDF)
            2. انقر على "تحليل العقد"
            3. انتظر اكتمال التحليل
            4. اعرض النتائج في التبويبات
            5. صدر النتائج بالتنسيق المطلوب
            
            **الأنظمة المدعومة:**
            - Ollama (محلي)
            - LM Studio (محلي)
            
            **تنسيقات التصدير:**
            - PDF
            - Word (DOCX)
            - JSON
            """)
        
        with st.expander("⚖️ المراجع القانونية"):
            st.markdown("""
            **القوانين المرجعية:**
            - القانون المدني رقم 67/1980
            - القانون التجاري رقم 68/1980
            - قانون المرافعات المدنية والتجارية
            - قانون العمل الكويتي
            """)
        
        st.markdown("---")
        
        # Footer with MAXBIT branding
        st.markdown("""
        <div style="text-align: center; color: #7f8c8d; font-size: 0.8rem;">
            <img src="https://maxbit.net/wp-content/uploads/2022/08/logo.svg"
                 alt="MAXBIT LLC"
                 style="height: 30px; margin-bottom: 5px;">
            <p>محلل العقود القانونية الكويتية v1.0</p>
            <p>تطوير: <strong>MAXBIT LLC</strong></p>
            <p>© 2025 جميع الحقوق محفوظة</p>
            <p style="font-size: 0.7rem;"><a href="https://maxbit.net" target="_blank" style="color: #3498db;">www.maxbit.net</a></p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)

def format_timestamp(timestamp: str) -> str:
    """Format timestamp for display"""
    try:
        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M')
    except:
        return timestamp or 'غير محدد'

def show_connection_status(backend: str) -> bool:
    """Show connection status indicator"""
    analyzer = ContractAnalyzer(backend=backend)
    
    if analyzer.test_connection():
        st.success(f"✅ متصل بـ {backend.upper()}")
        return True
    else:
        st.error(f"❌ غير متصل بـ {backend.upper()}")
        st.info("تأكد من تشغيل الخدمة المطلوبة")
        return False

def create_progress_indicator(stage: str, percentage: int):
    """Create animated progress indicator"""
    progress_html = f"""
    <div style="background: #f8f9fa; border-radius: 10px; padding: 1rem; margin: 1rem 0;">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.5rem;">
            <span style="font-weight: 600; color: #2c3e50;">{stage}</span>
            <span style="font-weight: 600; color: #3498db;">{percentage}%</span>
        </div>
        <div style="background: #e9ecef; border-radius: 10px; height: 8px; overflow: hidden;">
            <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); height: 100%; width: {percentage}%; transition: width 0.3s ease; border-radius: 10px;"></div>
        </div>
    </div>
    """
    return progress_html

def create_metric_card(title: str, value: str, icon: str = "📊"):
    """Create a metric display card"""
    card_html = f"""
    <div class="metric-container fade-in">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <div style="font-size: 0.9rem; color: #7f8c8d; margin-bottom: 0.25rem;">{title}</div>
                <div style="font-size: 1.5rem; font-weight: 700; color: #2c3e50;">{value}</div>
            </div>
            <div style="font-size: 2rem; opacity: 0.7;">{icon}</div>
        </div>
    </div>
    """
    return card_html

def show_toast_notification(message: str, type: str = "success"):
    """Show toast notification"""
    if type == "success":
        st.success(f"✅ {message}")
    elif type == "warning":
        st.warning(f"⚠️ {message}")
    elif type == "error":
        st.error(f"❌ {message}")
    else:
        st.info(f"ℹ️ {message}")

def create_footer():
    """Create application footer with MAXBIT branding"""
    st.markdown("""
    <div style="margin-top: 50px; padding: 20px; border-top: 1px solid #e0e0e0; text-align: center; background-color: #f8f9fa;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 10px;">
            <img src="https://maxbit.net/wp-content/uploads/2022/08/logo.svg"
                 alt="MAXBIT LLC"
                 style="height: 40px;">
            <div style="font-size: 16px; font-weight: bold; color: #333;">
                MAXBIT LLC
            </div>
        </div>
        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">
            محلل العقود القانونية الكويتية - تطوير شركة ماكس بت المحدودة
        </div>
        <div style="font-size: 12px; color: #888;">
            © 2025 MAXBIT LLC. جميع الحقوق محفوظة.
        </div>
        <div style="font-size: 11px; color: #aaa; margin-top: 5px;">
            <a href="https://maxbit.net" target="_blank" style="color: #3498db; text-decoration: none;">
                www.maxbit.net
            </a>
        </div>
    </div>
    """, unsafe_allow_html=True)
