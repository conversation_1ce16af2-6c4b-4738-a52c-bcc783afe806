"""
Contract Template Library System
Developed by MAXBIT LLC © 2025
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
from database import ContractType

class TemplateCategory:
    """Template category definitions"""
    EMPLOYMENT = "employment"
    COMMERCIAL = "commercial"
    REAL_ESTATE = "real_estate"
    SERVICE = "service"
    PARTNERSHIP = "partnership"
    GOVERNMENT = "government"
    INTERNATIONAL = "international"

class TemplateComplexity:
    """Template complexity levels"""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class TemplateLibrary:
    """Manage contract templates"""
    
    def __init__(self, templates_file: str = "templates.json"):
        self.templates_file = templates_file
        self.templates = self._load_templates()
        self._initialize_default_templates()
    
    def _load_templates(self) -> Dict[str, Any]:
        """Load templates from file"""
        if os.path.exists(self.templates_file):
            try:
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_templates(self):
        """Save templates to file"""
        with open(self.templates_file, 'w', encoding='utf-8') as f:
            json.dump(self.templates, f, ensure_ascii=False, indent=2)
    
    def _initialize_default_templates(self):
        """Initialize default Kuwaiti legal templates"""
        if not self.templates:
            default_templates = self._create_default_templates()
            for template in default_templates:
                self.add_template(
                    template["title"],
                    template["description"],
                    template["content"],
                    template["category"],
                    template["complexity"],
                    template["variables"],
                    "system",
                    template["tags"],
                    template["legal_references"]
                )
    
    def _create_default_templates(self) -> List[Dict[str, Any]]:
        """Create default Kuwaiti legal contract templates"""
        return [
            {
                "title": "عقد عمل أساسي - القطاع الخاص",
                "description": "نموذج عقد عمل للقطاع الخاص وفقاً لقانون العمل الكويتي رقم 6/2010",
                "category": TemplateCategory.EMPLOYMENT,
                "complexity": TemplateComplexity.BASIC,
                "content": """عقد عمل

بين: {{employer_name}} (صاحب العمل)
العنوان: {{employer_address}}
الرقم المدني: {{employer_civil_id}}

وبين: {{employee_name}} (العامل)
العنوان: {{employee_address}}
الرقم المدني: {{employee_civil_id}}
الجنسية: {{employee_nationality}}

المادة الأولى: طبيعة العمل
يتعهد العامل بأداء العمل التالي: {{job_description}}
في {{work_location}}

المادة الثانية: مدة العقد
مدة هذا العقد {{contract_duration}} اعتباراً من {{start_date}}

المادة الثالثة: الراتب والمزايا
الراتب الأساسي: {{basic_salary}} دينار كويتي شهرياً
البدلات: {{allowances}}

المادة الرابعة: ساعات العمل
ساعات العمل اليومية: {{daily_hours}} ساعات
أيام العمل الأسبوعية: {{weekly_days}} أيام

المادة الخامسة: الإجازات
الإجازة السنوية: {{annual_leave}} يوماً
الإجازة المرضية: وفقاً لقانون العمل الكويتي

المادة السادسة: إنهاء العقد
يمكن إنهاء هذا العقد وفقاً لأحكام قانون العمل الكويتي رقم 6/2010

التوقيع:
صاحب العمل: ________________
العامل: ________________
التاريخ: {{signature_date}}""",
                "variables": [
                    "employer_name", "employer_address", "employer_civil_id",
                    "employee_name", "employee_address", "employee_civil_id", "employee_nationality",
                    "job_description", "work_location", "contract_duration", "start_date",
                    "basic_salary", "allowances", "daily_hours", "weekly_days",
                    "annual_leave", "signature_date"
                ],
                "tags": ["عمل", "قطاع خاص", "أساسي"],
                "legal_references": ["قانون العمل الكويتي رقم 6/2010"]
            },
            {
                "title": "عقد بيع عقار",
                "description": "نموذج عقد بيع عقار وفقاً للقانون المدني الكويتي",
                "category": TemplateCategory.REAL_ESTATE,
                "complexity": TemplateComplexity.INTERMEDIATE,
                "content": """عقد بيع عقار

البائع: {{seller_name}}
الرقم المدني: {{seller_civil_id}}
العنوان: {{seller_address}}

المشتري: {{buyer_name}}
الرقم المدني: {{buyer_civil_id}}
العنوان: {{buyer_address}}

وصف العقار:
الموقع: {{property_location}}
المساحة: {{property_area}} متر مربع
رقم القطعة: {{plot_number}}
رقم القسيمة: {{parcel_number}}

الثمن: {{sale_price}} دينار كويتي

شروط الدفع: {{payment_terms}}

التسليم: {{delivery_date}}

الضمانات: {{warranties}}

التوقيع:
البائع: ________________
المشتري: ________________
التاريخ: {{signature_date}}""",
                "variables": [
                    "seller_name", "seller_civil_id", "seller_address",
                    "buyer_name", "buyer_civil_id", "buyer_address",
                    "property_location", "property_area", "plot_number", "parcel_number",
                    "sale_price", "payment_terms", "delivery_date", "warranties", "signature_date"
                ],
                "tags": ["عقار", "بيع", "ملكية"],
                "legal_references": ["القانون المدني الكويتي رقم 67/1980"]
            },
            {
                "title": "عقد خدمات استشارية",
                "description": "نموذج عقد تقديم خدمات استشارية",
                "category": TemplateCategory.SERVICE,
                "complexity": TemplateComplexity.INTERMEDIATE,
                "content": """عقد خدمات استشارية

مقدم الخدمة: {{service_provider}}
الرقم التجاري: {{commercial_registration}}
العنوان: {{provider_address}}

العميل: {{client_name}}
العنوان: {{client_address}}

نطاق الخدمات: {{service_scope}}

مدة العقد: {{contract_duration}}

الأتعاب: {{fees}} دينار كويتي

شروط الدفع: {{payment_terms}}

المسؤوليات:
مقدم الخدمة: {{provider_responsibilities}}
العميل: {{client_responsibilities}}

السرية: {{confidentiality_clause}}

التوقيع:
مقدم الخدمة: ________________
العميل: ________________
التاريخ: {{signature_date}}""",
                "variables": [
                    "service_provider", "commercial_registration", "provider_address",
                    "client_name", "client_address", "service_scope", "contract_duration",
                    "fees", "payment_terms", "provider_responsibilities", "client_responsibilities",
                    "confidentiality_clause", "signature_date"
                ],
                "tags": ["خدمات", "استشارة", "مهني"],
                "legal_references": ["القانون المدني الكويتي رقم 67/1980", "القانون التجاري الكويتي رقم 68/1980"]
            }
        ]
    
    def add_template(self, title: str, description: str, content: str, 
                    category: str, complexity: str, variables: List[str],
                    created_by: str, tags: List[str] = None, 
                    legal_references: List[str] = None) -> str:
        """Add a new template"""
        template_id = str(uuid.uuid4())
        
        template = {
            "id": template_id,
            "title": title,
            "description": description,
            "content": content,
            "category": category,
            "complexity": complexity,
            "variables": variables or [],
            "tags": tags or [],
            "legal_references": legal_references or [],
            "created_by": created_by,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "usage_count": 0,
            "is_active": True
        }
        
        self.templates[template_id] = template
        self._save_templates()
        return template_id
    
    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get template by ID"""
        return self.templates.get(template_id)
    
    def update_template(self, template_id: str, updates: Dict[str, Any]) -> bool:
        """Update template"""
        if template_id in self.templates:
            self.templates[template_id].update(updates)
            self.templates[template_id]["updated_at"] = datetime.now().isoformat()
            self._save_templates()
            return True
        return False
    
    def delete_template(self, template_id: str) -> bool:
        """Delete template (deactivate)"""
        if template_id in self.templates:
            self.templates[template_id]["is_active"] = False
            self._save_templates()
            return True
        return False
    
    def search_templates(self, query: str = None, category: str = None, 
                        complexity: str = None, tags: List[str] = None) -> List[Dict[str, Any]]:
        """Search templates with filters"""
        results = []
        
        for template in self.templates.values():
            if not template["is_active"]:
                continue
            
            # Text search
            if query:
                query_lower = query.lower()
                if not (query_lower in template["title"].lower() or 
                       query_lower in template["description"].lower()):
                    continue
            
            # Category filter
            if category and template["category"] != category:
                continue
            
            # Complexity filter
            if complexity and template["complexity"] != complexity:
                continue
            
            # Tags filter
            if tags:
                template_tags = [tag.lower() for tag in template["tags"]]
                if not any(tag.lower() in template_tags for tag in tags):
                    continue
            
            results.append(template)
        
        # Sort by usage count and creation date
        results.sort(key=lambda x: (x["usage_count"], x["created_at"]), reverse=True)
        return results
    
    def increment_usage(self, template_id: str):
        """Increment template usage count"""
        if template_id in self.templates:
            self.templates[template_id]["usage_count"] += 1
            self._save_templates()
    
    def generate_contract_from_template(self, template_id: str, 
                                      variable_values: Dict[str, str]) -> str:
        """Generate contract from template with variable substitution"""
        template = self.get_template(template_id)
        if not template:
            return None
        
        content = template["content"]
        
        # Replace variables
        for variable, value in variable_values.items():
            placeholder = "{{" + variable + "}}"
            content = content.replace(placeholder, value)
        
        # Increment usage count
        self.increment_usage(template_id)
        
        return content
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get template library statistics"""
        active_templates = [t for t in self.templates.values() if t["is_active"]]
        
        # Count by category
        category_counts = {}
        for category in [TemplateCategory.EMPLOYMENT, TemplateCategory.COMMERCIAL, 
                        TemplateCategory.REAL_ESTATE, TemplateCategory.SERVICE, 
                        TemplateCategory.PARTNERSHIP, TemplateCategory.GOVERNMENT, 
                        TemplateCategory.INTERNATIONAL]:
            category_counts[category] = len([t for t in active_templates if t["category"] == category])
        
        # Count by complexity
        complexity_counts = {}
        for complexity in [TemplateComplexity.BASIC, TemplateComplexity.INTERMEDIATE, 
                          TemplateComplexity.ADVANCED, TemplateComplexity.EXPERT]:
            complexity_counts[complexity] = len([t for t in active_templates if t["complexity"] == complexity])
        
        # Most used templates
        most_used = sorted(active_templates, key=lambda x: x["usage_count"], reverse=True)[:5]
        
        return {
            "total_templates": len(active_templates),
            "category_distribution": category_counts,
            "complexity_distribution": complexity_counts,
            "most_used_templates": most_used,
            "total_usage": sum(t["usage_count"] for t in active_templates)
        }

class TemplateUI:
    """UI components for template library"""
    
    def __init__(self):
        self.template_library = TemplateLibrary()
    
    def display_template_library(self, current_user: Dict[str, Any]):
        """Display template library interface"""
        st.markdown("### 📚 مكتبة قوالب العقود")
        
        # Search and filters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            search_query = st.text_input("البحث في القوالب", placeholder="ابحث عن قالب...")
        
        with col2:
            category_filter = st.selectbox(
                "الفئة",
                options=["الكل"] + [TemplateCategory.EMPLOYMENT, TemplateCategory.COMMERCIAL, 
                        TemplateCategory.REAL_ESTATE, TemplateCategory.SERVICE, 
                        TemplateCategory.PARTNERSHIP, TemplateCategory.GOVERNMENT, 
                        TemplateCategory.INTERNATIONAL],
                format_func=self._get_category_name
            )
        
        with col3:
            complexity_filter = st.selectbox(
                "مستوى التعقيد",
                options=["الكل"] + [TemplateComplexity.BASIC, TemplateComplexity.INTERMEDIATE, 
                        TemplateComplexity.ADVANCED, TemplateComplexity.EXPERT],
                format_func=self._get_complexity_name
            )
        
        # Search templates
        search_params = {}
        if search_query:
            search_params["query"] = search_query
        if category_filter != "الكل":
            search_params["category"] = category_filter
        if complexity_filter != "الكل":
            search_params["complexity"] = complexity_filter
        
        templates = self.template_library.search_templates(**search_params)
        
        # Add new template button (for admins and lawyers)
        if current_user["role"] in ["admin", "lawyer"]:
            if st.button("➕ إضافة قالب جديد", type="primary"):
                st.session_state.show_add_template = True
        
        # Display templates
        if templates:
            for template in templates:
                self._display_template_card(template, current_user)
        else:
            st.info("لا توجد قوالب مطابقة لمعايير البحث")
        
        # Add template form
        if st.session_state.get("show_add_template", False):
            self._display_add_template_form(current_user)
    
    def _display_template_card(self, template: Dict[str, Any], current_user: Dict[str, Any]):
        """Display individual template card"""
        with st.expander(f"📄 {template['title']} - {self._get_complexity_name(template['complexity'])}"):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**الوصف:** {template['description']}")
                st.markdown(f"**الفئة:** {self._get_category_name(template['category'])}")
                st.markdown(f"**عدد الاستخدامات:** {template['usage_count']}")
                
                if template['tags']:
                    tags_str = " • ".join(template['tags'])
                    st.markdown(f"**العلامات:** {tags_str}")
                
                if template['legal_references']:
                    refs_str = " • ".join(template['legal_references'])
                    st.markdown(f"**المراجع القانونية:** {refs_str}")
            
            with col2:
                if st.button(f"استخدام القالب", key=f"use_{template['id']}"):
                    st.session_state.selected_template = template['id']
                    st.session_state.show_template_form = True
                
                if st.button(f"معاينة", key=f"preview_{template['id']}"):
                    st.session_state.preview_template = template['id']
                
                if current_user["role"] in ["admin", "lawyer"]:
                    if st.button(f"تعديل", key=f"edit_{template['id']}"):
                        st.session_state.edit_template = template['id']
        
        # Template usage form
        if st.session_state.get("selected_template") == template['id'] and st.session_state.get("show_template_form"):
            self._display_template_usage_form(template)
        
        # Template preview
        if st.session_state.get("preview_template") == template['id']:
            st.markdown("#### 👁️ معاينة القالب")
            st.code(template['content'], language='text')
            if st.button("إغلاق المعاينة", key=f"close_preview_{template['id']}"):
                st.session_state.preview_template = None
                st.rerun()
    
    def _display_template_usage_form(self, template: Dict[str, Any]):
        """Display form to use template with variables"""
        st.markdown("#### ✏️ ملء بيانات القالب")
        
        with st.form(f"template_form_{template['id']}"):
            variable_values = {}
            
            # Create input fields for each variable
            for variable in template['variables']:
                variable_values[variable] = st.text_input(
                    self._get_variable_label(variable),
                    key=f"var_{template['id']}_{variable}"
                )
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.form_submit_button("إنشاء العقد"):
                    # Check if all required fields are filled
                    if all(variable_values.values()):
                        generated_contract = self.template_library.generate_contract_from_template(
                            template['id'], variable_values
                        )
                        
                        st.session_state.generated_contract = generated_contract
                        st.session_state.contract_filename = f"{template['title']}.txt"
                        st.success("✅ تم إنشاء العقد بنجاح!")
                        
                    else:
                        st.error("يرجى ملء جميع الحقول المطلوبة")
            
            with col2:
                if st.form_submit_button("إلغاء"):
                    st.session_state.show_template_form = False
                    st.session_state.selected_template = None
                    st.rerun()
        
        # Display generated contract
        if st.session_state.get("generated_contract"):
            st.markdown("#### 📄 العقد المُنشأ")
            st.text_area("محتوى العقد", st.session_state.generated_contract, height=400)
            
            # Download button
            st.download_button(
                label="📥 تحميل العقد",
                data=st.session_state.generated_contract,
                file_name=st.session_state.contract_filename,
                mime="text/plain"
            )
            
            # Analyze button
            if st.button("🔍 تحليل العقد المُنشأ"):
                # Here you would integrate with the analysis system
                st.info("سيتم تحليل العقد...")
    
    def _display_add_template_form(self, current_user: Dict[str, Any]):
        """Display form to add new template"""
        st.markdown("#### ➕ إضافة قالب جديد")
        
        with st.form("add_template_form"):
            title = st.text_input("عنوان القالب")
            description = st.text_area("وصف القالب")
            
            col1, col2 = st.columns(2)
            
            with col1:
                category = st.selectbox(
                    "الفئة",
                    options=[TemplateCategory.EMPLOYMENT, TemplateCategory.COMMERCIAL, 
                            TemplateCategory.REAL_ESTATE, TemplateCategory.SERVICE, 
                            TemplateCategory.PARTNERSHIP, TemplateCategory.GOVERNMENT, 
                            TemplateCategory.INTERNATIONAL],
                    format_func=self._get_category_name
                )
            
            with col2:
                complexity = st.selectbox(
                    "مستوى التعقيد",
                    options=[TemplateComplexity.BASIC, TemplateComplexity.INTERMEDIATE, 
                            TemplateComplexity.ADVANCED, TemplateComplexity.EXPERT],
                    format_func=self._get_complexity_name
                )
            
            content = st.text_area("محتوى القالب", height=300, 
                                 help="استخدم {{variable_name}} للمتغيرات")
            
            variables = st.text_input("المتغيرات (مفصولة بفواصل)", 
                                    help="مثال: employer_name, employee_name, salary")
            
            tags = st.text_input("العلامات (مفصولة بفواصل)", 
                                help="مثال: عمل, قطاع خاص, أساسي")
            
            legal_references = st.text_area("المراجع القانونية (كل مرجع في سطر منفصل)")
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.form_submit_button("إضافة القالب"):
                    if title and description and content:
                        # Parse variables and tags
                        variables_list = [v.strip() for v in variables.split(',') if v.strip()]
                        tags_list = [t.strip() for t in tags.split(',') if t.strip()]
                        refs_list = [r.strip() for r in legal_references.split('\n') if r.strip()]
                        
                        template_id = self.template_library.add_template(
                            title, description, content, category, complexity,
                            variables_list, current_user["id"], tags_list, refs_list
                        )
                        
                        st.success(f"✅ تم إضافة القالب بنجاح (ID: {template_id[:8]})")
                        st.session_state.show_add_template = False
                        st.rerun()
                    else:
                        st.error("يرجى ملء الحقول المطلوبة")
            
            with col2:
                if st.form_submit_button("إلغاء"):
                    st.session_state.show_add_template = False
                    st.rerun()
    
    def display_template_statistics(self):
        """Display template library statistics"""
        stats = self.template_library.get_template_statistics()
        
        st.markdown("### 📊 إحصائيات مكتبة القوالب")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("إجمالي القوالب", stats["total_templates"])
        
        with col2:
            st.metric("إجمالي الاستخدامات", stats["total_usage"])
        
        with col3:
            avg_usage = stats["total_usage"] / stats["total_templates"] if stats["total_templates"] > 0 else 0
            st.metric("متوسط الاستخدام", f"{avg_usage:.1f}")
        
        # Most used templates
        if stats["most_used_templates"]:
            st.markdown("#### 🏆 القوالب الأكثر استخداماً")
            for i, template in enumerate(stats["most_used_templates"], 1):
                st.markdown(f"{i}. **{template['title']}** - {template['usage_count']} استخدام")
    
    def _get_category_name(self, category: str) -> str:
        """Get Arabic category name"""
        names = {
            "الكل": "الكل",
            TemplateCategory.EMPLOYMENT: "عقود العمل",
            TemplateCategory.COMMERCIAL: "عقود تجارية",
            TemplateCategory.REAL_ESTATE: "عقود عقارية",
            TemplateCategory.SERVICE: "عقود خدمات",
            TemplateCategory.PARTNERSHIP: "عقود شراكة",
            TemplateCategory.GOVERNMENT: "عقود حكومية",
            TemplateCategory.INTERNATIONAL: "عقود دولية"
        }
        return names.get(category, category)
    
    def _get_complexity_name(self, complexity: str) -> str:
        """Get Arabic complexity name"""
        names = {
            "الكل": "الكل",
            TemplateComplexity.BASIC: "أساسي",
            TemplateComplexity.INTERMEDIATE: "متوسط",
            TemplateComplexity.ADVANCED: "متقدم",
            TemplateComplexity.EXPERT: "خبير"
        }
        return names.get(complexity, complexity)
    
    def _get_variable_label(self, variable: str) -> str:
        """Get Arabic label for variable"""
        labels = {
            "employer_name": "اسم صاحب العمل",
            "employee_name": "اسم الموظف",
            "salary": "الراتب",
            "start_date": "تاريخ البداية",
            "contract_duration": "مدة العقد",
            "job_description": "وصف الوظيفة",
            "work_location": "مكان العمل",
            "seller_name": "اسم البائع",
            "buyer_name": "اسم المشتري",
            "property_location": "موقع العقار",
            "sale_price": "سعر البيع",
            "service_provider": "مقدم الخدمة",
            "client_name": "اسم العميل",
            "fees": "الأتعاب",
            "signature_date": "تاريخ التوقيع"
        }
        return labels.get(variable, variable.replace('_', ' ').title())
