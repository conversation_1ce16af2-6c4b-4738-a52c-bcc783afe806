"""
Contract Template Management System
Developed by MAXBIT LLC © 2025
"""

import sqlite3
import json
import uuid
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
import PyPDF2
import docx
from io import BytesIO

class TemplateManager:
    """Manage contract templates"""
    
    def __init__(self, db_manager=None, auth_manager=None, db_path: str = "contracts.db"):
        self.db_path = db_path
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.upload_dir = "uploaded_templates"
        os.makedirs(self.upload_dir, exist_ok=True)

        # Template categories
        self.categories = {
            'employment': 'عقود العمل',
            'commercial': 'العقود التجارية',
            'real_estate': 'عقود العقارات',
            'services': 'عقود الخدمات',
            'partnership': 'عقود الشراكة',
            'supply': 'عقود التوريد',
            'consulting': 'عقود الاستشارات',
            'other': 'أخرى'
        }
    
    def create_template(self, name: str, description: str, template_type: str, 
                       content: str, user_id: str, tags: str = "") -> str:
        """Create a new contract template"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            template_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO contract_templates (
                    id, name, description, template_type, content,
                    is_active, created_by, created_at, updated_at,
                    usage_count, tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                template_id, name, description, template_type, content,
                1, user_id, current_time, current_time, 0, tags
            ))
            
            conn.commit()
            conn.close()
            
            return template_id
            
        except Exception as e:
            print(f"Error creating template: {e}")
            return None
    
    def upload_template_file(self, uploaded_file, name: str, description: str, 
                           template_type: str, user_id: str, tags: str = "") -> str:
        """Upload and process template file (PDF/DOCX)"""
        try:
            # Extract content based on file type
            content = ""
            file_extension = uploaded_file.name.split('.')[-1].lower()
            
            if file_extension == 'pdf':
                content = self._extract_pdf_content(uploaded_file)
            elif file_extension in ['docx', 'doc']:
                content = self._extract_docx_content(uploaded_file)
            elif file_extension == 'txt':
                content = str(uploaded_file.read(), "utf-8")
            else:
                return None
            
            if not content:
                return None
            
            # Save file
            file_path = os.path.join(self.upload_dir, f"{uuid.uuid4()}_{uploaded_file.name}")
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # Create template in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            template_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO contract_templates (
                    id, name, description, template_type, content,
                    is_active, created_by, created_at, updated_at,
                    usage_count, tags, file_path
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                template_id, name, description, template_type, content,
                1, user_id, current_time, current_time, 0, tags, file_path
            ))
            
            conn.commit()
            conn.close()
            
            return template_id
            
        except Exception as e:
            print(f"Error uploading template: {e}")
            return None
    
    def _extract_pdf_content(self, pdf_file) -> str:
        """Extract text content from PDF"""
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(pdf_file.read()))
            content = ""
            for page in pdf_reader.pages:
                content += page.extract_text() + "\n"
            return content.strip()
        except Exception as e:
            print(f"Error extracting PDF content: {e}")
            return ""
    
    def _extract_docx_content(self, docx_file) -> str:
        """Extract text content from DOCX"""
        try:
            doc = docx.Document(BytesIO(docx_file.read()))
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            return content.strip()
        except Exception as e:
            print(f"Error extracting DOCX content: {e}")
            return ""
    
    def get_templates(self, template_type: str = None, user_id: str = None, 
                     active_only: bool = True) -> List[Dict[str, Any]]:
        """Get contract templates with filters"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT t.*, u.full_name as creator_name FROM contract_templates t LEFT JOIN users u ON t.created_by = u.id WHERE 1=1"
            params = []
            
            if active_only:
                query += " AND t.is_active = 1"
            
            if template_type:
                query += " AND t.template_type = ?"
                params.append(template_type)
            
            if user_id:
                query += " AND t.created_by = ?"
                params.append(user_id)
            
            query += " ORDER BY t.usage_count DESC, t.created_at DESC"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            templates = [dict(row) for row in rows]
            conn.close()
            
            return templates
            
        except Exception as e:
            print(f"Error getting templates: {e}")
            return []
    
    def get_template_by_id(self, template_id: str) -> Optional[Dict[str, Any]]:
        """Get specific template by ID"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT t.*, u.full_name as creator_name 
                FROM contract_templates t 
                LEFT JOIN users u ON t.created_by = u.id 
                WHERE t.id = ?
            """, (template_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            return dict(row) if row else None
            
        except Exception as e:
            print(f"Error getting template: {e}")
            return None
    
    def update_template(self, template_id: str, name: str = None, description: str = None,
                       content: str = None, tags: str = None) -> bool:
        """Update template"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            updates = []
            params = []
            
            if name:
                updates.append("name = ?")
                params.append(name)
            
            if description:
                updates.append("description = ?")
                params.append(description)
            
            if content:
                updates.append("content = ?")
                params.append(content)
            
            if tags:
                updates.append("tags = ?")
                params.append(tags)
            
            if updates:
                updates.append("updated_at = ?")
                params.append(datetime.now().isoformat())
                params.append(template_id)
                
                query = f"UPDATE contract_templates SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(query, params)
                
                conn.commit()
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"Error updating template: {e}")
            return False
    
    def increment_usage(self, template_id: str):
        """Increment template usage count"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE contract_templates 
                SET usage_count = usage_count + 1, updated_at = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), template_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error incrementing usage: {e}")
    
    def delete_template(self, template_id: str) -> bool:
        """Soft delete template (mark as inactive)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE contract_templates 
                SET is_active = 0, updated_at = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), template_id))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"Error deleting template: {e}")
            return False
    
    def search_templates(self, query: str, template_type: str = None) -> List[Dict[str, Any]]:
        """Search templates by name, description, or content"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            search_query = """
                SELECT t.*, u.full_name as creator_name 
                FROM contract_templates t 
                LEFT JOIN users u ON t.created_by = u.id 
                WHERE t.is_active = 1 AND (
                    t.name LIKE ? OR 
                    t.description LIKE ? OR 
                    t.content LIKE ? OR 
                    t.tags LIKE ?
                )
            """
            params = [f"%{query}%"] * 4
            
            if template_type:
                search_query += " AND t.template_type = ?"
                params.append(template_type)
            
            search_query += " ORDER BY t.usage_count DESC"
            
            cursor.execute(search_query, params)
            rows = cursor.fetchall()
            
            templates = [dict(row) for row in rows]
            conn.close()
            
            return templates
            
        except Exception as e:
            print(f"Error searching templates: {e}")
            return []
    
    def get_template_statistics(self) -> Dict[str, Any]:
        """Get template usage statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Total templates
            cursor.execute("SELECT COUNT(*) FROM contract_templates WHERE is_active = 1")
            total_templates = cursor.fetchone()[0]
            
            # Templates by type
            cursor.execute("""
                SELECT template_type, COUNT(*) 
                FROM contract_templates 
                WHERE is_active = 1 
                GROUP BY template_type
            """)
            type_stats = dict(cursor.fetchall())
            
            # Most used templates
            cursor.execute("""
                SELECT name, usage_count 
                FROM contract_templates 
                WHERE is_active = 1 
                ORDER BY usage_count DESC 
                LIMIT 5
            """)
            popular_templates = cursor.fetchall()
            
            conn.close()
            
            return {
                "total_templates": total_templates,
                "type_statistics": type_stats,
                "popular_templates": popular_templates
            }
            
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {
                "total_templates": 0,
                "type_statistics": {},
                "popular_templates": []
            }

class TemplateUI:
    """UI components for template management"""

    def __init__(self):
        self.template_manager = TemplateManager()

    def display_template_library(self, current_user: Dict[str, Any]):
        """Display template library with enhanced cards"""
        st.markdown("### 📚 مكتبة نماذج العقود الكويتية")

        # Template type filter
        col1, col2, col3 = st.columns([2, 2, 1])

        with col1:
            template_types = {
                "": "جميع الأنواع",
                "employment": "عقود العمل",
                "real_estate": "العقارات",
                "commercial": "التجارية",
                "partnership": "الشراكة",
                "service": "الخدمات"
            }
            selected_type = st.selectbox("نوع العقد", options=list(template_types.keys()),
                                       format_func=lambda x: template_types[x])

        with col2:
            search_query = st.text_input("🔍 البحث في النماذج", placeholder="ابحث بالاسم أو الوصف...")

        with col3:
            if st.button("➕ إضافة نموذج جديد"):
                st.session_state.show_create_template = True

        # Get templates
        if search_query:
            templates = self.template_manager.search_templates(search_query, selected_type if selected_type else None)
        else:
            templates = self.template_manager.get_templates(selected_type if selected_type else None)

        # Display templates in enhanced cards
        if templates:
            # Group templates by type for better organization
            templates_by_type = {}
            for template in templates:
                t_type = template['template_type']
                if t_type not in templates_by_type:
                    templates_by_type[t_type] = []
                templates_by_type[t_type].append(template)

            for t_type, type_templates in templates_by_type.items():
                st.markdown(f"#### {template_types.get(t_type, t_type)}")

                # Display templates in rows of 2
                for i in range(0, len(type_templates), 2):
                    cols = st.columns(2)

                    for j, col in enumerate(cols):
                        if i + j < len(type_templates):
                            template = type_templates[i + j]
                            with col:
                                self._display_template_card(template, current_user)

                st.divider()
        else:
            st.info("لا توجد نماذج متاحة")

        # Show create template form if requested
        if st.session_state.get('show_create_template', False):
            self._show_create_template_form(current_user)

    def _display_template_card(self, template: Dict[str, Any], current_user: Dict[str, Any]):
        """Display enhanced template card"""
        # Create modern card with gradient background
        card_style = """
        <div style="
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: white;
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: -50%;
                right: -50%;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                pointer-events: none;
            "></div>
            <div style="position: relative; z-index: 1;">
                <h4 style="margin: 0 0 10px 0; color: #ffffff; font-weight: bold;">
                    {name}
                </h4>
                <p style="margin: 0 0 15px 0; color: #e0e7ff; font-size: 14px; line-height: 1.4;">
                    {description}
                </p>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <span style="
                        background: rgba(255, 255, 255, 0.2);
                        padding: 4px 12px;
                        border-radius: 20px;
                        font-size: 12px;
                        color: #ffffff;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    ">
                        {type_name}
                    </span>
                    <span style="color: #fbbf24; font-weight: bold; font-size: 14px;">
                        استخدم {usage_count} مرة
                    </span>
                </div>
                <div style="font-size: 12px; color: #cbd5e1; margin-bottom: 15px;">
                    <div>📅 تم الإنشاء: {created_date}</div>
                    <div>👤 بواسطة: {creator}</div>
                </div>
            </div>
        </div>
        """.format(
            name=template['name'],
            description=template['description'][:100] + "..." if len(template['description']) > 100 else template['description'],
            type_name=self._get_type_name(template['template_type']),
            usage_count=template['usage_count'],
            created_date=template['created_at'][:10],
            creator=template.get('creator_name', 'غير معروف')
        )

        st.markdown(card_style, unsafe_allow_html=True)

        # Action buttons
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("👁️ عرض", key=f"view_{template['id']}", use_container_width=True):
                st.session_state.selected_template = template['id']
                st.session_state.show_template_details = True

        with col2:
            if st.button("📋 استخدام", key=f"use_{template['id']}", use_container_width=True):
                self.template_manager.increment_usage(template['id'])
                st.session_state.selected_template_content = template['content']
                st.success("تم نسخ النموذج!")

        with col3:
            if current_user['id'] == template['created_by'] or current_user['role'] == 'admin':
                if st.button("✏️ تعديل", key=f"edit_{template['id']}", use_container_width=True):
                    st.session_state.edit_template = template['id']

    def _get_type_name(self, template_type: str) -> str:
        """Get Arabic name for template type"""
        type_names = {
            "employment": "عقود العمل",
            "real_estate": "العقارات",
            "commercial": "التجارية",
            "partnership": "الشراكة",
            "service": "الخدمات"
        }
        return type_names.get(template_type, template_type)

    def _show_create_template_form(self, current_user: Dict[str, Any]):
        """Show create template form"""
        with st.expander("➕ إنشاء نموذج جديد", expanded=True):
            with st.form("create_template_form"):
                col1, col2 = st.columns(2)

                with col1:
                    name = st.text_input("اسم النموذج *", placeholder="مثال: عقد عمل موظف بدوام كامل")
                    template_type = st.selectbox("نوع النموذج *",
                                               options=["employment", "real_estate", "commercial", "partnership", "service"],
                                               format_func=self._get_type_name)

                with col2:
                    description = st.text_area("وصف النموذج", placeholder="وصف مختصر للنموذج وغرض استخدامه")
                    tags = st.text_input("العلامات", placeholder="مثال: عمل,موظف,دوام_كامل")

                # Upload option
                st.markdown("**طريقة الإنشاء:**")
                creation_method = st.radio("", ["كتابة النص مباشرة", "رفع ملف (PDF/DOCX/TXT)"])

                if creation_method == "كتابة النص مباشرة":
                    content = st.text_area("محتوى النموذج *", height=300,
                                         placeholder="اكتب محتوى النموذج هنا...")
                    uploaded_file = None
                else:
                    content = ""
                    uploaded_file = st.file_uploader("اختر ملف النموذج",
                                                   type=['pdf', 'docx', 'doc', 'txt'])

                col1, col2 = st.columns(2)

                with col1:
                    if st.form_submit_button("💾 حفظ النموذج", use_container_width=True):
                        if name and template_type and (content or uploaded_file):
                            if uploaded_file:
                                template_id = self.template_manager.upload_template_file(
                                    uploaded_file, name, description, template_type,
                                    current_user['id'], tags
                                )
                            else:
                                template_id = self.template_manager.create_template(
                                    name, description, template_type, content,
                                    current_user['id'], tags
                                )

                            if template_id:
                                st.success("✅ تم إنشاء النموذج بنجاح!")
                                st.session_state.show_create_template = False
                                st.rerun()
                            else:
                                st.error("❌ حدث خطأ في إنشاء النموذج")
                        else:
                            st.error("⚠️ يرجى ملء جميع الحقول المطلوبة")

                with col2:
                    if st.form_submit_button("❌ إلغاء", use_container_width=True):
                        st.session_state.show_create_template = False
                        st.rerun()

    def display_template_statistics(self):
        """Display template usage statistics"""
        st.markdown("### 📊 إحصائيات النماذج")

        stats = self.template_manager.get_template_statistics()

        # Main metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("إجمالي النماذج", stats['total_templates'])

        with col2:
            most_used = stats['popular_templates'][0] if stats['popular_templates'] else ("لا يوجد", 0)
            st.metric("الأكثر استخداماً", f"{most_used[0][:20]}..." if len(most_used[0]) > 20 else most_used[0])

        with col3:
            total_usage = sum([t[1] for t in stats['popular_templates']])
            st.metric("إجمالي الاستخدام", total_usage)

        # Type distribution
        if stats['type_statistics']:
            st.markdown("#### توزيع النماذج حسب النوع")

            type_names = {
                "employment": "عقود العمل",
                "real_estate": "العقارات",
                "commercial": "التجارية",
                "partnership": "الشراكة",
                "service": "الخدمات"
            }

            chart_data = []
            for t_type, count in stats['type_statistics'].items():
                chart_data.append({
                    "النوع": type_names.get(t_type, t_type),
                    "العدد": count
                })

            st.bar_chart(data=chart_data, x="النوع", y="العدد")

        # Popular templates
        if stats['popular_templates']:
            st.markdown("#### النماذج الأكثر استخداماً")

            for i, (name, usage) in enumerate(stats['popular_templates'][:5], 1):
                st.markdown(f"{i}. **{name}** - استخدم {usage} مرة")
