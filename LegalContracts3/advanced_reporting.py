"""
Advanced Reporting & Analytics System
Developed by MAXBIT LLC © 2025
"""

import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import streamlit as st
from database import ContractDatabase, ContractType, ContractStatus
from risk_assessment import RiskLevel
from collaboration import CollaborationManager

class ReportGenerator:
    """Generate comprehensive reports and analytics"""
    
    def __init__(self):
        self.db = ContractDatabase()
        self.collaboration_manager = CollaborationManager()
    
    def generate_executive_summary(self, user_id: str = None, date_range: int = 30) -> Dict[str, Any]:
        """Generate executive summary report"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range)
        
        contracts = self.db.list_contracts(user_id=user_id, limit=1000)
        
        # Filter by date range
        filtered_contracts = []
        for contract in contracts:
            contract_date = datetime.fromisoformat(contract['created_at'])
            if start_date <= contract_date <= end_date:
                filtered_contracts.append(contract)
        
        total_contracts = len(filtered_contracts)
        
        # Risk analysis
        high_risk_contracts = len([c for c in filtered_contracts if c.get('risk_score', 0) >= 7])
        avg_risk_score = sum(c.get('risk_score', 0) for c in filtered_contracts) / total_contracts if total_contracts > 0 else 0
        
        # Status distribution
        status_counts = {}
        for status in [ContractStatus.PENDING, ContractStatus.ANALYZED, ContractStatus.REVIEWED, 
                      ContractStatus.APPROVED, ContractStatus.REJECTED]:
            status_counts[status] = len([c for c in filtered_contracts if c['status'] == status])
        
        # Type distribution
        type_counts = {}
        for contract_type in [ContractType.EMPLOYMENT, ContractType.COMMERCIAL, ContractType.REAL_ESTATE,
                             ContractType.SERVICE, ContractType.PARTNERSHIP, ContractType.OTHER]:
            type_counts[contract_type] = len([c for c in filtered_contracts if c['contract_type'] == contract_type])
        
        # Collaboration stats
        collaboration_stats = self.collaboration_manager.get_comment_statistics()
        
        return {
            "period": f"{date_range} أيام",
            "total_contracts": total_contracts,
            "high_risk_contracts": high_risk_contracts,
            "average_risk_score": round(avg_risk_score, 2),
            "status_distribution": status_counts,
            "type_distribution": type_counts,
            "collaboration_stats": collaboration_stats,
            "generated_at": datetime.now().isoformat()
        }
    
    def generate_risk_analysis_report(self, user_id: str = None) -> Dict[str, Any]:
        """Generate detailed risk analysis report"""
        contracts = self.db.list_contracts(user_id=user_id, limit=1000)
        
        risk_data = {
            "low_risk": [],
            "medium_risk": [],
            "high_risk": [],
            "critical_risk": []
        }
        
        for contract in contracts:
            risk_score = contract.get('risk_score', 0)
            if risk_score < 3:
                risk_data["low_risk"].append(contract)
            elif risk_score < 6:
                risk_data["medium_risk"].append(contract)
            elif risk_score < 8:
                risk_data["high_risk"].append(contract)
            else:
                risk_data["critical_risk"].append(contract)
        
        # Risk trends over time
        risk_trends = self._calculate_risk_trends(contracts)
        
        # Risk by contract type
        risk_by_type = self._calculate_risk_by_type(contracts)
        
        return {
            "risk_distribution": {k: len(v) for k, v in risk_data.items()},
            "risk_details": risk_data,
            "risk_trends": risk_trends,
            "risk_by_type": risk_by_type,
            "recommendations": self._generate_risk_recommendations(risk_data)
        }
    
    def generate_productivity_report(self, user_id: str = None, date_range: int = 30) -> Dict[str, Any]:
        """Generate productivity analysis report"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range)
        
        contracts = self.db.list_contracts(user_id=user_id, limit=1000)
        
        # Filter by date range
        period_contracts = []
        for contract in contracts:
            contract_date = datetime.fromisoformat(contract['created_at'])
            if start_date <= contract_date <= end_date:
                period_contracts.append(contract)
        
        # Daily analysis counts
        daily_counts = {}
        for contract in period_contracts:
            date_key = contract['created_at'][:10]  # YYYY-MM-DD
            daily_counts[date_key] = daily_counts.get(date_key, 0) + 1
        
        # Average processing time (mock data for now)
        avg_processing_time = self._calculate_avg_processing_time(period_contracts)
        
        # User productivity (if admin view)
        user_productivity = self._calculate_user_productivity(contracts) if not user_id else {}
        
        return {
            "period": f"{date_range} أيام",
            "total_analyzed": len(period_contracts),
            "daily_analysis": daily_counts,
            "average_processing_time": avg_processing_time,
            "user_productivity": user_productivity,
            "peak_analysis_day": max(daily_counts.items(), key=lambda x: x[1]) if daily_counts else None
        }
    
    def generate_compliance_report(self, user_id: str = None) -> Dict[str, Any]:
        """Generate compliance analysis report"""
        contracts = self.db.list_contracts(user_id=user_id, limit=1000)
        
        compliance_issues = []
        compliant_contracts = []
        
        for contract in contracts:
            risk_score = contract.get('risk_score', 0)
            if risk_score >= 6:  # High risk indicates potential compliance issues
                compliance_issues.append(contract)
            else:
                compliant_contracts.append(contract)
        
        # Compliance by contract type
        compliance_by_type = {}
        for contract_type in [ContractType.EMPLOYMENT, ContractType.COMMERCIAL, ContractType.REAL_ESTATE,
                             ContractType.SERVICE, ContractType.PARTNERSHIP, ContractType.OTHER]:
            type_contracts = [c for c in contracts if c['contract_type'] == contract_type]
            compliant_count = len([c for c in type_contracts if c.get('risk_score', 0) < 6])
            total_count = len(type_contracts)
            compliance_rate = (compliant_count / total_count * 100) if total_count > 0 else 0
            compliance_by_type[contract_type] = {
                "total": total_count,
                "compliant": compliant_count,
                "compliance_rate": round(compliance_rate, 2)
            }
        
        return {
            "total_contracts": len(contracts),
            "compliant_contracts": len(compliant_contracts),
            "compliance_issues": len(compliance_issues),
            "overall_compliance_rate": round(len(compliant_contracts) / len(contracts) * 100, 2) if contracts else 0,
            "compliance_by_type": compliance_by_type,
            "high_risk_contracts": [c for c in compliance_issues if c.get('risk_score', 0) >= 8]
        }
    
    def _calculate_risk_trends(self, contracts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate risk trends over time"""
        # Group contracts by month
        monthly_risks = {}
        for contract in contracts:
            month_key = contract['created_at'][:7]  # YYYY-MM
            risk_score = contract.get('risk_score', 0)
            
            if month_key not in monthly_risks:
                monthly_risks[month_key] = []
            monthly_risks[month_key].append(risk_score)
        
        # Calculate average risk per month
        monthly_avg_risk = {}
        for month, risks in monthly_risks.items():
            monthly_avg_risk[month] = sum(risks) / len(risks) if risks else 0
        
        return monthly_avg_risk
    
    def _calculate_risk_by_type(self, contracts: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate average risk by contract type"""
        type_risks = {}
        
        for contract_type in [ContractType.EMPLOYMENT, ContractType.COMMERCIAL, ContractType.REAL_ESTATE,
                             ContractType.SERVICE, ContractType.PARTNERSHIP, ContractType.OTHER]:
            type_contracts = [c for c in contracts if c['contract_type'] == contract_type]
            if type_contracts:
                avg_risk = sum(c.get('risk_score', 0) for c in type_contracts) / len(type_contracts)
                type_risks[contract_type] = round(avg_risk, 2)
            else:
                type_risks[contract_type] = 0
        
        return type_risks
    
    def _generate_risk_recommendations(self, risk_data: Dict[str, List]) -> List[str]:
        """Generate risk-based recommendations"""
        recommendations = []
        
        if len(risk_data["critical_risk"]) > 0:
            recommendations.append("🚨 يوجد عقود عالية المخاطر تحتاج مراجعة فورية")
        
        if len(risk_data["high_risk"]) > len(risk_data["low_risk"]):
            recommendations.append("⚠️ نسبة العقود عالية المخاطر مرتفعة - يُنصح بمراجعة عمليات التحليل")
        
        if len(risk_data["low_risk"]) > len(risk_data["high_risk"]) * 2:
            recommendations.append("✅ معظم العقود منخفضة المخاطر - أداء ممتاز")
        
        recommendations.append("📋 يُنصح بمراجعة دورية للعقود عالية المخاطر")
        recommendations.append("🔍 تطبيق معايير صارمة للعقود الجديدة")
        
        return recommendations
    
    def _calculate_avg_processing_time(self, contracts: List[Dict[str, Any]]) -> float:
        """Calculate average processing time (mock implementation)"""
        # In a real implementation, this would calculate actual processing time
        # For now, return a mock average based on contract complexity
        if not contracts:
            return 0
        
        total_time = 0
        for contract in contracts:
            # Mock calculation based on risk score and text length
            risk_score = contract.get('risk_score', 0)
            base_time = 15  # Base 15 minutes
            complexity_factor = risk_score / 10  # Higher risk = more time
            total_time += base_time * (1 + complexity_factor)
        
        return round(total_time / len(contracts), 2)
    
    def _calculate_user_productivity(self, contracts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate productivity by user"""
        user_stats = {}
        
        for contract in contracts:
            user_id = contract.get('created_by', 'unknown')
            if user_id not in user_stats:
                user_stats[user_id] = {
                    "contracts_analyzed": 0,
                    "avg_risk_score": 0,
                    "total_risk_score": 0
                }
            
            user_stats[user_id]["contracts_analyzed"] += 1
            user_stats[user_id]["total_risk_score"] += contract.get('risk_score', 0)
        
        # Calculate averages
        for user_id, stats in user_stats.items():
            if stats["contracts_analyzed"] > 0:
                stats["avg_risk_score"] = round(stats["total_risk_score"] / stats["contracts_analyzed"], 2)
        
        return user_stats

class ReportingUI:
    """UI components for advanced reporting"""
    
    def __init__(self):
        self.report_generator = ReportGenerator()
    
    def display_executive_dashboard(self, user_id: str = None):
        """Display executive dashboard"""
        st.markdown("### 📊 لوحة التحكم التنفيذية")
        
        # Date range selector
        col1, col2 = st.columns([3, 1])
        with col1:
            date_range = st.selectbox(
                "الفترة الزمنية",
                options=[7, 30, 90, 365],
                format_func=lambda x: f"آخر {x} أيام",
                index=1
            )
        
        with col2:
            if st.button("تحديث التقرير", type="primary"):
                st.rerun()
        
        # Generate report
        summary = self.report_generator.generate_executive_summary(user_id, date_range)
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("إجمالي العقود", summary["total_contracts"])
        
        with col2:
            st.metric("عقود عالية المخاطر", summary["high_risk_contracts"])
        
        with col3:
            st.metric("متوسط المخاطر", f"{summary['average_risk_score']}/10")
        
        with col4:
            st.metric("التعليقات النشطة", summary["collaboration_stats"]["open_comments"])
        
        # Charts
        col1, col2 = st.columns(2)
        
        with col1:
            # Status distribution pie chart
            if summary["status_distribution"]:
                status_data = summary["status_distribution"]
                fig_status = px.pie(
                    values=list(status_data.values()),
                    names=[self._get_status_name(k) for k in status_data.keys()],
                    title="توزيع العقود حسب الحالة"
                )
                fig_status.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig_status, use_container_width=True)
        
        with col2:
            # Type distribution bar chart
            if summary["type_distribution"]:
                type_data = summary["type_distribution"]
                fig_type = px.bar(
                    x=[self._get_type_name(k) for k in type_data.keys()],
                    y=list(type_data.values()),
                    title="توزيع العقود حسب النوع"
                )
                fig_type.update_layout(xaxis_title="نوع العقد", yaxis_title="العدد")
                st.plotly_chart(fig_type, use_container_width=True)
    
    def display_risk_analysis_dashboard(self, user_id: str = None):
        """Display risk analysis dashboard"""
        st.markdown("### 🎯 تحليل المخاطر المتقدم")
        
        risk_report = self.report_generator.generate_risk_analysis_report(user_id)
        
        # Risk distribution
        col1, col2, col3, col4 = st.columns(4)
        
        risk_dist = risk_report["risk_distribution"]
        
        with col1:
            st.metric("مخاطر منخفضة", risk_dist["low_risk"], delta=None)
        
        with col2:
            st.metric("مخاطر متوسطة", risk_dist["medium_risk"], delta=None)
        
        with col3:
            st.metric("مخاطر عالية", risk_dist["high_risk"], delta="تحتاج انتباه" if risk_dist["high_risk"] > 0 else None)
        
        with col4:
            st.metric("مخاطر حرجة", risk_dist["critical_risk"], delta="عاجل" if risk_dist["critical_risk"] > 0 else None)
        
        # Risk trends chart
        if risk_report["risk_trends"]:
            st.markdown("#### 📈 اتجاهات المخاطر عبر الزمن")
            
            trends_data = risk_report["risk_trends"]
            fig_trends = px.line(
                x=list(trends_data.keys()),
                y=list(trends_data.values()),
                title="متوسط درجة المخاطر الشهرية"
            )
            fig_trends.update_layout(xaxis_title="الشهر", yaxis_title="متوسط درجة المخاطر")
            st.plotly_chart(fig_trends, use_container_width=True)
        
        # Risk by type
        if risk_report["risk_by_type"]:
            st.markdown("#### 📊 المخاطر حسب نوع العقد")
            
            type_risk_data = risk_report["risk_by_type"]
            fig_type_risk = px.bar(
                x=[self._get_type_name(k) for k in type_risk_data.keys()],
                y=list(type_risk_data.values()),
                title="متوسط المخاطر حسب نوع العقد",
                color=list(type_risk_data.values()),
                color_continuous_scale="RdYlGn_r"
            )
            fig_type_risk.update_layout(xaxis_title="نوع العقد", yaxis_title="متوسط درجة المخاطر")
            st.plotly_chart(fig_type_risk, use_container_width=True)
        
        # Recommendations
        st.markdown("#### 💡 التوصيات")
        for recommendation in risk_report["recommendations"]:
            st.markdown(f"• {recommendation}")
    
    def display_productivity_dashboard(self, user_id: str = None):
        """Display productivity dashboard"""
        st.markdown("### ⚡ تحليل الإنتاجية")
        
        # Date range selector
        date_range = st.selectbox(
            "الفترة الزمنية",
            options=[7, 30, 90],
            format_func=lambda x: f"آخر {x} أيام",
            index=1
        )
        
        productivity_report = self.report_generator.generate_productivity_report(user_id, date_range)
        
        # Key metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("العقود المحللة", productivity_report["total_analyzed"])
        
        with col2:
            st.metric("متوسط وقت المعالجة", f"{productivity_report['average_processing_time']} دقيقة")
        
        with col3:
            peak_day = productivity_report["peak_analysis_day"]
            if peak_day:
                st.metric("أعلى يوم إنتاجية", f"{peak_day[1]} عقد", delta=peak_day[0])
        
        # Daily analysis chart
        if productivity_report["daily_analysis"]:
            st.markdown("#### 📅 التحليل اليومي")
            
            daily_data = productivity_report["daily_analysis"]
            fig_daily = px.bar(
                x=list(daily_data.keys()),
                y=list(daily_data.values()),
                title="عدد العقود المحللة يومياً"
            )
            fig_daily.update_layout(xaxis_title="التاريخ", yaxis_title="عدد العقود")
            st.plotly_chart(fig_daily, use_container_width=True)
    
    def display_compliance_dashboard(self, user_id: str = None):
        """Display compliance dashboard"""
        st.markdown("### ⚖️ تحليل الامتثال")
        
        compliance_report = self.report_generator.generate_compliance_report(user_id)
        
        # Key metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("معدل الامتثال", f"{compliance_report['overall_compliance_rate']}%")
        
        with col2:
            st.metric("عقود متوافقة", compliance_report["compliant_contracts"])
        
        with col3:
            st.metric("مشاكل امتثال", compliance_report["compliance_issues"])
        
        # Compliance by type
        st.markdown("#### 📊 الامتثال حسب نوع العقد")
        
        compliance_by_type = compliance_report["compliance_by_type"]
        
        for contract_type, data in compliance_by_type.items():
            if data["total"] > 0:
                col1, col2 = st.columns([3, 1])
                
                with col1:
                    st.markdown(f"**{self._get_type_name(contract_type)}**")
                    progress_value = data["compliance_rate"] / 100
                    st.progress(progress_value)
                
                with col2:
                    st.metric("", f"{data['compliance_rate']}%")
    
    def _get_status_name(self, status: str) -> str:
        """Get Arabic status name"""
        names = {
            "pending": "قيد الانتظار",
            "analyzed": "محلل",
            "reviewed": "مراجع",
            "approved": "موافق عليه",
            "rejected": "مرفوض"
        }
        return names.get(status, status)
    
    def _get_type_name(self, contract_type: str) -> str:
        """Get Arabic contract type name"""
        names = {
            "employment": "عقد عمل",
            "commercial": "عقد تجاري",
            "real_estate": "عقد عقاري",
            "service": "عقد خدمة",
            "partnership": "عقد شراكة",
            "other": "أخرى"
        }
        return names.get(contract_type, contract_type)
