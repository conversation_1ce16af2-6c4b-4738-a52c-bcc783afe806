#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application Verification Script
Verifies that all components are working correctly
"""

import os
import sys
import sqlite3
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def verify_imports():
    """Verify all modules can be imported"""
    print("🔍 Verifying module imports...")
    
    modules = [
        'database_manager',
        'auth_manager', 
        'template_manager',
        'ai_backend',
        'file_processor',
        'export_manager',
        'help_system',
        'monitoring_system',
        'beautiful_ui',
        'enhanced_i18n',
        'theme_manager',
        'legal_frameworks'
    ]
    
    success_count = 0
    
    for module in modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
        except Exception as e:
            print(f"  ⚠️  {module}: {e}")
    
    print(f"\n📊 Import Success Rate: {success_count}/{len(modules)} ({success_count/len(modules)*100:.1f}%)")
    return success_count == len(modules)

def verify_database():
    """Verify database functionality"""
    print("\n🗄️  Verifying database functionality...")
    
    try:
        from database_manager import DatabaseManager
        
        # Create temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        # Initialize database
        db_manager = DatabaseManager(db_path)
        print("  ✅ Database initialization")
        
        # Check tables exist
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        expected_tables = ['users', 'contracts', 'analysis_results', 'templates']
        missing_tables = [t for t in expected_tables if t not in tables]
        
        if not missing_tables:
            print("  ✅ All required tables exist")
        else:
            print(f"  ⚠️  Missing tables: {missing_tables}")
        
        # Clean up
        os.unlink(db_path)
        
        return len(missing_tables) == 0
        
    except Exception as e:
        print(f"  ❌ Database error: {e}")
        return False

def verify_ai_backend():
    """Verify AI backend functionality"""
    print("\n🤖 Verifying AI backend...")
    
    try:
        from ai_backend import ContractAnalyzer
        
        analyzer = ContractAnalyzer()
        print("  ✅ AI backend initialization")
        
        # Test connection (without actual analysis)
        if hasattr(analyzer, 'test_connection'):
            connected = analyzer.test_connection()
            if connected:
                print("  ✅ AI service connection")
            else:
                print("  ⚠️  AI service not available (expected in test environment)")
        else:
            print("  ✅ AI backend structure")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI backend error: {e}")
        return False

def verify_file_processing():
    """Verify file processing functionality"""
    print("\n📄 Verifying file processing...")
    
    try:
        from file_processor import FileProcessor
        
        processor = FileProcessor()
        print("  ✅ File processor initialization")
        
        # Test text processing
        test_text = "This is a test contract document."
        if hasattr(processor, 'extract_text_from_content'):
            result = processor.extract_text_from_content(test_text.encode(), 'txt')
            if result and 'text' in result:
                print("  ✅ Text processing")
            else:
                print("  ⚠️  Text processing returned unexpected result")
        else:
            print("  ✅ File processor structure")
        
        return True
        
    except Exception as e:
        print(f"  ❌ File processing error: {e}")
        return False

def verify_export_functionality():
    """Verify export functionality"""
    print("\n📤 Verifying export functionality...")
    
    try:
        from export_manager import ExportManager
        
        export_manager = ExportManager()
        print("  ✅ Export manager initialization")
        
        # Test export data structure
        test_data = {
            'contract_name': 'Test Contract',
            'analysis_date': '2024-01-01',
            'risk_score': 30,
            'legal_points': ['Point 1'],
            'recommendations': ['Recommendation 1'],
            'summary': 'Test summary'
        }
        
        # Check if export methods exist
        has_pdf_export = hasattr(export_manager, 'export_to_pdf')
        has_word_export = hasattr(export_manager, 'export_to_word')
        
        if has_pdf_export and has_word_export:
            print("  ✅ Export methods available")
        else:
            print("  ⚠️  Some export methods missing")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Export functionality error: {e}")
        return False

def verify_ui_components():
    """Verify UI components"""
    print("\n🎨 Verifying UI components...")
    
    try:
        from beautiful_ui import get_beautiful_ui
        from theme_manager import get_theme_manager
        from enhanced_i18n import get_i18n
        
        # Test UI initialization
        ui = get_beautiful_ui()
        print("  ✅ Beautiful UI")
        
        theme_manager = get_theme_manager()
        print("  ✅ Theme manager")
        
        i18n = get_i18n()
        print("  ✅ Internationalization")
        
        return True
        
    except Exception as e:
        print(f"  ❌ UI components error: {e}")
        return False

def verify_application_structure():
    """Verify main application structure"""
    print("\n🏗️  Verifying application structure...")
    
    required_files = [
        'enhanced_app.py',
        'run_enhanced_app.py',
        'requirements.txt',
        'database_manager.py',
        'auth_manager.py',
        'template_manager.py',
        'ai_backend.py',
        'file_processor.py',
        'export_manager.py',
        'help_system.py',
        'monitoring_system.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing_files.append(file)
    
    if not missing_files:
        print("  ✅ All required files present")
        return True
    else:
        print(f"  ⚠️  Missing files: {missing_files}")
        return False

def run_verification():
    """Run complete verification"""
    print("🔍 Enhanced Legal Contract Analyzer - Application Verification")
    print("=" * 70)
    
    tests = [
        ("Application Structure", verify_application_structure),
        ("Module Imports", verify_imports),
        ("Database Functionality", verify_database),
        ("AI Backend", verify_ai_backend),
        ("File Processing", verify_file_processing),
        ("Export Functionality", verify_export_functionality),
        ("UI Components", verify_ui_components)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        print("✅ Enhanced Legal Contract Analyzer is ready for use!")
        print("\n🚀 To start the application, run:")
        print("   python run_enhanced_app.py")
        print("\n🌐 Then open: http://localhost:8563")
        print("🔑 Login with: admin / admin123")
    else:
        print(f"\n⚠️  {total - passed} verification(s) failed.")
        print("Please check the errors above and fix any issues.")
    
    return passed == total

if __name__ == "__main__":
    success = run_verification()
    sys.exit(0 if success else 1)
