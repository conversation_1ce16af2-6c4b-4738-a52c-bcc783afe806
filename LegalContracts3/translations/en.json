{"app_title": "Kuwaiti Legal Contract Analyzer", "app_subtitle": "Comprehensive platform for contract analysis according to Kuwaiti law", "login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "welcome": "Welcome", "dashboard": "Dashboard", "contracts": "Contracts", "analysis": "Analysis", "reports": "Reports", "settings": "Settings", "help": "Help", "upload_file": "Upload File", "analyze_contract": "Analyze Contract", "risk_assessment": "Risk Assessment", "legal_compliance": "Legal Compliance", "recommendations": "Recommendations", "export": "Export", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "sort": "Sort", "date": "Date", "time": "Time", "user": "User", "admin": "Admin", "status": "Status", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "high_risk": "High Risk", "medium_risk": "Medium Risk", "low_risk": "Low Risk", "contract_types": {"employment": "Employment Contract", "commercial": "Commercial Contract", "real_estate": "Real Estate Contract", "service": "Service Contract", "partnership": "Partnership Agreement", "government": "Government Contract"}, "legal_laws": {"civil_code": "Civil Code 67/1980", "labor_law": "Labor Law 6/2010", "commercial_law": "Commercial Law 68/1980"}, "navigation": {"home": "Home", "analyze": "Analyze", "history": "History", "templates": "Templates", "collaboration": "Collaboration", "reports": "Reports", "risk_analysis": "Risk Analysis", "advanced_analysis": "Advanced Analysis", "mobile_dashboard": "Mobile Dashboard", "api_docs": "API Documentation", "ai_insights": "AI Insights", "monitoring": "System Monitoring", "admin": "User Management", "system_config": "System Configuration", "maintenance": "Maintenance Tools", "system_logs": "System Logs"}, "messages": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loading": "Loading...", "processing": "Processing...", "analyzing": "Analyzing...", "uploading": "Uploading...", "saving": "Saving...", "login_success": "Login successful", "login_failed": "<PERSON><PERSON> failed", "file_uploaded": "File uploaded successfully", "analysis_complete": "Analysis completed successfully", "invalid_file": "Invalid file", "file_too_large": "File too large", "no_text_found": "No text found", "ai_error": "AI processing error", "database_error": "Database error", "network_error": "Network error", "permission_denied": "Permission denied"}, "forms": {"contract_title": "Contract Title", "contract_type": "Contract Type", "contract_text": "Contract Text", "upload_instructions": "Drag file here or click to select", "supported_formats": "Supported formats: TXT, DOC, DOCX, PDF", "max_file_size": "Maximum file size: 50 MB", "required_field": "Required field", "optional_field": "Optional field"}}