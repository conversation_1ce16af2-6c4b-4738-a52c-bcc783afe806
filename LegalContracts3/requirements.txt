# Kuwaiti Legal Contract Analysis Application
# Core dependencies for Streamlit app with AI backend integration

# Core Streamlit framework
streamlit>=1.28.0

# AI and HTTP requests
requests>=2.31.0

# Document processing libraries
python-docx>=0.8.11
PyPDF2>=3.0.1
pdfplumber>=0.9.0

# PDF generation
reportlab>=4.0.4

# Data handling
pandas>=2.0.0

# Date and time handling
python-dateutil>=2.8.2

# Arabic text support and fonts
arabic-reshaper>=3.0.0
python-bidi>=0.4.2

# Image processing (for PDF generation)
Pillow>=10.0.0

# Progress bars and UI enhancements
stqdm>=0.0.5

# File type detection
python-magic>=0.4.27

# Optional: Development and testing
pytest>=7.4.0
black>=23.7.0

# API Server dependencies
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.4.0
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6

# Advanced visualization
plotly>=5.17.0

# Optional: Enhanced features
beautifulsoup4>=4.12.0
lxml>=4.9.0
