
import streamlit as st
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from user_management import UserManager

def main():
    st.title("🔐 Simple Login Test")
    
    # Initialize user manager
    user_manager = UserManager()
    
    # Initialize session state
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    if 'current_user' not in st.session_state:
        st.session_state.current_user = {}
    
    if not st.session_state.authenticated:
        st.markdown("### Login Form")
        
        with st.form("simple_login"):
            username = st.text_input("Username", value="admin")
            password = st.text_input("Password", type="password", value="admin123")
            
            if st.form_submit_button("Login"):
                st.write(f"Attempting login with: {username}")
                
                try:
                    user = user_manager.authenticate_user(username, password)
                    st.write(f"Authentication result: {user}")
                    
                    if user:
                        st.session_state.authenticated = True
                        st.session_state.current_user = user
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Login failed")
                except Exception as e:
                    st.error(f"Login error: {str(e)}")
                    import traceback
                    st.code(traceback.format_exc())
    else:
        st.success("✅ Logged in successfully!")
        st.write(f"Current user: {st.session_state.current_user}")
        
        if st.button("Logout"):
            st.session_state.authenticated = False
            st.session_state.current_user = {}
            st.rerun()

if __name__ == "__main__":
    main()
