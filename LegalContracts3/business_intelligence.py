"""
Business Intelligence and Advanced Analytics
Developed by MAXBIT LLC © 2025
"""

import sqlite3
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import numpy as np
from dataclasses import dataclass
from database import ContractDatabase

@dataclass
class BusinessMetrics:
    """Business metrics data structure"""
    total_contracts: int
    total_users: int
    contracts_this_month: int
    average_risk_score: float
    compliance_rate: float
    user_engagement: float
    revenue_potential: float
    market_penetration: float

@dataclass
class ContractInsight:
    """Contract analysis insight"""
    contract_type: str
    total_count: int
    average_risk: float
    compliance_rate: float
    common_issues: List[str]
    recommendations: List[str]

class BusinessIntelligence:
    """Advanced business intelligence and analytics"""
    
    def __init__(self, db_path: str = "contracts.db"):
        self.db_path = db_path
        self.contract_db = ContractDatabase()
    
    def get_business_metrics(self) -> BusinessMetrics:
        """Calculate comprehensive business metrics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Check if contracts table exists
                table_exists = conn.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='contracts'
                """).fetchone()

                if not table_exists:
                    # Return default metrics if table doesn't exist
                    return self._get_default_metrics()

                # Total contracts
                total_contracts = conn.execute("SELECT COUNT(*) FROM contracts").fetchone()[0]
            
            # Total users
            total_users = conn.execute("SELECT COUNT(*) FROM users").fetchone()[0]
            
            # Contracts this month
            contracts_this_month = conn.execute("""
                SELECT COUNT(*) FROM contracts 
                WHERE created_at >= date('now', 'start of month')
            """).fetchone()[0]
            
            # Average risk score
            avg_risk = conn.execute("""
                SELECT AVG(CAST(json_extract(analysis_result, '$.risk_score') AS REAL))
                FROM contracts 
                WHERE analysis_result IS NOT NULL
            """).fetchone()[0] or 0
            
            # Compliance rate (contracts with risk score < 30)
            compliance_rate = conn.execute("""
                SELECT 
                    COUNT(CASE WHEN CAST(json_extract(analysis_result, '$.risk_score') AS REAL) < 30 THEN 1 END) * 100.0 / COUNT(*)
                FROM contracts 
                WHERE analysis_result IS NOT NULL
            """).fetchone()[0] or 0
            
            # User engagement (active users in last 30 days)
            user_engagement = conn.execute("""
                SELECT COUNT(DISTINCT user_id) * 100.0 / (SELECT COUNT(*) FROM users)
                FROM contracts 
                WHERE created_at >= date('now', '-30 days')
            """).fetchone()[0] or 0

            # Calculate derived metrics
            revenue_potential = self._calculate_revenue_potential(total_contracts, avg_risk)
            market_penetration = self._calculate_market_penetration(total_users)

            return BusinessMetrics(
                total_contracts=total_contracts,
                total_users=total_users,
                contracts_this_month=contracts_this_month,
                average_risk_score=avg_risk,
                compliance_rate=compliance_rate,
                user_engagement=user_engagement,
                revenue_potential=revenue_potential,
                market_penetration=market_penetration
            )
        except Exception as e:
            print(f"Error getting business metrics: {e}")
            return self._get_default_metrics()

    def _get_default_metrics(self) -> BusinessMetrics:
        """Return default metrics when database is not available"""
        return BusinessMetrics(
            total_contracts=0,
            total_users=1,
            contracts_this_month=0,
            average_risk_score=0.0,
            compliance_rate=100.0,
            user_engagement=0.0,
            revenue_potential=0.0,
            market_penetration=0.0
        )
    
    def get_contract_insights(self) -> List[ContractInsight]:
        """Get insights by contract type"""
        insights = []
        
        with sqlite3.connect(self.db_path) as conn:
            # Get contract types
            contract_types = conn.execute("""
                SELECT DISTINCT contract_type FROM contracts 
                WHERE contract_type IS NOT NULL
            """).fetchall()
            
            for (contract_type,) in contract_types:
                # Count and average risk
                stats = conn.execute("""
                    SELECT 
                        COUNT(*) as count,
                        AVG(CAST(json_extract(analysis_result, '$.risk_score') AS REAL)) as avg_risk,
                        COUNT(CASE WHEN CAST(json_extract(analysis_result, '$.risk_score') AS REAL) < 30 THEN 1 END) * 100.0 / COUNT(*) as compliance
                    FROM contracts 
                    WHERE contract_type = ? AND analysis_result IS NOT NULL
                """, (contract_type,)).fetchone()
                
                count, avg_risk, compliance_rate = stats
                avg_risk = avg_risk or 0
                compliance_rate = compliance_rate or 0
                
                # Get common issues
                common_issues = self._get_common_issues(contract_type)
                recommendations = self._get_recommendations(contract_type, avg_risk)
                
                insights.append(ContractInsight(
                    contract_type=contract_type,
                    total_count=count,
                    average_risk=avg_risk,
                    compliance_rate=compliance_rate,
                    common_issues=common_issues,
                    recommendations=recommendations
                ))
        
        return insights
    
    def get_trend_analysis(self, days: int = 30) -> Dict[str, Any]:
        """Get trend analysis for the specified period"""
        with sqlite3.connect(self.db_path) as conn:
            # Daily contract creation
            daily_contracts = pd.read_sql_query("""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as contracts,
                    AVG(CAST(json_extract(analysis_result, '$.risk_score') AS REAL)) as avg_risk
                FROM contracts 
                WHERE created_at >= date('now', '-{} days')
                GROUP BY DATE(created_at)
                ORDER BY date
            """.format(days), conn)
            
            # Contract type distribution
            type_distribution = pd.read_sql_query("""
                SELECT 
                    contract_type,
                    COUNT(*) as count
                FROM contracts 
                WHERE created_at >= date('now', '-{} days')
                AND contract_type IS NOT NULL
                GROUP BY contract_type
                ORDER BY count DESC
            """.format(days), conn)
            
            # Risk score distribution
            risk_distribution = pd.read_sql_query("""
                SELECT 
                    CASE 
                        WHEN CAST(json_extract(analysis_result, '$.risk_score') AS REAL) < 20 THEN 'Low (0-20)'
                        WHEN CAST(json_extract(analysis_result, '$.risk_score') AS REAL) < 40 THEN 'Medium (20-40)'
                        WHEN CAST(json_extract(analysis_result, '$.risk_score') AS REAL) < 60 THEN 'High (40-60)'
                        ELSE 'Critical (60+)'
                    END as risk_category,
                    COUNT(*) as count
                FROM contracts 
                WHERE created_at >= date('now', '-{} days')
                AND analysis_result IS NOT NULL
                GROUP BY risk_category
            """.format(days), conn)
            
            # User activity
            user_activity = pd.read_sql_query("""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(DISTINCT user_id) as active_users
                FROM contracts 
                WHERE created_at >= date('now', '-{} days')
                GROUP BY DATE(created_at)
                ORDER BY date
            """.format(days), conn)
        
        return {
            "daily_contracts": daily_contracts,
            "type_distribution": type_distribution,
            "risk_distribution": risk_distribution,
            "user_activity": user_activity
        }
    
    def get_predictive_analytics(self) -> Dict[str, Any]:
        """Generate predictive analytics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Check if contracts table exists
                table_exists = conn.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name='contracts'
                """).fetchone()

                if not table_exists:
                    return self._get_default_predictions()

                # Get historical data for prediction
                historical_data = pd.read_sql_query("""
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as contracts,
                        AVG(CAST(json_extract(analysis_result, '$.risk_score') AS REAL)) as avg_risk
                    FROM contracts
                    WHERE created_at >= date('now', '-90 days')
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """, conn)

                if len(historical_data) < 7:
                    return {"error": "Insufficient data for predictions"}

                # Simple trend analysis
                recent_avg = historical_data.tail(7)['contracts'].mean()
                previous_avg = historical_data.head(7)['contracts'].mean()
                growth_rate = (recent_avg - previous_avg) / previous_avg if previous_avg > 0 else 0

                # Predict next 30 days
                predicted_contracts = recent_avg * (1 + growth_rate) * 30

                # Risk trend
                risk_trend = historical_data['avg_risk'].rolling(window=7).mean().iloc[-1]
        
            return {
                "predicted_monthly_contracts": int(predicted_contracts),
                "growth_rate": growth_rate * 100,
                "risk_trend": risk_trend,
                "confidence": min(90, len(historical_data) * 2)  # Confidence based on data points
            }
        except Exception as e:
            print(f"Error getting predictive analytics: {e}")
            return self._get_default_predictions()

    def _get_default_predictions(self) -> Dict[str, Any]:
        """Return default predictions when database is not available"""
        return {
            "predicted_monthly_contracts": 0,
            "growth_rate": 0.0,
            "risk_trend": 0.0,
            "confidence": 0
        }
    
    def get_roi_analysis(self) -> Dict[str, Any]:
        """Calculate return on investment metrics"""
        metrics = self.get_business_metrics()
        
        # Estimated cost savings per contract (based on reduced legal review time)
        cost_savings_per_contract = 500  # KWD
        
        # Platform costs (estimated)
        monthly_platform_cost = 2000  # KWD
        
        # Calculate ROI
        monthly_savings = metrics.contracts_this_month * cost_savings_per_contract
        monthly_roi = ((monthly_savings - monthly_platform_cost) / monthly_platform_cost) * 100
        
        # Risk reduction value
        risk_reduction_value = (100 - metrics.average_risk_score) * metrics.total_contracts * 10
        
        # Compliance value
        compliance_value = metrics.compliance_rate * metrics.total_contracts * 20
        
        return {
            "monthly_savings": monthly_savings,
            "monthly_roi": monthly_roi,
            "risk_reduction_value": risk_reduction_value,
            "compliance_value": compliance_value,
            "total_value": monthly_savings + risk_reduction_value + compliance_value
        }
    
    def _calculate_revenue_potential(self, total_contracts: int, avg_risk: float) -> float:
        """Calculate revenue potential based on contract volume and risk reduction"""
        # Base revenue potential per contract
        base_value = 100  # KWD per contract
        
        # Risk reduction multiplier
        risk_multiplier = max(0.5, (100 - avg_risk) / 100)
        
        return total_contracts * base_value * risk_multiplier
    
    def _calculate_market_penetration(self, total_users: int) -> float:
        """Calculate market penetration percentage"""
        # Estimated total addressable market in Kuwait
        total_market = 5000  # Legal professionals and businesses
        
        return min(100, (total_users / total_market) * 100)
    
    def _get_common_issues(self, contract_type: str) -> List[str]:
        """Get common issues for contract type"""
        # This would analyze actual contract analysis results
        # For now, return predefined common issues
        common_issues_map = {
            "employment": [
                "غموض في شروط الإنهاء",
                "عدم وضوح الراتب والمزايا",
                "نقص في بنود السرية"
            ],
            "commercial": [
                "شروط دفع غير واضحة",
                "نقص في بنود فسخ العقد",
                "عدم تحديد المسؤوليات"
            ],
            "real_estate": [
                "عدم وضوح حقوق الملكية",
                "نقص في شروط التسليم",
                "غموض في المسؤوليات"
            ]
        }
        
        return common_issues_map.get(contract_type, ["لا توجد مشاكل شائعة محددة"])
    
    def _get_recommendations(self, contract_type: str, avg_risk: float) -> List[str]:
        """Get recommendations for contract type"""
        recommendations = []
        
        if avg_risk > 60:
            recommendations.append("مراجعة شاملة للعقود عالية المخاطر")
            recommendations.append("تحسين بنود الحماية القانونية")
        elif avg_risk > 40:
            recommendations.append("تعزيز الشروط والأحكام")
            recommendations.append("إضافة بنود حماية إضافية")
        else:
            recommendations.append("الحفاظ على المستوى الحالي")
            recommendations.append("مراجعة دورية للتحديثات")
        
        # Type-specific recommendations
        if contract_type == "employment":
            recommendations.append("تحديث بنود العمل وفقاً لقانون العمل الكويتي")
        elif contract_type == "commercial":
            recommendations.append("تعزيز بنود الدفع والتسليم")
        elif contract_type == "real_estate":
            recommendations.append("التأكد من صحة المستندات العقارية")
        
        return recommendations

class BIDashboard:
    """Business Intelligence Dashboard"""
    
    def __init__(self):
        self.bi = BusinessIntelligence()
    
    def display_executive_dashboard(self):
        """Display executive dashboard"""
        st.markdown("### 📊 لوحة المعلومات التنفيذية")
        
        # Get business metrics
        metrics = self.bi.get_business_metrics()
        
        # Key metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "إجمالي العقود",
                f"{metrics.total_contracts:,}",
                delta=f"+{metrics.contracts_this_month} هذا الشهر"
            )
        
        with col2:
            st.metric(
                "معدل المخاطر",
                f"{metrics.average_risk_score:.1f}%",
                delta=f"{metrics.compliance_rate:.1f}% امتثال"
            )
        
        with col3:
            st.metric(
                "المستخدمون النشطون",
                f"{metrics.total_users:,}",
                delta=f"{metrics.user_engagement:.1f}% نشاط"
            )
        
        with col4:
            st.metric(
                "القيمة المحتملة",
                f"{metrics.revenue_potential:,.0f} د.ك",
                delta=f"{metrics.market_penetration:.1f}% اختراق السوق"
            )
        
        # Charts
        col1, col2 = st.columns(2)
        
        with col1:
            self._display_trend_chart()
        
        with col2:
            self._display_risk_distribution()
        
        # Contract insights
        st.markdown("#### 🔍 تحليل العقود حسب النوع")
        insights = self.bi.get_contract_insights()
        
        for insight in insights:
            with st.expander(f"{insight.contract_type} ({insight.total_count} عقد)"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.metric("متوسط المخاطر", f"{insight.average_risk:.1f}%")
                    st.metric("معدل الامتثال", f"{insight.compliance_rate:.1f}%")
                
                with col2:
                    st.markdown("**المشاكل الشائعة:**")
                    for issue in insight.common_issues:
                        st.markdown(f"• {issue}")
                    
                    st.markdown("**التوصيات:**")
                    for rec in insight.recommendations:
                        st.markdown(f"• {rec}")
    
    def display_predictive_analytics(self):
        """Display predictive analytics"""
        st.markdown("### 🔮 التحليل التنبؤي")
        
        predictions = self.bi.get_predictive_analytics()
        
        if "error" in predictions:
            st.warning(predictions["error"])
            return
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                "العقود المتوقعة (شهرياً)",
                f"{predictions['predicted_monthly_contracts']:,}",
                delta=f"{predictions['growth_rate']:+.1f}% نمو"
            )
        
        with col2:
            st.metric(
                "اتجاه المخاطر",
                f"{predictions['risk_trend']:.1f}%",
                delta="مستقر" if abs(predictions['growth_rate']) < 5 else "متغير"
            )
        
        with col3:
            st.metric(
                "مستوى الثقة",
                f"{predictions['confidence']:.0f}%",
                delta="عالي" if predictions['confidence'] > 80 else "متوسط"
            )
        
        # Prediction chart
        self._display_prediction_chart(predictions)
    
    def display_roi_analysis(self):
        """Display ROI analysis"""
        st.markdown("### 💰 تحليل العائد على الاستثمار")
        
        roi_data = self.bi.get_roi_analysis()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric(
                "الوفورات الشهرية",
                f"{roi_data['monthly_savings']:,.0f} د.ك"
            )
            st.metric(
                "العائد على الاستثمار",
                f"{roi_data['monthly_roi']:+.1f}%"
            )
        
        with col2:
            st.metric(
                "قيمة تقليل المخاطر",
                f"{roi_data['risk_reduction_value']:,.0f} د.ك"
            )
            st.metric(
                "قيمة الامتثال",
                f"{roi_data['compliance_value']:,.0f} د.ك"
            )
        
        # ROI breakdown chart
        self._display_roi_chart(roi_data)
    
    def _display_trend_chart(self):
        """Display trend analysis chart"""
        trend_data = self.bi.get_trend_analysis(30)
        
        if not trend_data['daily_contracts'].empty:
            fig = px.line(
                trend_data['daily_contracts'],
                x='date',
                y='contracts',
                title='اتجاه إنشاء العقود (30 يوم)',
                labels={'date': 'التاريخ', 'contracts': 'عدد العقود'}
            )
            fig.update_layout(
                font=dict(family="Arial", size=12),
                title_font_size=14
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def _display_risk_distribution(self):
        """Display risk distribution chart"""
        trend_data = self.bi.get_trend_analysis(30)
        
        if not trend_data['risk_distribution'].empty:
            fig = px.pie(
                trend_data['risk_distribution'],
                values='count',
                names='risk_category',
                title='توزيع مستويات المخاطر'
            )
            fig.update_layout(
                font=dict(family="Arial", size=12),
                title_font_size=14
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def _display_prediction_chart(self, predictions):
        """Display prediction chart"""
        # Create sample prediction data
        dates = pd.date_range(start=datetime.now(), periods=30, freq='D')
        predicted_values = np.random.poisson(
            predictions['predicted_monthly_contracts'] / 30, 30
        )
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(
            x=dates,
            y=predicted_values,
            mode='lines+markers',
            name='العقود المتوقعة',
            line=dict(color='blue', dash='dash')
        ))
        
        fig.update_layout(
            title='توقعات العقود للشهر القادم',
            xaxis_title='التاريخ',
            yaxis_title='عدد العقود',
            font=dict(family="Arial", size=12)
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    def _display_roi_chart(self, roi_data):
        """Display ROI breakdown chart"""
        categories = ['الوفورات الشهرية', 'قيمة تقليل المخاطر', 'قيمة الامتثال']
        values = [
            roi_data['monthly_savings'],
            roi_data['risk_reduction_value'],
            roi_data['compliance_value']
        ]
        
        fig = px.bar(
            x=categories,
            y=values,
            title='تفصيل القيمة المضافة',
            labels={'x': 'الفئة', 'y': 'القيمة (د.ك)'}
        )
        fig.update_layout(
            font=dict(family="Arial", size=12),
            title_font_size=14
        )
        st.plotly_chart(fig, use_container_width=True)
