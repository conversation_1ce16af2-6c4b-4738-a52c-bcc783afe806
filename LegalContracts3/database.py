"""
Contract Database Management System
Developed by MAXBIT LLC © 2025
"""

import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st

class ContractStatus:
    """Contract status definitions"""
    PENDING = "pending"
    ANALYZED = "analyzed"
    REVIEWED = "reviewed"
    APPROVED = "approved"
    REJECTED = "rejected"

class ContractType:
    """Contract type definitions"""
    EMPLOYMENT = "employment"
    COMMERCIAL = "commercial"
    REAL_ESTATE = "real_estate"
    SERVICE = "service"
    PARTNERSHIP = "partnership"
    OTHER = "other"

class ContractDatabase:
    """Manage contract storage and retrieval"""

    def __init__(self, db_file: str = "contracts.db"):
        self.db_file = db_file
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """Ensure SQLite database exists with required tables"""
        if not os.path.exists(self.db_file):
            # Run database initialization
            from init_database import create_sqlite_database, create_default_admin_user, add_sample_data
            create_sqlite_database(self.db_file)
            create_default_admin_user(self.db_file)
            add_sample_data(self.db_file)
    
    def save_contract(self, filename: str, original_text: str, analysis: Dict[str, Any],
                     user_id: str, contract_type: str = ContractType.OTHER) -> str:
        """Save contract and analysis to database"""
        import sqlite3

        contract_id = str(uuid.uuid4())
        risk_score = self._calculate_risk_score(analysis)

        with sqlite3.connect(self.db_file) as conn:
            conn.execute("""
                INSERT INTO contracts (
                    id, filename, original_text, analysis_result,
                    contract_type, status, user_id, created_at,
                    updated_at, risk_score, tags, notes, version
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                contract_id,
                filename,
                original_text,
                json.dumps(analysis),
                contract_type,
                ContractStatus.ANALYZED,
                user_id,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                risk_score,
                json.dumps([]),
                "",
                1
            ))
            conn.commit()

        return contract_id
    
    def _calculate_risk_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate risk score based on analysis"""
        try:
            legal_points = analysis.get('legal_points', [])
            if not legal_points:
                return 0.0
            
            high_priority = len([p for p in legal_points if p.get('priority') == 'high'])
            medium_priority = len([p for p in legal_points if p.get('priority') == 'medium'])
            total_points = len(legal_points)
            
            if total_points == 0:
                return 0.0
            
            # Calculate weighted risk score (0-10)
            risk_score = ((high_priority * 3) + (medium_priority * 1.5)) / total_points
            return min(risk_score, 10.0)
        except:
            return 0.0
    
    def get_contract(self, contract_id: str) -> Optional[Dict[str, Any]]:
        """Get contract by ID"""
        return self.contracts.get(contract_id)
    
    def update_contract(self, contract_id: str, updates: Dict[str, Any]) -> bool:
        """Update contract data"""
        if contract_id in self.contracts:
            self.contracts[contract_id].update(updates)
            self.contracts[contract_id]["updated_at"] = datetime.now().isoformat()
            self._save_contracts()
            return True
        return False
    
    def delete_contract(self, contract_id: str) -> bool:
        """Delete contract"""
        if contract_id in self.contracts:
            del self.contracts[contract_id]
            self._save_contracts()
            return True
        return False
    
    def list_contracts(self, user_id: str = None, contract_type: str = None,
                      status: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """List contracts with filters"""
        import sqlite3

        try:
            conn = sqlite3.connect(self.db_file)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Build query with filters
            query = "SELECT * FROM contracts WHERE 1=1"
            params = []

            if user_id:
                query += " AND created_by = ?"
                params.append(user_id)

            if contract_type:
                query += " AND contract_type = ?"
                params.append(contract_type)

            if status:
                query += " AND status = ?"
                params.append(status)

            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            contracts = []
            for row in rows:
                contract = dict(row)
                # Parse JSON fields
                if contract.get('analysis'):
                    try:
                        contract['analysis'] = json.loads(contract['analysis'])
                    except:
                        contract['analysis'] = {}
                contracts.append(contract)

            conn.close()
            return contracts

        except Exception as e:
            print(f"Error listing contracts: {e}")
            return []
    
    def search_contracts(self, query: str, user_id: str = None) -> List[Dict[str, Any]]:
        """Search contracts by filename or content"""
        import sqlite3

        try:
            conn = sqlite3.connect(self.db_file)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Build search query
            search_query = """
                SELECT * FROM contracts
                WHERE (filename LIKE ? OR original_text LIKE ?)
            """
            params = [f"%{query}%", f"%{query}%"]

            if user_id:
                search_query += " AND created_by = ?"
                params.append(user_id)

            search_query += " ORDER BY created_at DESC"

            cursor.execute(search_query, params)
            rows = cursor.fetchall()

            results = []
            for row in rows:
                contract = dict(row)
                # Parse JSON fields
                if contract.get('analysis'):
                    try:
                        contract['analysis'] = json.loads(contract['analysis'])
                    except:
                        contract['analysis'] = {}
                results.append(contract)

            conn.close()
            return results

        except Exception as e:
            print(f"Error searching contracts: {e}")
            return []
    
    def get_statistics(self, user_id: str = None) -> Dict[str, Any]:
        """Get database statistics"""
        import sqlite3

        try:
            conn = sqlite3.connect(self.db_file)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Base query
            base_query = "SELECT * FROM contracts"
            params = []

            if user_id:
                base_query += " WHERE created_by = ?"
                params.append(user_id)

            cursor.execute(base_query, params)
            rows = cursor.fetchall()

            contracts = []
            for row in rows:
                contract = dict(row)
                # Parse JSON fields
                if contract.get('analysis'):
                    try:
                        contract['analysis'] = json.loads(contract['analysis'])
                    except:
                        contract['analysis'] = {}
                contracts.append(contract)

            total_contracts = len(contracts)

            # Count by status
            status_counts = {}
            for status in [ContractStatus.PENDING, ContractStatus.ANALYZED,
                          ContractStatus.REVIEWED, ContractStatus.APPROVED, ContractStatus.REJECTED]:
                status_counts[status] = len([c for c in contracts if c.get("status") == status])

            # Count by type
            type_counts = {}
            for contract_type in [ContractType.EMPLOYMENT, ContractType.COMMERCIAL,
                                 ContractType.REAL_ESTATE, ContractType.SERVICE,
                                 ContractType.PARTNERSHIP, ContractType.OTHER]:
                type_counts[contract_type] = len([c for c in contracts if c.get("contract_type") == contract_type])

            # Calculate average risk score
            risk_scores = [c.get("risk_score", 0) for c in contracts if c.get("risk_score")]
            avg_risk_score = sum(risk_scores) / len(risk_scores) if risk_scores else 0

            conn.close()

            return {
                "total_contracts": total_contracts,
                "status_counts": status_counts,
                "type_counts": type_counts,
                "average_risk_score": round(avg_risk_score, 2),
                "high_risk_contracts": len([c for c in contracts if c.get("risk_score", 0) > 7])
            }

        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {
                "total_contracts": 0,
                "status_counts": {},
                "type_counts": {},
                "average_risk_score": 0,
                "high_risk_contracts": 0
            }

class ContractUI:
    """Contract database UI components"""
    
    def __init__(self, db: ContractDatabase):
        self.db = db
    
    def display_contract_list(self, user_id: str = None):
        """Display list of contracts"""
        st.markdown("### 📋 قاعدة بيانات العقود")
        
        # Filters
        col1, col2, col3 = st.columns(3)
        
        with col1:
            contract_type = st.selectbox(
                "نوع العقد",
                options=["الكل"] + [ContractType.EMPLOYMENT, ContractType.COMMERCIAL, 
                        ContractType.REAL_ESTATE, ContractType.SERVICE, 
                        ContractType.PARTNERSHIP, ContractType.OTHER],
                format_func=self._get_type_name
            )
        
        with col2:
            status = st.selectbox(
                "الحالة",
                options=["الكل"] + [ContractStatus.PENDING, ContractStatus.ANALYZED, 
                        ContractStatus.REVIEWED, ContractStatus.APPROVED, ContractStatus.REJECTED],
                format_func=self._get_status_name
            )
        
        with col3:
            search_query = st.text_input("البحث", placeholder="ابحث في أسماء الملفات...")
        
        # Get contracts
        if search_query:
            contracts = self.db.search_contracts(search_query, user_id)
        else:
            filter_type = None if contract_type == "الكل" else contract_type
            filter_status = None if status == "الكل" else status
            contracts = self.db.list_contracts(user_id, filter_type, filter_status)
        
        # Display contracts
        if contracts:
            for contract in contracts:
                self._display_contract_card(contract)
        else:
            st.info("لا توجد عقود مطابقة للمعايير المحددة")
    
    def _display_contract_card(self, contract: Dict[str, Any]):
        """Display individual contract card"""
        with st.expander(f"📄 {contract['filename']} - {self._get_status_name(contract['status'])}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**النوع:** {self._get_type_name(contract['contract_type'])}")
                st.markdown(f"**تاريخ الإنشاء:** {self._format_date(contract['created_at'])}")
                st.markdown(f"**درجة المخاطر:** {contract.get('risk_score', 0):.1f}/10")
                
                if contract.get('notes'):
                    st.markdown(f"**ملاحظات:** {contract['notes']}")
            
            with col2:
                if st.button(f"عرض التحليل", key=f"view_{contract['id']}"):
                    st.session_state.selected_contract = contract['id']
                
                if st.button(f"تحديث", key=f"edit_{contract['id']}"):
                    st.session_state.edit_contract = contract['id']
    
    def display_statistics(self, user_id: str = None):
        """Display database statistics"""
        stats = self.db.get_statistics(user_id)
        
        st.markdown("### 📊 إحصائيات قاعدة البيانات")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("إجمالي العقود", stats["total_contracts"])
        
        with col2:
            st.metric("متوسط المخاطر", f"{stats['average_risk_score']}/10")
        
        with col3:
            st.metric("عقود عالية المخاطر", stats["high_risk_contracts"])
        
        with col4:
            analyzed_count = stats["status_counts"].get(ContractStatus.ANALYZED, 0)
            st.metric("عقود محللة", analyzed_count)
    
    def _get_type_name(self, contract_type: str) -> str:
        """Get Arabic contract type name"""
        type_names = {
            "الكل": "الكل",
            ContractType.EMPLOYMENT: "عقد عمل",
            ContractType.COMMERCIAL: "عقد تجاري",
            ContractType.REAL_ESTATE: "عقد عقاري",
            ContractType.SERVICE: "عقد خدمة",
            ContractType.PARTNERSHIP: "عقد شراكة",
            ContractType.OTHER: "أخرى"
        }
        return type_names.get(contract_type, contract_type)
    
    def _get_status_name(self, status: str) -> str:
        """Get Arabic status name"""
        status_names = {
            "الكل": "الكل",
            ContractStatus.PENDING: "قيد الانتظار",
            ContractStatus.ANALYZED: "محلل",
            ContractStatus.REVIEWED: "مراجع",
            ContractStatus.APPROVED: "موافق عليه",
            ContractStatus.REJECTED: "مرفوض"
        }
        return status_names.get(status, status)
    
    def _format_date(self, date_str: str) -> str:
        """Format date for display"""
        try:
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M')
        except:
            return date_str
