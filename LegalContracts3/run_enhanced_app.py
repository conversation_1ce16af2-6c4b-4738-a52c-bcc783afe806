#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Legal Contract Analyzer - Startup Script
Beautiful, multi-language, multi-legal system application
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'streamlit',
        'pandas',
        'numpy',
        'requests',
        'sqlite3',
        'pathlib',
        'datetime',
        'json',
        'tempfile'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install <package_name>")
        return False
    
    print("✅ All dependencies are installed")
    return True

def setup_environment():
    """Setup environment variables and configurations"""
    # Set environment variables
    os.environ['STREAMLIT_THEME_BASE'] = 'light'
    os.environ['STREAMLIT_THEME_PRIMARY_COLOR'] = '#1f77b4'
    os.environ['STREAMLIT_THEME_BACKGROUND_COLOR'] = '#ffffff'
    os.environ['STREAMLIT_THEME_SECONDARY_BACKGROUND_COLOR'] = '#f0f2f6'
    os.environ['STREAMLIT_THEME_TEXT_COLOR'] = '#262730'
    
    print("✅ Environment configured")

def create_config_file():
    """Create Streamlit configuration file"""
    config_dir = Path.home() / '.streamlit'
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / 'config.toml'
    
    config_content = """
[server]
port = 8563
headless = true
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
base = "light"
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[logger]
level = "info"
"""
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Streamlit configuration created")

def check_ai_backends():
    """Check AI backend availability"""
    import requests
    
    backends_status = {}
    
    # Check LM Studio
    try:
        response = requests.get("http://localhost:1234/v1/models", timeout=5)
        if response.status_code == 200:
            backends_status['lmstudio'] = "🟢 Connected"
        else:
            backends_status['lmstudio'] = "🔴 Not responding"
    except:
        backends_status['lmstudio'] = "🔴 Not available"
    
    # Check Ollama
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            backends_status['ollama'] = "🟢 Connected"
        else:
            backends_status['ollama'] = "🔴 Not responding"
    except:
        backends_status['ollama'] = "🔴 Not available"
    
    print("\n🤖 AI Backend Status:")
    for backend, status in backends_status.items():
        print(f"   {backend.upper()}: {status}")
    
    return backends_status

def display_startup_info():
    """Display startup information"""
    print("\n" + "="*60)
    print("⚖️  ENHANCED LEGAL CONTRACT ANALYZER")
    print("    Beautiful Multi-Language Legal Analysis Platform")
    print("    Powered by MAXBIT LLC")
    print("="*60)
    print("\n🚀 Starting Enhanced Application...")
    print("\n📋 Features:")
    print("   ✅ Multi-language support (Arabic/English)")
    print("   ✅ Multi-legal system (Kuwait/Saudi Arabia)")
    print("   ✅ 10 beautiful themes")
    print("   ✅ Advanced AI analysis")
    print("   ✅ Real-time legal framework switching")
    print("   ✅ Beautiful modern UI/UX")
    print("   ✅ Comprehensive contract analysis")
    print("   ✅ Risk assessment and recommendations")

def run_application():
    """Run the enhanced Streamlit application"""
    try:
        # Get the directory of this script
        script_dir = Path(__file__).parent
        app_file = script_dir / "enhanced_app.py"
        
        if not app_file.exists():
            print(f"❌ Application file not found: {app_file}")
            return False
        
        print(f"\n🌐 Starting application on http://localhost:8563")
        print("📱 Login credentials: admin / admin123")
        print("\n⏳ Please wait while the application loads...")
        
        # Run Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            str(app_file),
            "--server.port", "8563",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        subprocess.run(cmd, cwd=script_dir)
        
    except KeyboardInterrupt:
        print("\n\n👋 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Error running application: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    print("🔧 Checking system requirements...")
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies and try again")
        return
    
    # Setup environment
    setup_environment()
    
    # Create config
    create_config_file()
    
    # Check AI backends
    backends = check_ai_backends()
    
    # Display startup info
    display_startup_info()
    
    # Warning about AI backends
    if all("🔴" in status for status in backends.values()):
        print("\n⚠️  WARNING: No AI backends are available!")
        print("   Please start LM Studio or Ollama for full functionality")
        print("   The application will still work with limited features")
        
        response = input("\n❓ Continue anyway? (y/n): ")
        if response.lower() != 'y':
            print("👋 Startup cancelled")
            return
    
    print("\n" + "="*60)
    
    # Run the application
    success = run_application()
    
    if success:
        print("\n✅ Application completed successfully")
    else:
        print("\n❌ Application encountered an error")

if __name__ == "__main__":
    main()
