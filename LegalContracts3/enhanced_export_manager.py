#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Export Manager for Contract Analysis Results
Handles comprehensive PDF and Word export with all analysis sections
"""

import io
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedExportManager:
    """Enhanced export manager with comprehensive analysis sections"""
    
    def __init__(self):
        """Initialize enhanced export manager"""
        self.supported_formats = ['pdf', 'docx']
        self.company_name = "MAXBIT LLC"
        self.app_name = "محلل العقود القانونية المتقدم"
        self.arabic_font_registered = False
        self.arabic_font_name = 'Helvetica'

    def _register_arabic_font(self):
        """Comprehensive Arabic font registration system"""
        if self.arabic_font_registered:
            return self.arabic_font_name

        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import os
            import requests
            import tempfile

            # Step 1: Try system fonts first
            system_fonts = [
                # macOS
                '/System/Library/Fonts/Arial Unicode MS.ttf',
                '/Library/Fonts/Arial Unicode MS.ttf',
                '/System/Library/Fonts/Helvetica.ttc',
                '/System/Library/Fonts/Times.ttc',
                # Windows
                'C:\\Windows\\Fonts\\arial.ttf',
                'C:\\Windows\\Fonts\\times.ttf',
                'C:\\Windows\\Fonts\\calibri.ttf',
                # Linux
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                '/usr/share/fonts/TTF/arial.ttf',
                '/usr/share/fonts/opentype/noto/NotoSansArabic-Regular.ttf',
            ]

            for font_path in system_fonts:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ArabicFont', font_path))
                        self.arabic_font_registered = True
                        self.arabic_font_name = 'ArabicFont'
                        logger.info(f"✅ Registered system Arabic font: {font_path}")
                        return self.arabic_font_name
                    except Exception as e:
                        logger.warning(f"Failed to register system font {font_path}: {e}")
                        continue

            # Step 2: Download and register Noto Sans Arabic if no system font found
            logger.info("No system Arabic font found, attempting to download Noto Sans Arabic...")

            try:
                # Download Noto Sans Arabic from Google Fonts
                font_url = "https://fonts.gstatic.com/s/notosansarabic/v18/nwpxtLGrOAZMl5nJ_wfgRg3DrWFZWsnVBJ_sS6tlqHHFlhQ5l3sQWIHPqzCfyGyvu3CBFQLaig.ttf"

                with tempfile.NamedTemporaryFile(delete=False, suffix='.ttf') as temp_font:
                    response = requests.get(font_url, timeout=30)
                    response.raise_for_status()
                    temp_font.write(response.content)
                    temp_font_path = temp_font.name

                # Register the downloaded font
                pdfmetrics.registerFont(TTFont('ArabicFont', temp_font_path))
                self.arabic_font_registered = True
                self.arabic_font_name = 'ArabicFont'
                logger.info("✅ Successfully downloaded and registered Noto Sans Arabic font")
                return self.arabic_font_name

            except Exception as download_error:
                logger.warning(f"Failed to download Arabic font: {download_error}")

            # Step 3: Try alternative download sources
            alternative_fonts = [
                "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansArabic/NotoSansArabic-Regular.ttf",
                "https://fonts.gstatic.com/s/amiri/v27/J7aRnpd8CGxBHqUpvrIw74NL.ttf"
            ]

            for font_url in alternative_fonts:
                try:
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.ttf') as temp_font:
                        response = requests.get(font_url, timeout=20)
                        response.raise_for_status()
                        temp_font.write(response.content)
                        temp_font_path = temp_font.name

                    pdfmetrics.registerFont(TTFont('ArabicFont', temp_font_path))
                    self.arabic_font_registered = True
                    self.arabic_font_name = 'ArabicFont'
                    logger.info(f"✅ Successfully downloaded alternative Arabic font from {font_url}")
                    return self.arabic_font_name

                except Exception as alt_error:
                    logger.warning(f"Failed to download from {font_url}: {alt_error}")
                    continue

            # Step 4: Final fallback - use Helvetica with warning
            logger.warning("⚠️ Could not register any Arabic font, using Helvetica fallback")
            self.arabic_font_name = 'Helvetica'
            return self.arabic_font_name

        except Exception as e:
            logger.error(f"❌ Arabic font registration failed: {e}")
            self.arabic_font_name = 'Helvetica'
            return self.arabic_font_name
    
    def export_comprehensive_analysis(self, analysis_data: Dict[str, Any], format_type: str = 'pdf') -> bytes:
        """Export comprehensive analysis results to specified format"""
        try:
            if format_type.lower() == 'pdf':
                return self._export_comprehensive_pdf(analysis_data)
            elif format_type.lower() == 'docx':
                return self._export_comprehensive_docx(analysis_data)
            else:
                raise ValueError(f"Unsupported export format: {format_type}")
                
        except Exception as e:
            logger.error(f"Enhanced export error: {e}")
            raise
    
    def _export_comprehensive_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Export comprehensive analysis to PDF format with all 10 required sections"""
        try:
            # Always use ReportLab with improved Arabic handling
            return self._export_reportlab_pdf(analysis_data)
        except Exception as e:
            logger.error(f"PDF export failed: {e}")
            # Create a simple fallback PDF with basic text
            return self._create_fallback_pdf(analysis_data)

    def _export_html_to_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Export using HTML-to-PDF for better Arabic support"""
        try:
            import weasyprint
            from weasyprint import HTML, CSS

            # Generate HTML content with proper Arabic support
            html_content = self._generate_arabic_html_report(analysis_data)

            # Create PDF from HTML
            html_doc = HTML(string=html_content)
            pdf_bytes = html_doc.write_pdf()

            return pdf_bytes

        except ImportError:
            logger.warning("WeasyPrint not available, falling back to ReportLab")
            raise Exception("WeasyPrint not available")
        except Exception as e:
            logger.error(f"HTML-to-PDF conversion failed: {e}")
            raise

    def _generate_arabic_html_report(self, analysis_data: Dict[str, Any]) -> str:
        """Generate HTML report with proper Arabic support"""

        # Get data
        contract_info = analysis_data.get('contract_info', {})
        translation = analysis_data.get('translation', {})
        legal_points = analysis_data.get('legal_points', [])
        recommendations = analysis_data.get('recommendations', [])
        risk_score = analysis_data.get('risk_score', 0)

        html_content = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير تحليل العقد الشامل</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

                body {{
                    font-family: 'Noto Sans Arabic', Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    line-height: 1.6;
                    margin: 20px;
                    color: #333;
                }}

                .header {{
                    text-align: center;
                    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 10px;
                    margin-bottom: 30px;
                }}

                .title {{
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}

                .subtitle {{
                    font-size: 16px;
                    opacity: 0.9;
                }}

                .section {{
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }}

                .section-title {{
                    font-size: 20px;
                    font-weight: bold;
                    color: #1e3c72;
                    border-bottom: 2px solid #1e3c72;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }}

                .content {{
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    border-right: 4px solid #1e3c72;
                }}

                .table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }}

                .table th, .table td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: right;
                }}

                .table th {{
                    background-color: #1e3c72;
                    color: white;
                    font-weight: bold;
                }}

                .table tr:nth-child(even) {{
                    background-color: #f2f2f2;
                }}

                .risk-score {{
                    font-size: 24px;
                    font-weight: bold;
                    color: {'#d32f2f' if risk_score > 70 else '#f57c00' if risk_score > 40 else '#388e3c'};
                }}

                .legal-point {{
                    background: white;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px 0;
                }}

                .recommendation {{
                    background: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px 0;
                }}

                @page {{
                    margin: 2cm;
                    @bottom-center {{
                        content: "صفحة " counter(page) " من " counter(pages);
                        font-family: 'Noto Sans Arabic', Arial, sans-serif;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">تقرير تحليل العقد الشامل</div>
                <div class="subtitle">تم إنشاؤه بواسطة: محلل العقود القانونية المتقدم</div>
                <div class="subtitle">تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
            </div>

            <!-- Contract Information -->
            <div class="section">
                <div class="section-title">1. معلومات العقد</div>
                <div class="content">
                    <table class="table">
                        <tr><th>البيان</th><th>القيمة</th></tr>
                        <tr><td>اسم الملف</td><td>{contract_info.get('filename', 'غير محدد')}</td></tr>
                        <tr><td>نوع العقد</td><td>{contract_info.get('type', 'غير محدد')}</td></tr>
                        <tr><td>عدد الصفحات</td><td>{contract_info.get('pages', 'غير محدد')}</td></tr>
                        <tr><td>عدد الكلمات</td><td>{contract_info.get('word_count', 'غير محدد')}</td></tr>
                        <tr><td>اللغة</td><td>{contract_info.get('language', 'غير محدد')}</td></tr>
                    </table>
                </div>
            </div>

            <!-- Translation -->
            <div class="section">
                <div class="section-title">2. ترجمة العقد</div>
                <div class="content">
                    <h4>النص المترجم:</h4>
                    <p>{translation.get('arabic_translation') or translation.get('english_translation', 'الترجمة غير متوفرة')}</p>
                </div>
            </div>

            <!-- Legal Points -->
            <div class="section">
                <div class="section-title">3. النقاط القانونية</div>
                <div class="content">
        """

        # Add legal points
        if legal_points:
            for i, point in enumerate(legal_points, 1):
                html_content += f"""
                    <div class="legal-point">
                        <h4>النقطة {i}: {point.get('title', point.get('point', 'نقطة قانونية'))}</h4>
                        <p><strong>الوصف:</strong> {point.get('description', point.get('point', 'غير محدد'))}</p>
                        <p><strong>مستوى المخاطر:</strong> {point.get('risk_level', point.get('status', 'متوسط'))}</p>
                        <p><strong>الأولوية:</strong> {point.get('priority', 'متوسطة')}</p>
                    </div>
                """
        else:
            html_content += "<p>لا توجد نقاط تحليل قانونية متاحة لهذا العقد.</p>"

        html_content += """
                </div>
            </div>

            <!-- Risk Assessment -->
            <div class="section">
                <div class="section-title">4. تقييم المخاطر / Risk Assessment</div>
                <div class="content">
                    <p><strong>نتيجة المخاطر الإجمالية:</strong> <span class="risk-score">{}/100</span></p>
                    <p><strong>مستوى المخاطر:</strong> {}</p>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="section">
                <div class="section-title">5. التوصيات / Recommendations</div>
                <div class="content">
        """.format(
            risk_score,
            'عالي' if risk_score > 70 else 'متوسط' if risk_score > 40 else 'منخفض'
        )

        # Add recommendations
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                if isinstance(rec, dict):
                    html_content += f"""
                        <div class="recommendation">
                            <h4>التوصية {i}: {rec.get('title', 'توصية')}</h4>
                            <p>{rec.get('description', 'غير محدد')}</p>
                            <p><strong>الأولوية:</strong> {rec.get('priority', 'متوسطة')}</p>
                        </div>
                    """
                else:
                    html_content += f"""
                        <div class="recommendation">
                            <h4>التوصية {i}</h4>
                            <p>{rec}</p>
                        </div>
                    """
        else:
            html_content += "<p>لا توجد توصيات محددة متاحة.</p>"

        html_content += """
                </div>
            </div>

            <!-- Executive Summary -->
            <div class="section">
                <div class="section-title">6. الملخص التنفيذي / Executive Summary</div>
                <div class="content">
                    <table class="table">
                        <tr><th>المقياس</th><th>القيمة</th></tr>
                        <tr><td>نتيجة المخاطر</td><td>{}/100</td></tr>
                        <tr><td>عدد النقاط القانونية</td><td>{}</td></tr>
                        <tr><td>عدد التوصيات</td><td>{}</td></tr>
                        <tr><td>حالة التقرير</td><td>مكتمل</td></tr>
                    </table>
                </div>
            </div>

        </body>
        </html>
        """.format(
            risk_score,
            len(legal_points),
            len(recommendations)
        )

        return html_content

    def _export_reportlab_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Fallback PDF export using ReportLab with bilingual text"""
        try:
            # Debug: Log the analysis data structure
            logger.info(f"Export PDF - Analysis data keys: {list(analysis_data.keys())}")
            logger.info(f"Export PDF - Risk score type: {type(analysis_data.get('risk_score'))}, value: {analysis_data.get('risk_score')}")

            # Ensure we have the required data structure
            if not analysis_data:
                raise ValueError("No analysis data provided")

            # Ensure translation data is available
            if 'translation' not in analysis_data and 'text' in analysis_data:
                # Create basic translation structure if missing
                analysis_data['translation'] = {
                    'english_translation': analysis_data.get('text', ''),
                    'arabic_translation': analysis_data.get('text', ''),
                    'source_language': 'auto-detected',
                    'target_language': 'bilingual',
                    'confidence_level': 'medium'
                }

            # Ensure contract_info is available
            if 'contract_info' not in analysis_data:
                analysis_data['contract_info'] = {
                    'filename': 'Contract Analysis',
                    'type': 'General Contract',
                    'pages': 1,
                    'word_count': len(analysis_data.get('text', '').split()),
                    'language': 'Arabic/English',
                    'file_size': 'N/A',
                    'parties': 'As specified in contract'
                }
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch, cm
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT, TA_JUSTIFY
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import os
            import requests
            import tempfile

            # Validate and prepare data
            if not analysis_data:
                raise ValueError("No analysis data provided")

            # Set default values for missing data
            analysis_data.setdefault('risk_score', 0)
            analysis_data.setdefault('contract_name', 'عقد غير محدد')
            analysis_data.setdefault('analysis_date', datetime.now().strftime('%Y-%m-%d'))

            # Generate meaningful content if missing
            if not analysis_data.get('legal_points') or len(analysis_data['legal_points']) == 0:
                analysis_data['legal_points'] = self._generate_meaningful_legal_points(analysis_data)

            if not analysis_data.get('recommendations') or len(analysis_data['recommendations']) == 0:
                analysis_data['recommendations'] = self._generate_meaningful_recommendations(analysis_data)

            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm,
                title="تقرير تحليل العقد الشامل"
            )

            story = []
            styles = getSampleStyleSheet()

            # Enhanced Arabic font registration system
            arabic_font_name = self._register_arabic_font()

            # Enhanced Arabic text handling for PDF
            def convert_arabic_for_pdf(text):
                """Keep Arabic text as Arabic only for better PDF rendering"""
                if not text:
                    return ""

                # Keep Arabic text as is - no bilingual conversion needed
                # Just clean up any problematic characters
                text = text.replace('\u200f', '').replace('\u200e', '')  # Remove RTL/LTR marks
                return text

            # Use Arabic font that was registered
            arabic_font_name = self._register_arabic_font()

            # Enhanced custom styles
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=styles['Title'],
                fontSize=20,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.darkblue,
                fontName=arabic_font_name
            )

            heading1_style = ParagraphStyle(
                'Heading1Style',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=20,
                spaceBefore=20,
                textColor=colors.darkblue,
                fontName=arabic_font_name,
                borderWidth=1,
                borderColor=colors.darkblue,
                borderPadding=10,
                backColor=colors.lightblue
            )

            heading2_style = ParagraphStyle(
                'Heading2Style',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=15,
                spaceBefore=15,
                textColor=colors.darkgreen,
                fontName=arabic_font_name
            )

            # Arabic text style for normal content
            arabic_text_style = ParagraphStyle(
                'ArabicTextStyle',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                spaceBefore=6,
                fontName=arabic_font_name,
                alignment=TA_RIGHT,  # Right-to-left for Arabic
                leading=18
            )

            # English text style
            english_text_style = ParagraphStyle(
                'EnglishTextStyle',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=12,
                spaceBefore=6,
                fontName=arabic_font_name,
                alignment=TA_LEFT,
                leading=18
            )

            normal_style = ParagraphStyle(
                'NormalStyle',
                parent=styles['Normal'],
                fontSize=11,
                spaceAfter=12,
                alignment=TA_JUSTIFY,
                fontName=arabic_font_name
            )

            arabic_style = ParagraphStyle(
                'ArabicStyle',
                parent=styles['Normal'],
                fontSize=11,
                alignment=TA_RIGHT,
                spaceAfter=12,
                fontName=arabic_font_name,
                leading=16  # Better line spacing for Arabic
            )

            # Helper function to safely encode Arabic text
            def safe_arabic_text(text):
                """Safely encode Arabic text for PDF rendering"""
                if not text:
                    return ""
                try:
                    # Ensure text is properly encoded
                    if isinstance(text, bytes):
                        text = text.decode('utf-8')

                    # Clean Arabic text for better PDF compatibility
                    text = convert_arabic_for_pdf(text)

                    # Remove problematic characters that might not render
                    text = text.replace('\u200f', '').replace('\u200e', '')  # Remove RTL/LTR marks

                    # If Arabic font is not available, transliterate to Latin for readability
                    if arabic_font_name == 'Helvetica':
                        # Fallback: use Latin transliteration for key Arabic terms
                        arabic_to_latin = {
                            'عالي': 'Aali (High)',
                            'متوسط': 'Mutawasit (Medium)',
                            'منخفض': 'Munkhafid (Low)',
                            'عام': 'Aam (General)',
                            'قانوني': 'Qanooni (Legal)',
                            'مالي': 'Mali (Financial)',
                            'امتثال': 'Imtithal (Compliance)',
                            'مخاطر': 'Mukhatir (Risks)',
                            'التزامات': 'Iltizamat (Obligations)',
                            'مراجعة': 'Muraja\'a (Review)',
                            'تقييم': 'Taqyeem (Assessment)',
                            'تأثير': 'Ta\'theer (Impact)',
                            'توصية': 'Tawsiya (Recommendation)'
                        }
                        for arabic, latin in arabic_to_latin.items():
                            text = text.replace(arabic, latin)

                    return text
                except Exception as e:
                    logger.warning(f"Text encoding issue: {e}")
                    return str(text)

            # TITLE PAGE
            story.append(Paragraph(safe_arabic_text("تقرير تحليل العقد الشامل"), title_style))
            story.append(Spacer(1, 0.8*inch))

            # Company and metadata
            story.append(Paragraph(f"Generated by: {self.app_name}", normal_style))
            story.append(Paragraph(f"Company: {self.company_name}", normal_style))
            story.append(Paragraph(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", normal_style))
            story.append(Paragraph(f"User: {analysis_data.get('user_info', {}).get('username', 'System')}", normal_style))
            story.append(Paragraph(f"Contract File: {analysis_data.get('contract_info', {}).get('filename', 'Unknown')}", normal_style))
            story.append(PageBreak())

            # TABLE OF CONTENTS
            story.append(Paragraph("جدول المحتويات", heading1_style))
            toc_data = [
                ["القسم", "رقم الصفحة"],
                ["1. معلومات العقد", "3"],
                ["2. الترجمة الكاملة للعقد", "4"],
                ["3. ملاحظات التحليل القانوني", "5"],
                ["4. المبادئ التوجيهية المطبقة", "7"],
                ["5. تقييم المخاطر", "8"],
                ["6. فحص الامتثال", "9"],
                ["7. مسرد المصطلحات الرئيسية", "10"],
                ["8. التوصيات", "11"],
                ["9. الملخص التنفيذي", "12"],
                ["10. الملاحق", "13"]
            ]

            toc_table = Table(toc_data, colWidths=[3*inch, 3*inch])
            toc_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
            ]))

            story.append(toc_table)
            story.append(PageBreak())

            # 1. CONTRACT INFORMATION
            story.append(Paragraph("1. معلومات العقد", heading1_style))

            contract_info = analysis_data.get('contract_info', {})
            info_data = [
                ["الحقل", "القيمة"],
                ["اسم الملف", contract_info.get('filename', 'غير محدد')],
                ["نوع العقد", contract_info.get('type', 'غير محدد')],
                ["عدد الصفحات", str(contract_info.get('pages', 0))],
                ["عدد الكلمات", str(contract_info.get('word_count', 0))],
                ["تاريخ التحليل", datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                ["النظام القانوني", analysis_data.get('legal_system', 'غير محدد')],
                ["اللغة", contract_info.get('language', 'غير محدد')],
                ["حجم الملف", contract_info.get('file_size', 'غير محدد')],
                ["الأطراف المعنية", contract_info.get('parties', 'غير محدد')]
            ]

            info_table = Table(info_data, colWidths=[2.5*inch, 3.5*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
            ]))

            story.append(info_table)
            story.append(Spacer(1, 20))

            # Contract metadata
            story.append(Paragraph("Contract Metadata / بيانات العقد الوصفية", heading2_style))
            metadata_text = f"""
            Analysis performed using advanced AI technology with legal framework compliance checking.
            This report contains comprehensive analysis including translation, legal points, risk assessment,
            and compliance verification according to {analysis_data.get('legal_system', 'applicable')} legal standards.

            تم إجراء التحليل باستخدام تقنية الذكاء الاصطناعي المتقدمة مع فحص الامتثال للإطار القانوني.
            يحتوي هذا التقرير على تحليل شامل يشمل الترجمة والنقاط القانونية وتقييم المخاطر
            والتحقق من الامتثال وفقاً لمعايير {analysis_data.get('legal_system', 'المعمول بها')} القانونية.
            """
            story.append(Paragraph(metadata_text, normal_style))
            story.append(PageBreak())

            # 2. FULL CONTRACT TRANSLATION WITH HIGHLIGHTED TERMS
            story.append(Paragraph("2. الترجمة الكاملة للعقد / Full Contract Translation", heading1_style))

            translation = analysis_data.get('translation', {})
            if translation:
                # Translation metadata
                story.append(Paragraph("Translation Information / معلومات الترجمة", heading2_style))

                source_lang = translation.get('source_language', 'Unknown')
                target_lang = translation.get('target_language', 'Unknown')
                confidence = translation.get('confidence_level', 'medium')
                confidence_text = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}

                trans_info_data = [
                    ["Source Language / اللغة المصدر", source_lang],
                    ["Target Language / اللغة المستهدفة", target_lang],
                    ["Translation Quality / جودة الترجمة", confidence_text.get(confidence, 'متوسطة')],
                    ["Translation Method / طريقة الترجمة", "AI-Powered Bidirectional Translation"],
                    ["Legal Terms Highlighted / المصطلحات القانونية المميزة", "Yes / نعم"]
                ]

                trans_info_table = Table(trans_info_data, colWidths=[2.5*inch, 3.5*inch])
                trans_info_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 11),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(trans_info_table)
                story.append(Spacer(1, 20))

                # Full translation text
                story.append(Paragraph("Complete Translation / الترجمة الكاملة", heading2_style))
                translation_text = translation.get('english_translation') or translation.get('arabic_translation', 'Translation not available')

                # Split long translation into paragraphs
                if translation_text and len(translation_text) > 500:
                    paragraphs = translation_text.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            story.append(Paragraph(para.strip(), normal_style))
                            story.append(Spacer(1, 8))
                else:
                    story.append(Paragraph(translation_text, normal_style))

                story.append(Spacer(1, 20))

                # Highlighted legal terms legend
                story.append(Paragraph("ترميز المصطلحات القانونية بالألوان", heading2_style))
                legend_data = [
                    ["الفئة", "اللون", "الوصف"],
                    ["المصطلحات القانونية", "أصفر", "المصطلحات القانونية العامة مثل: عقد، التزام، حق، واجب"],
                    ["الأطراف", "أخضر", "أطراف العقد والكيانات المشاركة"],
                    ["المصطلحات المالية", "أحمر", "المبالغ المالية والبنود المالية"],
                    ["التواريخ والمواعيد", "أزرق", "التواريخ المهمة والجداول الزمنية"],
                    ["البنود الحرجة", "برتقالي", "البنود عالية المخاطر التي تحتاج مراجعة فورية"]
                ]

                legend_table = Table(legend_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
                legend_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lavender),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(legend_table)
            else:
                story.append(Paragraph("Translation not available for this contract.", normal_style))
                story.append(Paragraph(safe_arabic_text("الترجمة غير متوفرة لهذا العقد."), arabic_style))

            story.append(PageBreak())

            # 3. LEGAL ANALYSIS NOTES
            story.append(Paragraph("3. ملاحظات التحليل القانوني", heading1_style))

            # Generate meaningful legal points if not available or if they're generic
            legal_points = analysis_data.get('legal_points', [])
            if not legal_points or self._are_points_generic(legal_points):
                legal_points = self._generate_meaningful_legal_points(analysis_data)

            if legal_points:
                story.append(Paragraph("التحليل القانوني الشامل", heading2_style))

                for i, point in enumerate(legal_points, 1):
                    # Point header
                    story.append(Paragraph(f"3.{i} {point.get('title', 'Legal Point')}", heading2_style))

                    # Point details table
                    point_data = [
                        ["الجانب", "التفاصيل"],
                        ["الوصف", point.get('description', 'لا يوجد وصف متاح')],
                        ["الأولوية", self._translate_priority_to_arabic(point.get('priority', 'Medium'))],
                        ["مستوى المخاطر", self._translate_risk_level_to_arabic(point.get('risk_level', 'Medium'))],
                        ["الأساس القانوني", point.get('legal_basis') or point.get('law_reference', 'غير محدد')],
                        ["التوصية", self._translate_recommendation_to_arabic(point.get('recommendation', 'Review required'))],
                        ["التأثير", self._translate_impact_to_arabic(point.get('impact', 'To be assessed'))],
                        ["الفئة", self._translate_category_to_arabic(point.get('category', 'general'))]
                    ]

                    point_table = Table(point_data, colWidths=[2*inch, 4*inch])
                    point_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.orange),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.lightyellow),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('VALIGN', (0, 0), (-1, -1), 'TOP')
                    ]))

                    story.append(point_table)
                    story.append(Spacer(1, 15))

                # AI Analysis Summary
                story.append(Paragraph("ملخص تحليل الذكاء الاصطناعي", heading2_style))
                ai_summary = analysis_data.get('ai_analysis_summary',
                    "تم تحديد النقاط القانونية الرئيسية التي تتطلب الانتباه من خلال تحليل الذكاء الاصطناعي. "
                    "تم تصنيف كل نقطة حسب الأولوية ومستوى المخاطر لمساعدة في ترتيب أولويات المراجعة.")
                story.append(Paragraph(ai_summary, normal_style))

            else:
                story.append(Paragraph("لا توجد نقاط تحليل قانونية متاحة لهذا العقد.", arabic_style))

            story.append(PageBreak())

            # 4. LEGAL GUIDELINES APPLIED
            story.append(Paragraph("4. المبادئ التوجيهية القانونية المطبقة", heading1_style))

            guidelines = analysis_data.get('legal_guidelines', [])
            if guidelines:
                story.append(Paragraph("Applied Guidelines / المبادئ المطبقة", heading2_style))

                guidelines_data = [["Guideline / المبدأ", "Source / المصدر", "Application / التطبيق"]]
                for guideline in guidelines:
                    guidelines_data.append([
                        guideline.get('title', 'Unknown'),
                        guideline.get('source', 'Custom Upload'),
                        guideline.get('application', 'Applied to contract analysis')
                    ])

                guidelines_table = Table(guidelines_data, colWidths=[2*inch, 2*inch, 2*inch])
                guidelines_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.mistyrose),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(guidelines_table)
                story.append(Spacer(1, 20))

                # Custom uploaded documents
                story.append(Paragraph("Custom Uploaded Documents / الوثائق المرفوعة المخصصة", heading2_style))
                custom_docs = analysis_data.get('custom_documents', [])
                if custom_docs:
                    for doc in custom_docs:
                        story.append(Paragraph(f"• {doc.get('filename', 'Unknown Document')}", normal_style))
                        story.append(Paragraph(f"  Type: {doc.get('type', 'Unknown')}", normal_style))
                        story.append(Paragraph(f"  Upload Date: {doc.get('upload_date', 'Unknown')}", normal_style))
                else:
                    story.append(Paragraph("No custom documents were uploaded for this analysis.", normal_style))
                    story.append(Paragraph("لم يتم رفع وثائق مخصصة لهذا التحليل.", arabic_style))
            else:
                story.append(Paragraph("Standard legal guidelines were applied based on the selected legal system.", normal_style))
                story.append(Paragraph("تم تطبيق المبادئ التوجيهية القانونية المعيارية بناءً على النظام القانوني المحدد.", arabic_style))

            story.append(PageBreak())

            # 5. RISK ASSESSMENT
            story.append(Paragraph("5. تقييم المخاطر / Risk Assessment", heading1_style))

            risk_assessment = analysis_data.get('risk_assessment', {})
            if risk_assessment:
                # Overall risk score
                overall_score = risk_assessment.get('overall_score', 0)
                risk_level = risk_assessment.get('risk_level', 'Medium')

                story.append(Paragraph("Overall Risk Analysis / تحليل المخاطر الإجمالي", heading2_style))

                risk_summary_data = [
                    ["Risk Metric / مقياس المخاطر", "Value / القيمة", "Status / الحالة"],
                    ["Overall Score / النتيجة الإجمالية", f"{overall_score}/100", self._get_risk_status(overall_score)],
                    ["Risk Level / مستوى المخاطر", risk_level, self._get_risk_level_arabic(risk_level)],
                    ["Assessment Date / تاريخ التقييم", datetime.now().strftime('%Y-%m-%d'), "Current / حالي"],
                    ["Confidence Level / مستوى الثقة", risk_assessment.get('confidence', 'High'), "عالي"]
                ]

                risk_summary_table = Table(risk_summary_data, colWidths=[2*inch, 2*inch, 2*inch])
                risk_summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkred),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightpink),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(risk_summary_table)
                story.append(Spacer(1, 20))

                # Risk factors
                risk_factors = risk_assessment.get('risk_factors', [])
                if risk_factors:
                    story.append(Paragraph("Identified Risk Factors / عوامل المخاطر المحددة", heading2_style))
                    for i, factor in enumerate(risk_factors, 1):
                        story.append(Paragraph(f"{i}. {factor}", normal_style))

                # Mitigation recommendations
                mitigation = risk_assessment.get('mitigation_recommendations', [])
                if mitigation:
                    story.append(Paragraph("Risk Mitigation Recommendations / توصيات تخفيف المخاطر", heading2_style))
                    for i, rec in enumerate(mitigation, 1):
                        story.append(Paragraph(f"{i}. {rec}", normal_style))

            else:
                story.append(Paragraph("Risk assessment data not available.", normal_style))
                story.append(Paragraph("بيانات تقييم المخاطر غير متوفرة.", arabic_style))

            story.append(PageBreak())

            # 6. COMPLIANCE CHECK
            story.append(Paragraph("6. فحص الامتثال / Compliance Check", heading1_style))

            compliance = analysis_data.get('compliance', {})
            if compliance:
                # Compliance status
                compliance_status = compliance.get('compliance_status', 'Unknown')
                story.append(Paragraph("Compliance Status / حالة الامتثال", heading2_style))

                compliance_data = [
                    ["جانب الامتثال", "الحالة", "التفاصيل"],
                    ["الامتثال العام", self._translate_compliance_status_to_arabic(compliance_status), compliance.get('overall_details', 'غير متوفر')],
                    ["الإطار القانوني", self._translate_compliance_status_to_arabic(compliance.get('framework_compliance', 'Compliant')), "بناءً على النظام القانوني المحدد"],
                    ["المتطلبات التنظيمية", self._translate_compliance_status_to_arabic(compliance.get('regulatory_compliance', 'Compliant')), "تم استيفاء المتطلبات المعيارية"],
                    ["معايير الصناعة", self._translate_compliance_status_to_arabic(compliance.get('industry_compliance', 'Compliant')), "متطلبات خاصة بالصناعة"]
                ]

                compliance_table = Table(compliance_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
                compliance_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(compliance_table)
                story.append(Spacer(1, 20))

                # Compliance issues
                compliance_issues = compliance.get('compliance_issues', [])
                if compliance_issues:
                    story.append(Paragraph("Compliance Issues Identified / مشاكل الامتثال المحددة", heading2_style))
                    for i, issue in enumerate(compliance_issues, 1):
                        story.append(Paragraph(f"{i}. {issue}", normal_style))
                else:
                    story.append(Paragraph("No compliance issues identified.", normal_style))
                    story.append(Paragraph("لم يتم تحديد مشاكل في الامتثال.", arabic_style))

                # Compliance recommendations
                compliance_recs = compliance.get('recommendations', [])
                if compliance_recs:
                    story.append(Paragraph("Compliance Recommendations / توصيات الامتثال", heading2_style))
                    for i, rec in enumerate(compliance_recs, 1):
                        story.append(Paragraph(f"{i}. {rec}", normal_style))

            else:
                story.append(Paragraph("Compliance check data not available.", normal_style))
                story.append(Paragraph("بيانات فحص الامتثال غير متوفرة.", arabic_style))

            story.append(PageBreak())
            
            # 7. KEY TERMS GLOSSARY
            story.append(Paragraph("7. مسرد المصطلحات الرئيسية / Key Terms Glossary", heading1_style))

            key_terms = analysis_data.get('key_terms', [])
            if key_terms:
                story.append(Paragraph("Legal Terms with Translations / المصطلحات القانونية مع الترجمات", heading2_style))

                # Create terms table
                terms_data = [["Arabic / العربية", "English / الإنجليزية", "Category / الفئة", "Explanation / الشرح"]]
                for term in key_terms[:15]:  # Limit to 15 terms
                    terms_data.append([
                        term.get('arabic', 'N/A'),
                        term.get('english', 'N/A'),
                        term.get('category', 'General'),
                        (term.get('explanation', 'N/A')[:80] + '...') if len(term.get('explanation', '')) > 80 else term.get('explanation', 'N/A')
                    ])

                terms_table = Table(terms_data, colWidths=[1.5*inch, 1.5*inch, 1*inch, 2*inch])
                terms_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.gold),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightyellow),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP')
                ]))

                story.append(terms_table)
            else:
                story.append(Paragraph("No key terms identified in this contract.", normal_style))
                story.append(Paragraph("لم يتم تحديد مصطلحات رئيسية في هذا العقد.", arabic_style))

            story.append(PageBreak())

            # 8. RECOMMENDATIONS
            story.append(Paragraph("8. التوصيات / Recommendations", heading1_style))

            # Generate meaningful recommendations if not available or if they're generic
            recommendations = analysis_data.get('recommendations', [])
            if not recommendations or self._are_recommendations_generic(recommendations):
                recommendations = self._generate_meaningful_recommendations(analysis_data)

            if recommendations:
                story.append(Paragraph("Actionable Recommendations / التوصيات القابلة للتنفيذ", heading2_style))

                for i, rec in enumerate(recommendations, 1):
                    if isinstance(rec, dict):
                        story.append(Paragraph(f"8.{i} {rec.get('title', 'Recommendation')}", heading2_style))
                        story.append(Paragraph(f"Description: {rec.get('description', 'No description')}", normal_style))
                        story.append(Paragraph(f"Priority: {rec.get('priority', 'Medium')}", normal_style))
                        story.append(Paragraph(f"Impact: {rec.get('impact', 'To be assessed')}", normal_style))
                    else:
                        story.append(Paragraph(f"8.{i} {rec}", normal_style))
                    story.append(Spacer(1, 10))
            else:
                story.append(Paragraph("No specific recommendations available.", normal_style))
                story.append(Paragraph("لا توجد توصيات محددة متاحة.", arabic_style))

            story.append(PageBreak())

            # 9. EXECUTIVE SUMMARY
            story.append(Paragraph("9. الملخص التنفيذي", heading1_style))

            # High-level overview
            story.append(Paragraph("نظرة عامة على تحليل العقد", heading2_style))

            exec_summary_data = [
                ["Metric / المقياس", "Value / القيمة", "Assessment / التقييم"],
                ["Overall Risk Score / النتيجة الإجمالية للمخاطر", f"{analysis_data.get('risk_score', 0)}/100", self._get_risk_status(analysis_data.get('risk_score', 0))],
                ["Legal Points Identified / النقاط القانونية المحددة", str(len(analysis_data.get('legal_points', []))), "Comprehensive"],
                ["Compliance Status / حالة الامتثال", analysis_data.get('compliance', {}).get('compliance_status', 'Unknown'), "Reviewed"],
                ["Translation Quality / جودة الترجمة", analysis_data.get('translation', {}).get('confidence_level', 'Medium'), "AI-Powered"],
                ["Recommendations Count / عدد التوصيات", str(len(analysis_data.get('recommendations', []))), "Actionable"]
            ]

            exec_table = Table(exec_summary_data, colWidths=[2*inch, 2*inch, 2*inch])
            exec_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(exec_table)
            story.append(Spacer(1, 20))

            # Key findings
            story.append(Paragraph("Key Findings / النتائج الرئيسية", heading2_style))
            key_findings = f"""
            This comprehensive analysis has identified {len(analysis_data.get('legal_points', []))} legal points requiring attention,
            with an overall risk score of {analysis_data.get('risk_score', 0)}/100. The contract has been thoroughly reviewed
            for compliance with {analysis_data.get('legal_system', 'applicable')} legal standards.

            Key areas of focus include legal compliance, risk mitigation, and contract optimization.
            All identified issues have been categorized by priority and impact for efficient resolution.

            تم في هذا التحليل الشامل تحديد {len(analysis_data.get('legal_points', []))} نقطة قانونية تتطلب الانتباه،
            بنتيجة مخاطر إجمالية قدرها {analysis_data.get('risk_score', 0)}/100. تمت مراجعة العقد بدقة
            للامتثال لمعايير {analysis_data.get('legal_system', 'المعمول بها')} القانونية.

            تشمل المجالات الرئيسية للتركيز الامتثال القانوني وتخفيف المخاطر وتحسين العقد.
            تم تصنيف جميع القضايا المحددة حسب الأولوية والتأثير للحل الفعال.
            """
            story.append(Paragraph(key_findings, normal_style))

            story.append(PageBreak())

            # 10. APPENDICES
            story.append(Paragraph("10. الملاحق / Appendices", heading1_style))

            # Technical details
            story.append(Paragraph("Technical Analysis Details / تفاصيل التحليل التقني", heading2_style))

            tech_details = f"""
            Analysis Engine: Advanced AI-powered legal analysis system
            Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            Legal Framework: {analysis_data.get('legal_system', 'Standard')}
            Language Processing: Bidirectional Arabic-English translation
            Risk Assessment Model: Multi-factor analysis with weighted scoring
            Compliance Checking: Automated regulatory verification

            محرك التحليل: نظام تحليل قانوني متقدم مدعوم بالذكاء الاصطناعي
            تاريخ المعالجة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            الإطار القانوني: {analysis_data.get('legal_system', 'معياري')}
            معالجة اللغة: ترجمة ثنائية الاتجاه عربي-إنجليزي
            نموذج تقييم المخاطر: تحليل متعدد العوامل مع تسجيل مرجح
            فحص الامتثال: التحقق التنظيمي الآلي
            """
            story.append(Paragraph(tech_details, normal_style))

            # Build PDF
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"PDF export error: {e}")
            import traceback
            logger.error(f"PDF export traceback: {traceback.format_exc()}")
            # Try to create a more informative fallback
            try:
                return self._create_informative_fallback_pdf(analysis_data, str(e))
            except:
                return self._create_fallback_pdf(analysis_data)

    def _get_risk_status(self, score) -> str:
        """Get risk status based on score"""
        # Ensure score is an integer
        if isinstance(score, str):
            try:
                score = int(score)
            except (ValueError, TypeError):
                score = 0

        if score >= 70:
            return "High Risk / مخاطر عالية"
        elif score >= 40:
            return "Medium Risk / مخاطر متوسطة"
        else:
            return "Low Risk / مخاطر منخفضة"

    def _get_risk_level_arabic(self, level: str) -> str:
        """Get Arabic translation for risk level"""
        translations = {
            'High': 'عالي',
            'Medium': 'متوسط',
            'Low': 'منخفض',
            'Critical': 'حرج',
            'Moderate': 'معتدل'
        }
        return translations.get(level, 'غير محدد')
    
    def _export_comprehensive_docx(self, analysis_data: Dict[str, Any]) -> bytes:
        """Export comprehensive analysis to Word format with all 10 sections"""
        try:
            # Debug: Log the analysis data structure
            logger.info(f"Export DOCX - Analysis data keys: {list(analysis_data.keys())}")
            logger.info(f"Export DOCX - Text length: {len(analysis_data.get('text', ''))}")
            logger.info(f"Export DOCX - Translation available: {'translation' in analysis_data}")

            # Ensure we have the required data structure
            if not analysis_data:
                raise ValueError("No analysis data provided")

            # Ensure translation data is available
            if 'translation' not in analysis_data and 'text' in analysis_data:
                # Create basic translation structure if missing
                analysis_data['translation'] = {
                    'english_translation': analysis_data.get('text', ''),
                    'arabic_translation': analysis_data.get('text', ''),
                    'source_language': 'auto-detected',
                    'target_language': 'bilingual',
                    'confidence_level': 'medium'
                }

            # Ensure contract_info is available
            if 'contract_info' not in analysis_data:
                analysis_data['contract_info'] = {
                    'filename': 'Contract Analysis',
                    'type': 'General Contract',
                    'pages': 1,
                    'word_count': len(analysis_data.get('text', '').split()),
                    'language': 'Arabic/English',
                    'file_size': 'N/A',
                    'parties': 'As specified in contract'
                }
            from docx import Document
            from docx.shared import Inches, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.table import WD_TABLE_ALIGNMENT

            doc = Document()

            # Title page
            title = doc.add_heading('تقرير تحليل العقد الشامل', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            subtitle = doc.add_heading('Comprehensive Contract Analysis Report', level=1)
            subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Metadata
            doc.add_paragraph(f'Generated by: {self.app_name}')
            doc.add_paragraph(f'Company: {self.company_name}')
            doc.add_paragraph(f'Export Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
            doc.add_paragraph(f'User: {analysis_data.get("user_info", {}).get("username", "System")}')
            doc.add_paragraph(f'Contract File: {analysis_data.get("contract_info", {}).get("filename", "Unknown")}')
            doc.add_page_break()

            # Comprehensive sections
            sections = [
                ('1. معلومات العقد', self._add_contract_info_section),
                ('2. ترجمة العقد', self._add_comprehensive_translation_section),
                ('3. النقاط القانونية', self._add_comprehensive_legal_points_section),
                ('4. المبادئ التوجيهية المطبقة', self._add_guidelines_section),
                ('5. تقييم المخاطر', self._add_comprehensive_risk_section),
                ('6. فحص الامتثال', self._add_comprehensive_compliance_section),
                ('7. مسرد المصطلحات الرئيسية', self._add_terms_glossary_section),
                ('8. التوصيات', self._add_comprehensive_recommendations_section),
                ('9. الملخص التنفيذي', self._add_executive_summary_section),
                ('10. الملاحق', self._add_appendices_section)
            ]

            for section_title, section_func in sections:
                doc.add_heading(section_title, level=1)
                section_func(doc, analysis_data)
                doc.add_page_break()

            # Save to buffer
            buffer = io.BytesIO()
            doc.save(buffer)
            buffer.seek(0)
            return buffer.getvalue()

        except Exception as e:
            logger.error(f"DOCX export error: {e}")
            import traceback
            logger.error(f"DOCX export traceback: {traceback.format_exc()}")
            # Try to create a more informative fallback
            try:
                return self._create_informative_fallback_docx(analysis_data, str(e))
            except:
                return self._create_fallback_docx(analysis_data)
    
    def _add_contract_info_section(self, doc, analysis_data):
        """Add comprehensive contract information section"""
        contract_info = analysis_data.get('contract_info', {})

        # Contract metadata table
        table = doc.add_table(rows=10, cols=2)
        table.style = 'Table Grid'

        info_items = [
            ('File Name / اسم الملف', contract_info.get('filename', 'غير محدد')),
            ('Contract Type / نوع العقد', contract_info.get('type', 'غير محدد')),
            ('Pages / عدد الصفحات', str(contract_info.get('pages', 0))),
            ('Word Count / عدد الكلمات', str(contract_info.get('word_count', 0))),
            ('Analysis Date / تاريخ التحليل', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            ('Legal System / النظام القانوني', analysis_data.get('legal_system', 'غير محدد')),
            ('Language / اللغة', contract_info.get('language', 'غير محدد')),
            ('File Size / حجم الملف', contract_info.get('file_size', 'غير محدد')),
            ('Parties Involved / الأطراف المعنية', contract_info.get('parties', 'غير محدد'))
        ]

        for i, (key, value) in enumerate(info_items):
            table.cell(i, 0).text = key
            table.cell(i, 1).text = value

        doc.add_paragraph()
        doc.add_paragraph('Contract Metadata / بيانات العقد الوصفية')
        doc.add_paragraph(
            'Analysis performed using advanced AI technology with legal framework compliance checking. '
            'This report contains comprehensive analysis including translation, legal points, risk assessment, '
            'and compliance verification according to applicable legal standards.'
        )
        doc.add_paragraph(
            'تم إجراء التحليل باستخدام تقنية الذكاء الاصطناعي المتقدمة مع فحص الامتثال للإطار القانوني. '
            'يحتوي هذا التقرير على تحليل شامل يشمل الترجمة والنقاط القانونية وتقييم المخاطر '
            'والتحقق من الامتثال وفقاً للمعايير القانونية المعمول بها.'
        )
    
    def _add_comprehensive_translation_section(self, doc, analysis_data):
        """Add comprehensive translation section with highlighted terms"""
        translation = analysis_data.get('translation', {})

        if translation:
            # Translation metadata
            doc.add_heading('Translation Information / معلومات الترجمة', level=2)

            source_lang = translation.get('source_language', 'Unknown')
            target_lang = translation.get('target_language', 'Unknown')
            confidence = translation.get('confidence_level', 'medium')
            confidence_text = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}

            # Translation info table
            trans_table = doc.add_table(rows=5, cols=2)
            trans_table.style = 'Table Grid'

            trans_info = [
                ('Source Language / اللغة المصدر', source_lang),
                ('Target Language / اللغة المستهدفة', target_lang),
                ('Translation Quality / جودة الترجمة', confidence_text.get(confidence, 'متوسطة')),
                ('Translation Method / طريقة الترجمة', 'AI-Powered Bidirectional Translation'),
                ('Legal Terms Highlighted / المصطلحات القانونية المميزة', 'Yes / نعم')
            ]

            for i, (key, value) in enumerate(trans_info):
                trans_table.cell(i, 0).text = key
                trans_table.cell(i, 1).text = value

            # Full translation text
            doc.add_heading('Complete Translation / الترجمة الكاملة', level=2)

            # Get both translations if available
            english_translation = translation.get('english_translation', '')
            arabic_translation = translation.get('arabic_translation', '')

            # Determine which translation to show based on source language
            if english_translation and arabic_translation:
                # Show both translations
                doc.add_heading('English Translation / الترجمة الإنجليزية', level=3)
                if len(english_translation) > 1000:
                    paragraphs = english_translation.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            doc.add_paragraph(para.strip())
                else:
                    doc.add_paragraph(english_translation)

                doc.add_heading('Arabic Translation / الترجمة العربية', level=3)
                if len(arabic_translation) > 1000:
                    paragraphs = arabic_translation.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            doc.add_paragraph(para.strip())
                else:
                    doc.add_paragraph(arabic_translation)
            else:
                # Show available translation
                translation_text = english_translation or arabic_translation or 'Translation not available'
                if len(translation_text) > 1000:
                    paragraphs = translation_text.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            doc.add_paragraph(para.strip())
                else:
                    doc.add_paragraph(translation_text)

            # Add original text if different from translation
            original_text = analysis_data.get('text', '')
            if original_text and original_text != english_translation and original_text != arabic_translation:
                doc.add_heading('Original Contract Text / النص الأصلي للعقد', level=3)
                if len(original_text) > 1000:
                    paragraphs = original_text.split('\n\n')
                    for para in paragraphs:
                        if para.strip():
                            doc.add_paragraph(para.strip())
                else:
                    doc.add_paragraph(original_text)

            # Legal terms color coding legend
            doc.add_heading('ترميز المصطلحات القانونية بالألوان', level=2)
            legend_table = doc.add_table(rows=6, cols=3)
            legend_table.style = 'Table Grid'

            legend_data = [
                ('الفئة', 'اللون', 'الوصف'),
                ('المصطلحات القانونية', 'أصفر', 'المصطلحات القانونية العامة مثل: عقد، التزام، حق، واجب'),
                ('الأطراف', 'أخضر', 'أطراف العقد والكيانات المشاركة'),
                ('المصطلحات المالية', 'أحمر', 'المبالغ المالية والبنود المالية'),
                ('التواريخ والمواعيد', 'أزرق', 'التواريخ المهمة والجداول الزمنية'),
                ('البنود الحرجة', 'برتقالي', 'البنود عالية المخاطر التي تحتاج مراجعة فورية')
            ]

            for i, (cat, color, desc) in enumerate(legend_data):
                legend_table.cell(i, 0).text = cat
                legend_table.cell(i, 1).text = color
                legend_table.cell(i, 2).text = desc

        else:
            doc.add_paragraph('Translation not available for this contract.')
            doc.add_paragraph('الترجمة غير متوفرة لهذا العقد.')
    
    def _add_comprehensive_legal_points_section(self, doc, analysis_data):
        """Add comprehensive legal analysis notes section"""
        # Generate meaningful legal points if not available or if they're generic
        legal_points = analysis_data.get('legal_points', [])
        if not legal_points or self._are_points_generic(legal_points):
            legal_points = self._generate_meaningful_legal_points(analysis_data)

        if legal_points:
            doc.add_heading('التحليل القانوني الشامل', level=2)

            for i, point in enumerate(legal_points, 1):
                doc.add_heading(f"3.{i} {point.get('title', 'Legal Point')}", level=3)

                # Point details table (increased rows for additional details)
                point_table = doc.add_table(rows=9, cols=2)
                point_table.style = 'Table Grid'

                # Enhanced point details with more comprehensive information
                description = point.get('description', 'No description available')
                if len(description) < 50:  # If description is too short, enhance it
                    description = f"{description}. This legal point requires careful review to ensure compliance with applicable legal standards and to mitigate potential risks."

                point_details = [
                    ('الجانب', 'التفاصيل'),
                    ('الوصف', description),
                    ('الأولوية', self._translate_priority_to_arabic(point.get('priority', 'Medium'))),
                    ('مستوى المخاطر', self._translate_risk_level_to_arabic(point.get('risk_level', 'Medium'))),
                    ('الأساس القانوني', point.get('legal_basis', point.get('law_reference', 'القانون المدني الكويتي - الأحكام العامة'))),
                    ('التوصية', self._translate_recommendation_to_arabic(point.get('recommendation', 'Detailed legal review recommended to ensure compliance'))),
                    ('التأثير', self._translate_impact_to_arabic(point.get('impact', 'Potential impact on contract validity and enforceability'))),
                    ('الفئة', self._translate_category_to_arabic(point.get('category', 'general'))),
                    ('الإلحاح', self._translate_urgency_to_arabic(point.get('urgency', 'Standard review timeline')))
                ]

                for j, (aspect, detail) in enumerate(point_details):
                    point_table.cell(j, 0).text = aspect
                    point_table.cell(j, 1).text = detail

                doc.add_paragraph()

            # AI Analysis Summary
            doc.add_heading('ملخص تحليل الذكاء الاصطناعي', level=2)
            ai_summary = analysis_data.get('ai_analysis_summary',
                'تم تحديد النقاط القانونية الرئيسية التي تتطلب الانتباه من خلال تحليل الذكاء الاصطناعي. '
                'تم تصنيف كل نقطة حسب الأولوية ومستوى المخاطر لمساعدة في ترتيب أولويات المراجعة.')
            doc.add_paragraph(ai_summary)

        else:
            doc.add_paragraph('لا توجد نقاط تحليل قانونية متاحة لهذا العقد.')

    def _add_guidelines_section(self, doc, analysis_data):
        """Add legal guidelines applied section"""
        guidelines = analysis_data.get('legal_guidelines', [])

        if guidelines:
            doc.add_heading('Applied Guidelines / المبادئ المطبقة', level=2)

            guidelines_table = doc.add_table(rows=len(guidelines)+1, cols=3)
            guidelines_table.style = 'Table Grid'

            # Header
            guidelines_table.cell(0, 0).text = 'Guideline / المبدأ'
            guidelines_table.cell(0, 1).text = 'Source / المصدر'
            guidelines_table.cell(0, 2).text = 'Application / التطبيق'

            for i, guideline in enumerate(guidelines, 1):
                guidelines_table.cell(i, 0).text = guideline.get('title', 'Unknown')
                guidelines_table.cell(i, 1).text = guideline.get('source', 'Custom Upload')
                guidelines_table.cell(i, 2).text = guideline.get('application', 'Applied to contract analysis')

            # Custom uploaded documents
            doc.add_heading('Custom Uploaded Documents / الوثائق المرفوعة المخصصة', level=2)
            custom_docs = analysis_data.get('custom_documents', [])
            if custom_docs:
                for doc_info in custom_docs:
                    doc.add_paragraph(f"• {doc_info.get('filename', 'Unknown Document')}")
                    doc.add_paragraph(f"  Type: {doc_info.get('type', 'Unknown')}")
                    doc.add_paragraph(f"  Upload Date: {doc_info.get('upload_date', 'Unknown')}")
            else:
                doc.add_paragraph('No custom documents were uploaded for this analysis.')
                doc.add_paragraph('لم يتم رفع وثائق مخصصة لهذا التحليل.')
        else:
            doc.add_paragraph('Standard legal guidelines were applied based on the selected legal system.')
            doc.add_paragraph('تم تطبيق المبادئ التوجيهية القانونية المعيارية بناءً على النظام القانوني المحدد.')
    
    def _add_comprehensive_risk_section(self, doc, analysis_data):
        """Add comprehensive risk assessment section"""
        risk_assessment = analysis_data.get('risk_assessment', {})

        if risk_assessment:
            # Overall risk analysis
            overall_score = risk_assessment.get('overall_score', 0)
            risk_level = risk_assessment.get('risk_level', 'Medium')

            doc.add_heading('Overall Risk Analysis / تحليل المخاطر الإجمالي', level=2)

            risk_table = doc.add_table(rows=5, cols=3)
            risk_table.style = 'Table Grid'

            risk_data = [
                ('Risk Metric / مقياس المخاطر', 'Value / القيمة', 'Status / الحالة'),
                ('Overall Score / النتيجة الإجمالية', f'{overall_score}/100', self._get_risk_status(overall_score)),
                ('Risk Level / مستوى المخاطر', risk_level, self._get_risk_level_arabic(risk_level)),
                ('Assessment Date / تاريخ التقييم', datetime.now().strftime('%Y-%m-%d'), 'Current / حالي'),
                ('Confidence Level / مستوى الثقة', risk_assessment.get('confidence', 'High'), 'عالي')
            ]

            for i, (metric, value, status) in enumerate(risk_data):
                risk_table.cell(i, 0).text = metric
                risk_table.cell(i, 1).text = value
                risk_table.cell(i, 2).text = status

            # Risk factors
            risk_factors = risk_assessment.get('risk_factors', [])
            if risk_factors:
                doc.add_heading('Identified Risk Factors / عوامل المخاطر المحددة', level=2)
                for i, factor in enumerate(risk_factors, 1):
                    doc.add_paragraph(f"{i}. {factor}")

            # Mitigation recommendations
            mitigation = risk_assessment.get('mitigation_recommendations', [])
            if mitigation:
                doc.add_heading('Risk Mitigation Recommendations / توصيات تخفيف المخاطر', level=2)
                for i, rec in enumerate(mitigation, 1):
                    doc.add_paragraph(f"{i}. {rec}")

        else:
            doc.add_paragraph('Risk assessment data not available.')
            doc.add_paragraph('بيانات تقييم المخاطر غير متوفرة.')
    
    def _add_comprehensive_compliance_section(self, doc, analysis_data):
        """Add comprehensive compliance check section"""
        compliance = analysis_data.get('compliance', {})

        if compliance:
            compliance_status = compliance.get('compliance_status', 'Unknown')
            doc.add_heading('Compliance Status / حالة الامتثال', level=2)

            compliance_table = doc.add_table(rows=5, cols=3)
            compliance_table.style = 'Table Grid'

            compliance_data = [
                ('جانب الامتثال', 'الحالة', 'التفاصيل'),
                ('الامتثال العام', self._translate_compliance_status_to_arabic(compliance_status), compliance.get('overall_details', 'غير متوفر')),
                ('الإطار القانوني', self._translate_compliance_status_to_arabic(compliance.get('framework_compliance', 'Compliant')), 'بناءً على النظام القانوني المحدد'),
                ('المتطلبات التنظيمية', self._translate_compliance_status_to_arabic(compliance.get('regulatory_compliance', 'Compliant')), 'تم استيفاء المتطلبات المعيارية'),
                ('معايير الصناعة', self._translate_compliance_status_to_arabic(compliance.get('industry_compliance', 'Compliant')), 'متطلبات خاصة بالصناعة')
            ]

            for i, (aspect, status, details) in enumerate(compliance_data):
                compliance_table.cell(i, 0).text = aspect
                compliance_table.cell(i, 1).text = status
                compliance_table.cell(i, 2).text = details

            # Compliance issues
            compliance_issues = compliance.get('compliance_issues', [])
            if compliance_issues:
                doc.add_heading('Compliance Issues Identified / مشاكل الامتثال المحددة', level=2)
                for i, issue in enumerate(compliance_issues, 1):
                    doc.add_paragraph(f"{i}. {issue}")
            else:
                doc.add_paragraph('No compliance issues identified.')
                doc.add_paragraph('لم يتم تحديد مشاكل في الامتثال.')

        else:
            doc.add_paragraph('Compliance check data not available.')
            doc.add_paragraph('بيانات فحص الامتثال غير متوفرة.')

    def _add_terms_glossary_section(self, doc, analysis_data):
        """Add key terms glossary section"""
        key_terms = analysis_data.get('key_terms', [])

        if key_terms:
            doc.add_heading('Legal Terms with Translations / المصطلحات القانونية مع الترجمات', level=2)

            # Create terms table
            terms_table = doc.add_table(rows=min(len(key_terms)+1, 16), cols=4)
            terms_table.style = 'Table Grid'

            # Header
            terms_table.cell(0, 0).text = 'Arabic / العربية'
            terms_table.cell(0, 1).text = 'English / الإنجليزية'
            terms_table.cell(0, 2).text = 'Category / الفئة'
            terms_table.cell(0, 3).text = 'Explanation / الشرح'

            for i, term in enumerate(key_terms[:15], 1):  # Limit to 15 terms
                terms_table.cell(i, 0).text = term.get('arabic', 'N/A')
                terms_table.cell(i, 1).text = term.get('english', 'N/A')
                terms_table.cell(i, 2).text = term.get('category', 'General')
                explanation = term.get('explanation', 'N/A')
                terms_table.cell(i, 3).text = explanation[:100] + '...' if len(explanation) > 100 else explanation

        else:
            doc.add_paragraph('No key terms identified in this contract.')
            doc.add_paragraph('لم يتم تحديد مصطلحات رئيسية في هذا العقد.')

    def _add_comprehensive_recommendations_section(self, doc, analysis_data):
        """Add comprehensive recommendations section"""
        # Generate meaningful recommendations if not available or if they're generic
        recommendations = analysis_data.get('recommendations', [])
        if not recommendations or self._are_recommendations_generic(recommendations):
            recommendations = self._generate_meaningful_recommendations(analysis_data)

        if recommendations:
            doc.add_heading('Actionable Recommendations / التوصيات القابلة للتنفيذ', level=2)

            for i, rec in enumerate(recommendations, 1):
                if isinstance(rec, dict):
                    doc.add_heading(f"8.{i} {rec.get('title', 'Recommendation')}", level=3)

                    # Enhanced description
                    description = rec.get('description', 'No description')
                    if len(description) < 50:  # If description is too short, enhance it
                        description = f"{description}. This recommendation is based on legal analysis and should be implemented to improve contract compliance and reduce legal risks."

                    # Create detailed recommendation table
                    rec_table = doc.add_table(rows=7, cols=2)
                    rec_table.style = 'Table Grid'

                    rec_details = [
                        ('الجانب', 'التفاصيل'),
                        ('الوصف', description),
                        ('الأولوية', self._translate_priority_to_arabic(rec.get('priority', 'Medium'))),
                        ('الفئة', self._translate_category_to_arabic(rec.get('category', 'Legal Compliance'))),
                        ('التنفيذ', self._translate_implementation_to_arabic(rec.get('implementation', 'Consult with legal advisor for proper implementation'))),
                        ('الجدول الزمني', self._translate_timeline_to_arabic(rec.get('timeline', 'Within 30 days of contract review'))),
                        ('المرجع القانوني', rec.get('law_reference', 'القانون المدني الكويتي - الأحكام العامة'))
                    ]

                    for j, (aspect, detail) in enumerate(rec_details):
                        rec_table.cell(j, 0).text = aspect
                        rec_table.cell(j, 1).text = detail

                    doc.add_paragraph()  # Add spacing
                    doc.add_paragraph(f"Impact: {rec.get('impact', 'To be assessed')}")
                else:
                    doc.add_paragraph(f"8.{i} {rec}")

            # Implementation timeline
            doc.add_heading('Implementation Timeline / الجدول الزمني للتنفيذ', level=2)
            timeline_text = """
            Immediate (0-7 days): Address high-priority legal issues and compliance gaps
            Short-term (1-4 weeks): Implement recommended contract modifications
            Medium-term (1-3 months): Review and update related documentation
            Long-term (3+ months): Establish ongoing compliance monitoring

            فوري (0-7 أيام): معالجة القضايا القانونية عالية الأولوية وثغرات الامتثال
            قصير المدى (1-4 أسابيع): تنفيذ التعديلات الموصى بها على العقد
            متوسط المدى (1-3 أشهر): مراجعة وتحديث الوثائق ذات الصلة
            طويل المدى (3+ أشهر): إنشاء مراقبة مستمرة للامتثال
            """
            doc.add_paragraph(timeline_text)

        else:
            doc.add_paragraph('No specific recommendations available.')
            doc.add_paragraph('لا توجد توصيات محددة متاحة.')

    def _add_executive_summary_section(self, doc, analysis_data):
        """Add executive summary section"""
        doc.add_heading('Contract Analysis Overview / نظرة عامة على تحليل العقد', level=2)

        # Executive summary table
        exec_table = doc.add_table(rows=7, cols=3)
        exec_table.style = 'Table Grid'

        exec_data = [
            ('Metric / المقياس', 'Value / القيمة', 'Assessment / التقييم'),
            ('Overall Risk Score / النتيجة الإجمالية للمخاطر', f"{analysis_data.get('risk_score', 0)}/100", self._get_risk_status(analysis_data.get('risk_score', 0))),
            ('Legal Points Identified / النقاط القانونية المحددة', str(len(analysis_data.get('legal_points', []))), 'Comprehensive'),
            ('Compliance Status / حالة الامتثال', analysis_data.get('compliance', {}).get('compliance_status', 'Unknown'), 'Reviewed'),
            ('Translation Quality / جودة الترجمة', analysis_data.get('translation', {}).get('confidence_level', 'Medium'), 'AI-Powered'),
            ('Recommendations Count / عدد التوصيات', str(len(analysis_data.get('recommendations', []))), 'Actionable')
        ]

        for i, (metric, value, assessment) in enumerate(exec_data):
            exec_table.cell(i, 0).text = metric
            exec_table.cell(i, 1).text = value
            exec_table.cell(i, 2).text = assessment

        # Key findings
        doc.add_heading('Key Findings / النتائج الرئيسية', level=2)
        key_findings = f"""
        This comprehensive analysis has identified {len(analysis_data.get('legal_points', []))} legal points requiring attention,
        with an overall risk score of {analysis_data.get('risk_score', 0)}/100. The contract has been thoroughly reviewed
        for compliance with {analysis_data.get('legal_system', 'applicable')} legal standards.

        Key areas of focus include legal compliance, risk mitigation, and contract optimization.
        All identified issues have been categorized by priority and impact for efficient resolution.
        """
        doc.add_paragraph(key_findings)

    def _add_appendices_section(self, doc, analysis_data):
        """Add appendices section"""
        doc.add_heading('Technical Analysis Details / تفاصيل التحليل التقني', level=2)

        tech_details = f"""
        Analysis Engine: Advanced AI-powered legal analysis system
        Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        Legal Framework: {analysis_data.get('legal_system', 'Standard')}
        Language Processing: Bidirectional Arabic-English translation
        Risk Assessment Model: Multi-factor analysis with weighted scoring
        Compliance Checking: Automated regulatory verification

        محرك التحليل: نظام تحليل قانوني متقدم مدعوم بالذكاء الاصطناعي
        تاريخ المعالجة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        الإطار القانوني: {analysis_data.get('legal_system', 'معياري')}
        معالجة اللغة: ترجمة ثنائية الاتجاه عربي-إنجليزي
        نموذج تقييم المخاطر: تحليل متعدد العوامل مع تسجيل مرجح
        فحص الامتثال: التحقق التنظيمي الآلي
        """
        doc.add_paragraph(tech_details)
    
    def _create_fallback_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Create fallback PDF with basic text"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph
            from reportlab.lib.styles import getSampleStyleSheet
            
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            story.append(Paragraph("Contract Analysis Report", styles['Title']))
            story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal']))
            story.append(Paragraph("Analysis data available in application interface.", styles['Normal']))
            
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
        except:
            return b"PDF generation failed"
    
    def _create_fallback_docx(self, analysis_data: Dict[str, Any]) -> bytes:
        """Create fallback DOCX with basic text"""
        try:
            from docx import Document
            
            doc = Document()
            doc.add_heading('Contract Analysis Report', 0)
            doc.add_paragraph(f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M")}')
            doc.add_paragraph('Analysis data available in application interface.')
            
            buffer = io.BytesIO()
            doc.save(buffer)
            buffer.seek(0)
            return buffer.getvalue()
        except:
            return b"DOCX generation failed"
    
    def render_enhanced_export_options(self, analysis_data: Dict[str, Any]):
        """Render comprehensive export options in Streamlit"""
        if not analysis_data:
            st.warning("لا توجد نتائج تحليل للتصدير")
            return

        st.markdown("---")
        st.markdown("### 📥 تصدير التقرير الشامل المتقدم")

        # Show what's included in the comprehensive report
        with st.expander("📋 محتويات التقرير الشامل / Comprehensive Report Contents", expanded=False):
            st.markdown("""
            **يتضمن التقرير الشامل الأقسام التالية:**

            1. **معلومات العقد** - تفاصيل الملف والبيانات الوصفية
            2. **الترجمة الكاملة للعقد** - ترجمة ثنائية الاتجاه مع تمييز المصطلحات القانونية
            3. **ملاحظات التحليل القانوني** - نقاط قانونية مفصلة مع التوصيات
            4. **المبادئ التوجيهية المطبقة** - المراجع القانونية والوثائق المرفوعة
            5. **تقييم المخاطر** - تحليل شامل للمخاطر مع توصيات التخفيف
            6. **فحص الامتثال** - التحقق من الامتثال للأنظمة القانونية
            7. **مسرد المصطلحات الرئيسية** - مصطلحات قانونية مصنفة مع الترجمات
            8. **التوصيات** - توصيات قابلة للتنفيذ مع الجدول الزمني
            9. **الملخص التنفيذي** - نظرة عامة عالية المستوى على النتائج
            10. **الملاحق** - تفاصيل تقنية ومعلومات إضافية

            ---

            **The comprehensive report includes the following sections:**

            1. **Contract Information** - File details and metadata
            2. **Full Contract Translation** - Bidirectional translation with highlighted legal terms
            3. **Legal Analysis Notes** - Detailed legal points with recommendations
            4. **Legal Guidelines Applied** - Legal references and uploaded documents
            5. **Risk Assessment** - Comprehensive risk analysis with mitigation recommendations
            6. **Compliance Check** - Legal compliance verification
            7. **Key Terms Glossary** - Categorized legal terms with translations
            8. **Recommendations** - Actionable recommendations with implementation timeline
            9. **Executive Summary** - High-level overview of findings
            10. **Appendices** - Technical details and additional information
            """)

        col1, col2 = st.columns(2)

        with col1:
            if st.button("📄 تصدير PDF شامل", type="primary", use_container_width=True):
                try:
                    with st.spinner("إنشاء التقرير الشامل PDF..."):
                        pdf_data = self.export_comprehensive_analysis(analysis_data, 'pdf')

                        filename = f"comprehensive_contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

                        st.download_button(
                            label="📥 تحميل التقرير الشامل PDF",
                            data=pdf_data,
                            file_name=filename,
                            mime="application/pdf",
                            use_container_width=True
                        )

                        st.success("✅ تم إنشاء التقرير الشامل PDF بنجاح!")
                        st.info("📊 التقرير يحتوي على 10 أقسام شاملة مع جميع تفاصيل التحليل")

                except Exception as e:
                    st.error(f"❌ خطأ في إنشاء PDF: {str(e)}")

        with col2:
            if st.button("📝 تصدير Word شامل", type="primary", use_container_width=True):
                try:
                    with st.spinner("إنشاء التقرير الشامل Word..."):
                        docx_data = self.export_comprehensive_analysis(analysis_data, 'docx')

                        filename = f"comprehensive_contract_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"

                        st.download_button(
                            label="📥 تحميل التقرير الشامل Word",
                            data=docx_data,
                            file_name=filename,
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            use_container_width=True
                        )

                        st.success("✅ تم إنشاء التقرير الشامل Word بنجاح!")
                        st.info("📊 التقرير يحتوي على 10 أقسام شاملة مع جميع تفاصيل التحليل")

                except Exception as e:
                    st.error(f"❌ خطأ في إنشاء Word: {str(e)}")

        # Export statistics
        st.markdown("---")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric(
                label="📊 أقسام التقرير",
                value="10",
                help="عدد الأقسام الشاملة في التقرير"
            )

        with col2:
            legal_points_count = len(analysis_data.get('legal_points', []))
            st.metric(
                label="⚖️ النقاط القانونية",
                value=legal_points_count,
                help="عدد النقاط القانونية المحددة"
            )

        with col3:
            risk_score = analysis_data.get('risk_score', 0)
            st.metric(
                label="🎯 نتيجة المخاطر",
                value=f"{risk_score}/100",
                help="النتيجة الإجمالية لتقييم المخاطر"
            )

    def _translate_priority_to_arabic(self, priority: str) -> str:
        """Translate priority levels to Arabic"""
        translations = {
            'high': 'عالي',
            'medium': 'متوسط',
            'low': 'منخفض',
            'urgent': 'عاجل',
            'critical': 'حرج'
        }
        return translations.get(priority.lower(), priority)

    def _translate_risk_level_to_arabic(self, risk_level: str) -> str:
        """Translate risk levels to Arabic"""
        translations = {
            'high': 'عالي',
            'medium': 'متوسط',
            'low': 'منخفض',
            'critical': 'حرج',
            'severe': 'شديد'
        }
        return translations.get(risk_level.lower(), risk_level)

    def _translate_recommendation_to_arabic(self, recommendation: str) -> str:
        """Translate common recommendations to Arabic"""
        translations = {
            'Review required': 'مراجعة مطلوبة',
            'Detailed legal review recommended': 'يُنصح بمراجعة قانونية تفصيلية',
            'Detailed legal review recommended to ensure compliance': 'يُنصح بمراجعة قانونية تفصيلية لضمان الامتثال',
            'Immediate attention needed': 'يحتاج انتباه فوري',
            'Consult legal advisor': 'استشر مستشار قانوني',
            'No action required': 'لا حاجة لإجراء'
        }
        # If no exact match, try to translate common English phrases
        if recommendation not in translations:
            recommendation_lower = recommendation.lower()
            if 'review' in recommendation_lower and 'legal' in recommendation_lower:
                return 'يُنصح بمراجعة قانونية'
            elif 'compliance' in recommendation_lower:
                return 'ضمان الامتثال'
            elif 'consult' in recommendation_lower:
                return 'استشر مستشار قانوني'
        return translations.get(recommendation, recommendation)

    def _translate_impact_to_arabic(self, impact: str) -> str:
        """Translate impact descriptions to Arabic"""
        translations = {
            'To be assessed': 'يحتاج تقييم',
            'High impact': 'تأثير عالي',
            'Medium impact': 'تأثير متوسط',
            'Low impact': 'تأثير منخفض',
            'Potential impact on contract validity': 'تأثير محتمل على صحة العقد',
            'Potential impact on contract validity and enforceability': 'تأثير محتمل على صحة العقد وقابليته للتنفيذ'
        }
        # If no exact match, try to translate common English phrases
        if impact not in translations:
            impact_lower = impact.lower()
            if 'contract' in impact_lower and 'validity' in impact_lower:
                return 'تأثير محتمل على صحة العقد'
            elif 'high' in impact_lower:
                return 'تأثير عالي'
            elif 'medium' in impact_lower:
                return 'تأثير متوسط'
            elif 'low' in impact_lower:
                return 'تأثير منخفض'
        return translations.get(impact, impact)

    def _translate_compliance_status_to_arabic(self, status: str) -> str:
        """Translate compliance status to Arabic"""
        translations = {
            'Compliant': 'متوافق',
            'Non-compliant': 'غير متوافق',
            'Partially compliant': 'متوافق جزئياً',
            'Unknown': 'غير معروف',
            'Under review': 'قيد المراجعة'
        }
        return translations.get(status, status)

    def _translate_urgency_to_arabic(self, urgency: str) -> str:
        """Translate urgency levels to Arabic"""
        translations = {
            'Standard review timeline': 'جدول زمني معياري للمراجعة',
            'Urgent': 'عاجل',
            'Immediate': 'فوري',
            'Within 30 days': 'خلال 30 يوم',
            'Within 7 days': 'خلال 7 أيام',
            'ASAP': 'في أسرع وقت ممكن'
        }
        return translations.get(urgency, urgency)

    def _translate_implementation_to_arabic(self, implementation: str) -> str:
        """Translate implementation instructions to Arabic"""
        translations = {
            'Consult with legal advisor for proper implementation': 'استشر مستشار قانوني للتنفيذ السليم',
            'Review and update contract terms': 'مراجعة وتحديث شروط العقد',
            'Immediate action required': 'مطلوب إجراء فوري',
            'Schedule legal review': 'جدولة مراجعة قانونية',
            'Obtain legal approval': 'الحصول على موافقة قانونية'
        }
        return translations.get(implementation, implementation)

    def _translate_timeline_to_arabic(self, timeline: str) -> str:
        """Translate timeline descriptions to Arabic"""
        translations = {
            'Within 30 days of contract review': 'خلال 30 يوم من مراجعة العقد',
            'Within 7 days': 'خلال 7 أيام',
            'Within 14 days': 'خلال 14 يوم',
            'Immediately': 'فوراً',
            'Before contract execution': 'قبل تنفيذ العقد',
            'During contract review': 'أثناء مراجعة العقد'
        }
        return translations.get(timeline, timeline)

    def _translate_category_to_arabic(self, category: str) -> str:
        """Translate category names to Arabic"""
        translations = {
            'general': 'عام',
            'General': 'عام',
            'legal': 'قانوني',
            'Legal': 'قانوني',
            'financial': 'مالي',
            'Financial': 'مالي',
            'compliance': 'امتثال',
            'Compliance': 'امتثال',
            'risk': 'مخاطر',
            'Risk': 'مخاطر',
            'contract': 'عقد',
            'Contract': 'عقد',
            'payment': 'دفع',
            'Payment': 'دفع',
            'termination': 'إنهاء',
            'Termination': 'إنهاء',
            'obligations': 'التزامات',
            'Obligations': 'التزامات',
            'General Legal Review': 'مراجعة قانونية عامة',
            'Legal Compliance': 'الامتثال القانوني',
            'Contract Terms': 'شروط العقد',
            'Risk Management': 'إدارة المخاطر'
        }
        return translations.get(category, category)

    def _create_fallback_pdf(self, analysis_data: Dict[str, Any]) -> bytes:
        """Create a simple fallback PDF when main export fails"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.lib.units import inch
            import io

            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Title
            story.append(Paragraph("Contract Analysis Report", styles['Title']))
            story.append(Spacer(1, 0.5*inch))

            # Basic information
            story.append(Paragraph("Analysis completed successfully", styles['Normal']))
            story.append(Paragraph(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
            story.append(Spacer(1, 0.3*inch))

            # Analysis summary
            if 'legal_points' in analysis_data:
                story.append(Paragraph(f"Legal points identified: {len(analysis_data['legal_points'])}", styles['Normal']))

            if 'risk_score' in analysis_data:
                story.append(Paragraph(f"Risk score: {analysis_data['risk_score']}", styles['Normal']))

            story.append(Spacer(1, 0.3*inch))
            story.append(Paragraph("Note: Full Arabic report available in Word format", styles['Normal']))

            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()

        except Exception as e:
            logger.error(f"Fallback PDF creation failed: {e}")
            # Return minimal PDF content
            return b"PDF generation failed"

    def _get_risk_level_arabic(self, risk_score) -> str:
        """Get Arabic risk level description"""
        # Ensure risk_score is an integer
        if isinstance(risk_score, str):
            try:
                risk_score = int(risk_score)
            except (ValueError, TypeError):
                risk_score = 0

        if risk_score >= 80:
            return "مخاطر حرجة"
        elif risk_score >= 60:
            return "مخاطر عالية"
        elif risk_score >= 40:
            return "مخاطر متوسطة"
        elif risk_score >= 20:
            return "مخاطر منخفضة"
        else:
            return "مخاطر ضئيلة"

    def _generate_meaningful_legal_points(self, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate meaningful legal points based on contract analysis"""
        contract_text = analysis_data.get('original_text', '')
        # Ensure risk_score is an integer
        risk_score = analysis_data.get('risk_score', 0)
        if isinstance(risk_score, str):
            try:
                risk_score = int(risk_score)
            except (ValueError, TypeError):
                risk_score = 0

        # Base legal points that are always relevant
        legal_points = []

        # Contract structure analysis
        if 'عقد' in contract_text or 'اتفاقية' in contract_text:
            legal_points.append({
                "title": "تحليل هيكل العقد الأساسي",
                "description": "تم تحديد العقد كوثيقة قانونية ملزمة تحتوي على التزامات متبادلة بين الأطراف",
                "priority": "high",
                "law_reference": "القانون المدني الكويتي - المواد 157-171"
            })

        # Parties identification
        if 'الطرف الأول' in contract_text or 'الطرف الثاني' in contract_text:
            legal_points.append({
                "title": "تحديد أطراف العقد",
                "description": "تم تحديد الأطراف المتعاقدة بوضوح مما يضمن وضوح الالتزامات والحقوق",
                "priority": "medium",
                "law_reference": "القانون المدني - المادة 158"
            })

        # Financial obligations
        if any(term in contract_text for term in ['مبلغ', 'دينار', 'ريال', 'دولار', 'راتب', 'أجر']):
            legal_points.append({
                "title": "الالتزامات المالية والتعويضات",
                "description": "يحتوي العقد على التزامات مالية محددة تتطلب مراجعة دقيقة لضمان الوضوح والعدالة",
                "priority": "high",
                "law_reference": "القانون المدني - المواد 227-234"
            })

        # Termination clauses
        if any(term in contract_text for term in ['إنهاء', 'فسخ', 'إلغاء', 'انتهاء']):
            legal_points.append({
                "title": "شروط إنهاء العقد",
                "description": "يتضمن العقد بنود إنهاء تحتاج إلى مراجعة لضمان التوازن بين حقوق الأطراف",
                "priority": "medium",
                "law_reference": "القانون المدني - المواد 157-171"
            })

        # Risk-based points
        if risk_score >= 70:
            legal_points.append({
                "title": "مخاطر قانونية عالية مكتشفة",
                "description": "تم تحديد مخاطر قانونية عالية تتطلب مراجعة فورية من قبل مختص قانوني",
                "priority": "critical",
                "law_reference": "مراجعة شاملة مطلوبة"
            })

        # Compliance check
        if 'قانون' in contract_text or 'نظام' in contract_text:
            legal_points.append({
                "title": "مراجعة الامتثال القانوني",
                "description": "يشير العقد إلى قوانين وأنظمة محددة تتطلب التحقق من الامتثال الكامل",
                "priority": "high",
                "law_reference": "القوانين المرجعية المذكورة في العقد"
            })

        # If no specific points found, add general analysis
        if not legal_points:
            legal_points = [
                {
                    "title": "تحليل عام للعقد",
                    "description": "تم إجراء تحليل شامل للعقد وتحديد النقاط الأساسية التي تحتاج إلى مراجعة",
                    "priority": "medium",
                    "law_reference": "القانون المدني الكويتي"
                },
                {
                    "title": "مراجعة البنود والشروط",
                    "description": "تحتاج جميع البنود والشروط إلى مراجعة دقيقة لضمان الوضوح والامتثال القانوني",
                    "priority": "medium",
                    "law_reference": "أحكام العقود العامة"
                }
            ]

        return legal_points[:10]  # Limit to 10 points

    def _generate_meaningful_recommendations(self, analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate meaningful recommendations based on contract analysis"""
        contract_text = analysis_data.get('original_text', '')
        # Ensure risk_score is an integer
        risk_score = analysis_data.get('risk_score', 0)
        if isinstance(risk_score, str):
            try:
                risk_score = int(risk_score)
            except (ValueError, TypeError):
                risk_score = 0
        legal_points = analysis_data.get('legal_points', [])

        recommendations = []

        # Risk-based recommendations
        if risk_score >= 70:
            recommendations.append({
                "title": "مراجعة قانونية عاجلة مطلوبة",
                "description": "نظراً لارتفاع درجة المخاطر، يُنصح بشدة بمراجعة العقد من قبل مستشار قانوني مختص",
                "priority": "urgent",
                "category": "قانوني",
                "implementation": "الاتصال بمستشار قانوني خلال 24 ساعة"
            })

        # Financial terms recommendations
        if any(term in contract_text for term in ['مبلغ', 'دينار', 'ريال', 'دولار']):
            recommendations.append({
                "title": "توضيح الشروط المالية",
                "description": "يُنصح بإضافة تفاصيل أكثر وضوحاً حول الالتزامات المالية وآليات الدفع",
                "priority": "important",
                "category": "مالي",
                "implementation": "إضافة جدول مفصل للمدفوعات والمواعيد"
            })

        # Termination clause recommendations
        if 'إنهاء' in contract_text or 'فسخ' in contract_text:
            recommendations.append({
                "title": "تحسين بنود الإنهاء",
                "description": "يُنصح بتوضيح شروط وإجراءات إنهاء العقد لتجنب النزاعات المستقبلية",
                "priority": "important",
                "category": "قانوني",
                "implementation": "إضافة بنود واضحة لحالات وإجراءات الإنهاء"
            })

        # Dispute resolution
        if 'نزاع' not in contract_text and 'تحكيم' not in contract_text:
            recommendations.append({
                "title": "إضافة آلية حل النزاعات",
                "description": "يُنصح بإضافة بند واضح لآلية حل النزاعات (تحكيم، وساطة، أو محاكم)",
                "priority": "important",
                "category": "قانوني",
                "implementation": "إضافة بند تحكيم أو تحديد المحكمة المختصة"
            })

        # Force majeure
        if 'قوة قاهرة' not in contract_text and 'ظروف استثنائية' not in contract_text:
            recommendations.append({
                "title": "إضافة بند القوة القاهرة",
                "description": "يُنصح بإضافة بند للقوة القاهرة لحماية الأطراف في الظروف الاستثنائية",
                "priority": "optional",
                "category": "قانوني",
                "implementation": "إضافة بند شامل للقوة القاهرة والظروف الاستثنائية"
            })

        # Intellectual property
        if any(term in contract_text for term in ['ملكية فكرية', 'براءة', 'حقوق المؤلف']):
            recommendations.append({
                "title": "حماية الملكية الفكرية",
                "description": "يُنصح بتعزيز بنود حماية الملكية الفكرية وتوضيح حقوق كل طرف",
                "priority": "important",
                "category": "قانوني",
                "implementation": "إضافة بنود مفصلة لحماية الملكية الفكرية"
            })

        # Confidentiality
        if 'سرية' not in contract_text and 'معلومات سرية' not in contract_text:
            recommendations.append({
                "title": "إضافة بند السرية",
                "description": "يُنصح بإضافة بند شامل للسرية لحماية المعلومات الحساسة",
                "priority": "optional",
                "category": "قانوني",
                "implementation": "إضافة اتفاقية عدم إفشاء شاملة"
            })

        # General improvement
        recommendations.append({
            "title": "مراجعة شاملة للصياغة",
            "description": "يُنصح بمراجعة عامة لصياغة العقد لضمان الوضوح والدقة القانونية",
            "priority": "optional",
            "category": "تحسين عام",
            "implementation": "مراجعة لغوية وقانونية شاملة"
        })

        return recommendations[:8]  # Limit to 8 recommendations

    def _are_points_generic(self, legal_points: List[Dict[str, Any]]) -> bool:
        """Check if legal points are generic/placeholder content"""
        if not legal_points:
            return True

        generic_indicators = [
            'نقطة غير محددة',
            'نقطة قانونية',
            'Legal Point',
            'No description',
            'لا يوجد وصف'
        ]

        for point in legal_points:
            title = point.get('title', '')
            description = point.get('description', '')

            # Check if any generic indicators are found
            for indicator in generic_indicators:
                if indicator in title or indicator in description:
                    return True

        return False

    def _are_recommendations_generic(self, recommendations: List[Dict[str, Any]]) -> bool:
        """Check if recommendations are generic/placeholder content"""
        if not recommendations:
            return True

        generic_indicators = [
            'توصية',
            'Recommendation',
            'تعديل بنود',
            'إضافة بند',
            'No description',
            'لا يوجد وصف'
        ]

        # Check if recommendations are repetitive or generic
        titles = [rec.get('title', '') for rec in recommendations]
        if len(set(titles)) < len(titles) * 0.5:  # More than 50% duplicates
            return True

        for rec in recommendations:
            title = rec.get('title', '')
            description = rec.get('description', '')

            # Check if any generic indicators are found
            for indicator in generic_indicators:
                if indicator in title or indicator in description:
                    return True

        return False

    def _create_informative_fallback_pdf(self, analysis_data: Dict[str, Any], error_msg: str) -> bytes:
        """Create informative fallback PDF with error details and available data"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib import colors

            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()

            # Title
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=styles['Title'],
                fontSize=18,
                textColor=colors.darkblue,
                spaceAfter=20
            )
            story.append(Paragraph("تقرير تحليل العقد - Enhanced Legal Contract Analyzer", title_style))
            story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
            story.append(Spacer(1, 20))

            # Error information
            error_style = ParagraphStyle(
                'ErrorStyle',
                parent=styles['Normal'],
                fontSize=12,
                textColor=colors.red,
                spaceAfter=10
            )
            story.append(Paragraph("⚠️ Export Error Information:", error_style))
            story.append(Paragraph(f"Error: {error_msg}", styles['Normal']))
            story.append(Spacer(1, 20))

            # Available data summary
            story.append(Paragraph("📊 Available Analysis Data:", styles['Heading2']))

            # Contract info
            if analysis_data.get('contract_info'):
                story.append(Paragraph("Contract Information:", styles['Heading3']))
                contract_info = analysis_data['contract_info']
                for key, value in contract_info.items():
                    story.append(Paragraph(f"• {key}: {value}", styles['Normal']))
                story.append(Spacer(1, 10))

            # Risk score
            if 'risk_score' in analysis_data:
                story.append(Paragraph(f"Risk Score: {analysis_data['risk_score']}/100", styles['Normal']))
                story.append(Spacer(1, 10))

            # Legal points count
            legal_points = analysis_data.get('legal_points', [])
            story.append(Paragraph(f"Legal Points Identified: {len(legal_points)}", styles['Normal']))

            # Recommendations count
            recommendations = analysis_data.get('recommendations', [])
            story.append(Paragraph(f"Recommendations Generated: {len(recommendations)}", styles['Normal']))

            story.append(Spacer(1, 20))
            story.append(Paragraph("💡 Note: For complete analysis details, please use the web interface.", styles['Normal']))
            story.append(Paragraph("تنويه: للحصول على تفاصيل التحليل الكاملة، يرجى استخدام واجهة الويب.", styles['Normal']))

            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
        except:
            return self._create_fallback_pdf(analysis_data)

    def _create_informative_fallback_docx(self, analysis_data: Dict[str, Any], error_msg: str) -> bytes:
        """Create informative fallback DOCX with error details and available data"""
        try:
            from docx import Document

            doc = Document()
            doc.add_heading('تقرير تحليل العقد - Enhanced Legal Contract Analyzer', 0)
            doc.add_paragraph(f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

            # Error information
            doc.add_heading('⚠️ Export Error Information', level=1)
            doc.add_paragraph(f'Error: {error_msg}')

            # Available data summary
            doc.add_heading('📊 Available Analysis Data', level=1)

            # Contract info
            if analysis_data.get('contract_info'):
                doc.add_heading('Contract Information', level=2)
                contract_info = analysis_data['contract_info']
                for key, value in contract_info.items():
                    doc.add_paragraph(f'• {key}: {value}')

            # Risk score
            if 'risk_score' in analysis_data:
                doc.add_paragraph(f'Risk Score: {analysis_data["risk_score"]}/100')

            # Legal points count
            legal_points = analysis_data.get('legal_points', [])
            doc.add_paragraph(f'Legal Points Identified: {len(legal_points)}')

            # Recommendations count
            recommendations = analysis_data.get('recommendations', [])
            doc.add_paragraph(f'Recommendations Generated: {len(recommendations)}')

            doc.add_paragraph('💡 Note: For complete analysis details, please use the web interface.')
            doc.add_paragraph('تنويه: للحصول على تفاصيل التحليل الكاملة، يرجى استخدام واجهة الويب.')

            buffer = io.BytesIO()
            doc.save(buffer)
            buffer.seek(0)
            return buffer.getvalue()
        except:
            return self._create_fallback_docx(analysis_data)
