#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legal Guidelines Manager
Handles custom legal guidelines database for enhanced contract analysis
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LegalGuidelinesManager:
    """Manages custom legal guidelines database"""
    
    def __init__(self, db_path: str = "legal_contracts.db"):
        """Initialize guidelines manager"""
        self.db_path = db_path
        self._init_guidelines_tables()
    
    def _init_guidelines_tables(self):
        """Initialize guidelines database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Legal guidelines table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS legal_guidelines (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        category TEXT NOT NULL,
                        content TEXT NOT NULL,
                        source TEXT,
                        legal_system TEXT NOT NULL,
                        priority INTEGER DEFAULT 1,
                        tags TEXT,
                        created_by TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        version INTEGER DEFAULT 1
                    )
                """)
                
                # Guidelines categories table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS guideline_categories (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        legal_system TEXT NOT NULL,
                        created_at TEXT NOT NULL
                    )
                """)
                
                # Guidelines usage tracking
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS guideline_usage (
                        id TEXT PRIMARY KEY,
                        guideline_id TEXT NOT NULL,
                        contract_id TEXT,
                        analysis_id TEXT,
                        usage_context TEXT,
                        used_at TEXT NOT NULL,
                        FOREIGN KEY (guideline_id) REFERENCES legal_guidelines (id)
                    )
                """)
                
                # Initialize default categories
                self._init_default_categories()
                
        except Exception as e:
            logger.error(f"Error initializing guidelines tables: {e}")
            raise
    
    def _init_default_categories(self):
        """Initialize default guideline categories"""
        default_categories = [
            {
                "name": "Central Bank Regulations",
                "description": "البنك المركزي الكويتي - اللوائح والتعليمات",
                "legal_system": "kuwait"
            },
            {
                "name": "Commercial Law",
                "description": "القانون التجاري - الأحكام والقواعد",
                "legal_system": "kuwait"
            },
            {
                "name": "Labor Law",
                "description": "قانون العمل - الأحكام والضوابط",
                "legal_system": "kuwait"
            },
            {
                "name": "Real Estate Law",
                "description": "قانون العقارات والأراضي",
                "legal_system": "kuwait"
            },
            {
                "name": "SAMA Regulations",
                "description": "مؤسسة النقد العربي السعودي - اللوائح",
                "legal_system": "saudi"
            },
            {
                "name": "Saudi Commercial Law",
                "description": "نظام الشركات السعودي",
                "legal_system": "saudi"
            }
        ]
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                for category in default_categories:
                    # Check if category exists
                    existing = conn.execute(
                        "SELECT id FROM guideline_categories WHERE name = ? AND legal_system = ?",
                        (category["name"], category["legal_system"])
                    ).fetchone()
                    
                    if not existing:
                        conn.execute("""
                            INSERT INTO guideline_categories (id, name, description, legal_system, created_at)
                            VALUES (?, ?, ?, ?, ?)
                        """, (
                            str(uuid.uuid4()),
                            category["name"],
                            category["description"],
                            category["legal_system"],
                            datetime.now().isoformat()
                        ))
        except Exception as e:
            logger.error(f"Error initializing default categories: {e}")
    
    def add_guideline(self, title: str, category: str, content: str, 
                     legal_system: str, source: str = None, 
                     tags: List[str] = None, priority: int = 1,
                     created_by: str = "system") -> str:
        """Add new legal guideline"""
        try:
            guideline_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO legal_guidelines (
                        id, title, category, content, source, legal_system,
                        priority, tags, created_by, created_at, updated_at, is_active, version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    guideline_id, title, category, content, source, legal_system,
                    priority, json.dumps(tags or []), created_by, current_time, 
                    current_time, True, 1
                ))
            
            logger.info(f"Added guideline: {title}")
            return guideline_id
            
        except Exception as e:
            logger.error(f"Error adding guideline: {e}")
            raise
    
    def get_guidelines(self, legal_system: str = None, category: str = None, 
                      active_only: bool = True) -> List[Dict[str, Any]]:
        """Get legal guidelines with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = "SELECT * FROM legal_guidelines WHERE 1=1"
                params = []
                
                if legal_system:
                    query += " AND legal_system = ?"
                    params.append(legal_system)
                
                if category:
                    query += " AND category = ?"
                    params.append(category)
                
                if active_only:
                    query += " AND is_active = 1"
                
                query += " ORDER BY priority DESC, created_at DESC"
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                guidelines = []
                for row in rows:
                    guidelines.append({
                        'id': row[0],
                        'title': row[1],
                        'category': row[2],
                        'content': row[3],
                        'source': row[4],
                        'legal_system': row[5],
                        'priority': row[6],
                        'tags': json.loads(row[7]) if row[7] else [],
                        'created_by': row[8],
                        'created_at': row[9],
                        'updated_at': row[10],
                        'is_active': row[11],
                        'version': row[12]
                    })
                
                return guidelines
                
        except Exception as e:
            logger.error(f"Error getting guidelines: {e}")
            return []
    
    def get_categories(self, legal_system: str = None) -> List[Dict[str, Any]]:
        """Get guideline categories"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = "SELECT * FROM guideline_categories WHERE 1=1"
                params = []
                
                if legal_system:
                    query += " AND legal_system = ?"
                    params.append(legal_system)
                
                query += " ORDER BY name"
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                categories = []
                for row in rows:
                    categories.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'legal_system': row[3],
                        'created_at': row[4]
                    })
                
                return categories
                
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []
    
    def update_guideline(self, guideline_id: str, **kwargs) -> bool:
        """Update existing guideline"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get current guideline
                current = conn.execute(
                    "SELECT * FROM legal_guidelines WHERE id = ?", 
                    (guideline_id,)
                ).fetchone()
                
                if not current:
                    return False
                
                # Prepare update fields
                update_fields = []
                params = []
                
                for field, value in kwargs.items():
                    if field in ['title', 'category', 'content', 'source', 'legal_system', 'priority']:
                        update_fields.append(f"{field} = ?")
                        params.append(value)
                    elif field == 'tags' and isinstance(value, list):
                        update_fields.append("tags = ?")
                        params.append(json.dumps(value))
                
                if update_fields:
                    update_fields.append("updated_at = ?")
                    params.append(datetime.now().isoformat())
                    
                    update_fields.append("version = ?")
                    params.append(current[12] + 1)  # Increment version
                    
                    params.append(guideline_id)
                    
                    query = f"UPDATE legal_guidelines SET {', '.join(update_fields)} WHERE id = ?"
                    conn.execute(query, params)
                
                return True
                
        except Exception as e:
            logger.error(f"Error updating guideline: {e}")
            return False
    
    def delete_guideline(self, guideline_id: str, soft_delete: bool = True) -> bool:
        """Delete guideline (soft delete by default)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if soft_delete:
                    conn.execute(
                        "UPDATE legal_guidelines SET is_active = 0, updated_at = ? WHERE id = ?",
                        (datetime.now().isoformat(), guideline_id)
                    )
                else:
                    conn.execute("DELETE FROM legal_guidelines WHERE id = ?", (guideline_id,))
                
                return True
                
        except Exception as e:
            logger.error(f"Error deleting guideline: {e}")
            return False
    
    def track_usage(self, guideline_id: str, contract_id: str = None, 
                   analysis_id: str = None, context: str = "analysis") -> str:
        """Track guideline usage"""
        try:
            usage_id = str(uuid.uuid4())
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO guideline_usage (
                        id, guideline_id, contract_id, analysis_id, usage_context, used_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    usage_id, guideline_id, contract_id, analysis_id, 
                    context, datetime.now().isoformat()
                ))
            
            return usage_id
            
        except Exception as e:
            logger.error(f"Error tracking usage: {e}")
            return None
    
    def get_guidelines_for_analysis(self, legal_system: str, 
                                   contract_type: str = None) -> str:
        """Get formatted guidelines for AI analysis"""
        guidelines = self.get_guidelines(legal_system=legal_system)
        
        if not guidelines:
            return ""
        
        formatted_guidelines = []
        formatted_guidelines.append(f"=== Custom Legal Guidelines for {legal_system.upper()} ===\n")
        
        # Group by category
        categories = {}
        for guideline in guidelines:
            category = guideline['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(guideline)
        
        for category, cat_guidelines in categories.items():
            formatted_guidelines.append(f"\n--- {category} ---")
            for guideline in cat_guidelines:
                formatted_guidelines.append(f"• {guideline['title']}")
                formatted_guidelines.append(f"  {guideline['content'][:500]}...")
                if guideline['source']:
                    formatted_guidelines.append(f"  Source: {guideline['source']}")
                formatted_guidelines.append("")
        
        return "\n".join(formatted_guidelines)
