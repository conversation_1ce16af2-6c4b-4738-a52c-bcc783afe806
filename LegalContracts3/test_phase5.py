#!/usr/bin/env python3
"""
Phase 5 Integration Test Suite
Tests all Phase 5 enterprise features
"""

import unittest
import sys
import os
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

class TestPhase5Integration(unittest.TestCase):
    """Test Phase 5 enterprise features integration"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_passed = []
        self.test_failed = []
    
    def test_security_manager_import(self):
        """Test security manager import and initialization"""
        try:
            from security_manager import SecurityManager, EncryptionManager, get_security_manager
            
            # Test encryption manager
            encryption_manager = EncryptionManager()
            self.assertIsNotNone(encryption_manager.key)
            
            # Test password hashing
            password = "test_password_123"
            hashed, salt = encryption_manager.hash_password(password)
            self.assertTrue(encryption_manager.verify_password(password, hashed, salt))
            
            # Test security manager
            security_manager = get_security_manager()
            self.assertIsNotNone(security_manager)
            
            # Test rate limiting
            result = security_manager.check_rate_limit("test_user", "login")
            self.assertTrue(result)
            
            self.test_passed.append("✅ Security Manager")
            
        except Exception as e:
            self.test_failed.append(f"❌ Security Manager: {str(e)}")
            self.fail(f"Security Manager test failed: {e}")
    
    def test_business_intelligence_import(self):
        """Test business intelligence import and initialization"""
        try:
            from business_intelligence import BusinessIntelligence, BIDashboard

            # Test BI initialization
            bi = BusinessIntelligence()
            self.assertIsNotNone(bi)

            # Test dashboard (without database operations)
            dashboard = BIDashboard()
            self.assertIsNotNone(dashboard)

            self.test_passed.append("✅ Business Intelligence")

        except Exception as e:
            self.test_failed.append(f"❌ Business Intelligence: {str(e)}")
            self.fail(f"Business Intelligence test failed: {e}")
    
    def test_internationalization_import(self):
        """Test internationalization import and initialization"""
        try:
            from internationalization import LanguageManager, LocalizationUI, get_language_manager, t
            
            # Test language manager
            lang_manager = get_language_manager()
            self.assertIsNotNone(lang_manager)
            
            # Test translation
            text = t("app_title")
            self.assertIsNotNone(text)
            
            # Test language switching
            lang_manager.set_language("en")
            english_text = t("app_title")
            
            lang_manager.set_language("ar")
            arabic_text = t("app_title")
            
            self.assertNotEqual(english_text, arabic_text)
            
            # Test RTL detection
            is_rtl = lang_manager.is_rtl()
            self.assertTrue(is_rtl)  # Arabic is RTL
            
            # Test localization UI
            localization_ui = LocalizationUI(lang_manager)
            self.assertIsNotNone(localization_ui)
            
            self.test_passed.append("✅ Internationalization")
            
        except Exception as e:
            self.test_failed.append(f"❌ Internationalization: {str(e)}")
            self.fail(f"Internationalization test failed: {e}")
    
    def test_translation_files(self):
        """Test translation files exist and are valid"""
        try:
            from pathlib import Path
            import json
            
            translations_dir = Path("translations")
            
            # Check if translation files exist
            ar_file = translations_dir / "ar.json"
            en_file = translations_dir / "en.json"
            
            if not ar_file.exists():
                # Create translations directory and files if they don't exist
                translations_dir.mkdir(exist_ok=True)
                from internationalization import LanguageManager
                lang_manager = LanguageManager()
                lang_manager.load_translations()
            
            self.assertTrue(ar_file.exists(), "Arabic translation file should exist")
            self.assertTrue(en_file.exists(), "English translation file should exist")
            
            # Test JSON validity
            with open(ar_file, 'r', encoding='utf-8') as f:
                ar_data = json.load(f)
            
            with open(en_file, 'r', encoding='utf-8') as f:
                en_data = json.load(f)
            
            # Check required keys
            required_keys = ["app_title", "navigation", "messages"]
            for key in required_keys:
                self.assertIn(key, ar_data, f"Arabic translations missing key: {key}")
                self.assertIn(key, en_data, f"English translations missing key: {key}")
            
            self.test_passed.append("✅ Translation Files")
            
        except Exception as e:
            self.test_failed.append(f"❌ Translation Files: {str(e)}")
            self.fail(f"Translation files test failed: {e}")
    
    def test_app_integration(self):
        """Test main app integration with Phase 5 features"""
        try:
            # Test imports in app.py
            import app
            
            # Check if session state initialization includes Phase 5 components
            app.initialize_session_state()
            
            # This would normally require Streamlit session state
            # For testing, we'll just verify the imports work
            
            self.test_passed.append("✅ App Integration")
            
        except Exception as e:
            self.test_failed.append(f"❌ App Integration: {str(e)}")
            self.fail(f"App integration test failed: {e}")
    
    def test_security_features(self):
        """Test advanced security features"""
        try:
            from security_manager import SecurityManager

            security_manager = SecurityManager()

            # Test rate limiting
            test_user = "test_user"
            result = security_manager.check_rate_limit(test_user, "login")
            self.assertIsInstance(result, bool)

            # Test security event logging (with string data instead of dict)
            security_manager.log_security_event("test_event", "low", "test data")

            # Test audit logging (with string data instead of dict)
            security_manager.log_audit_event("test_user", "test_action", "test data")

            # Test dashboard data
            dashboard_data = security_manager.get_security_dashboard_data()
            self.assertIsInstance(dashboard_data, dict)
            self.assertIn("security_score", dashboard_data)

            self.test_passed.append("✅ Security Features")

        except Exception as e:
            self.test_failed.append(f"❌ Security Features: {str(e)}")
            self.fail(f"Security features test failed: {e}")
    
    def test_business_intelligence_features(self):
        """Test business intelligence features"""
        try:
            from business_intelligence import BusinessIntelligence

            bi = BusinessIntelligence()

            # Test basic functionality without database operations
            # Just verify the class methods exist
            self.assertTrue(hasattr(bi, 'get_business_metrics'))
            self.assertTrue(hasattr(bi, 'get_predictive_analytics'))
            self.assertTrue(hasattr(bi, 'get_roi_analysis'))

            self.test_passed.append("✅ Business Intelligence Features")

        except Exception as e:
            self.test_failed.append(f"❌ Business Intelligence Features: {str(e)}")
            self.fail(f"Business Intelligence features test failed: {e}")
    
    def tearDown(self):
        """Clean up after tests"""
        pass
    
    def print_test_summary(self):
        """Print test summary"""
        print("\n" + "="*60)
        print("🧪 PHASE 5 INTEGRATION TEST SUMMARY")
        print("="*60)
        
        print(f"\n✅ PASSED TESTS ({len(self.test_passed)}):")
        for test in self.test_passed:
            print(f"  {test}")
        
        if self.test_failed:
            print(f"\n❌ FAILED TESTS ({len(self.test_failed)}):")
            for test in self.test_failed:
                print(f"  {test}")
        
        total_tests = len(self.test_passed) + len(self.test_failed)
        success_rate = (len(self.test_passed) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📊 SUCCESS RATE: {success_rate:.1f}% ({len(self.test_passed)}/{total_tests})")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT! Phase 5 integration is working perfectly!")
        elif success_rate >= 75:
            print("✅ GOOD! Phase 5 integration is mostly working.")
        else:
            print("⚠️ NEEDS ATTENTION! Some Phase 5 features need fixing.")
        
        print("="*60)

def run_phase5_tests():
    """Run all Phase 5 integration tests"""
    print("🚀 Starting Phase 5 Integration Tests...")
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPhase5Integration)
    
    # Create custom test runner to capture results
    class CustomTestResult(unittest.TextTestResult):
        def __init__(self, stream, descriptions, verbosity):
            super().__init__(stream, descriptions, verbosity)
            self.test_instance = None
        
        def startTest(self, test):
            super().startTest(test)
            if hasattr(test, 'test_passed'):
                self.test_instance = test
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, resultclass=CustomTestResult)
    result = runner.run(suite)
    
    # Print summary if we have access to test instance
    if hasattr(result, 'test_instance') and result.test_instance:
        result.test_instance.print_test_summary()
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_phase5_tests()
    sys.exit(0 if success else 1)
