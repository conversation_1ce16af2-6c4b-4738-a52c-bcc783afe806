#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Legal Compliance Checker
Automated compliance verification for different jurisdictions
"""

import re
import json
import logging
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class ComplianceChecker:
    """Automated legal compliance verification system"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self._initialize_compliance_rules()
    
    def _initialize_compliance_rules(self):
        """Initialize default compliance rules for Kuwait and Saudi systems"""
        
        kuwait_rules = [
            {
                'name': 'تحديد هوية الأطراف',
                'description': 'يجب تحديد هوية جميع أطراف العقد بوضوح',
                'legal_system': 'kuwait',
                'jurisdiction': 'الكويت',
                'rule_type': 'mandatory',
                'rule_content': json.dumps({
                    'pattern': r'(الطرف الأول|الطرف الثاني|المتعاقد|العميل|المقاول)',
                    'required_elements': ['اسم كامل', 'عنوان', 'رقم مدني أو تجاري'],
                    'check_type': 'pattern_match'
                }),
                'severity': 'high'
            },
            {
                'name': 'شروط الدفع والعملة',
                'description': 'يجب تحديد شروط الدفع والعملة المستخدمة',
                'legal_system': 'kuwait',
                'jurisdiction': 'الكويت',
                'rule_type': 'mandatory',
                'rule_content': json.dumps({
                    'pattern': r'(دينار كويتي|ك\.د|KWD|دفع|سداد|مبلغ)',
                    'required_elements': ['مبلغ محدد', 'عملة', 'تاريخ استحقاق'],
                    'check_type': 'content_analysis'
                }),
                'severity': 'high'
            },
            {
                'name': 'القانون الواجب التطبيق',
                'description': 'يجب تحديد القانون الواجب التطبيق في العقد',
                'legal_system': 'kuwait',
                'jurisdiction': 'الكويت',
                'rule_type': 'recommended',
                'rule_content': json.dumps({
                    'pattern': r'(القانون الكويتي|قانون الكويت|المحاكم الكويتية)',
                    'check_type': 'pattern_match'
                }),
                'severity': 'medium'
            },
            {
                'name': 'بند القوة القاهرة',
                'description': 'يُنصح بتضمين بند القوة القاهرة في العقود طويلة المدى',
                'legal_system': 'kuwait',
                'jurisdiction': 'الكويت',
                'rule_type': 'recommended',
                'rule_content': json.dumps({
                    'pattern': r'(قوة قاهرة|ظروف استثنائية|أحداث خارجة عن الإرادة)',
                    'check_type': 'pattern_match'
                }),
                'severity': 'low'
            }
        ]
        
        saudi_rules = [
            {
                'name': 'الامتثال للأنظمة السعودية',
                'description': 'يجب النص على الامتثال للأنظمة السعودية',
                'legal_system': 'saudi',
                'jurisdiction': 'المملكة العربية السعودية',
                'rule_type': 'mandatory',
                'rule_content': json.dumps({
                    'pattern': r'(النظام السعودي|الأنظمة السعودية|المملكة العربية السعودية)',
                    'check_type': 'pattern_match'
                }),
                'severity': 'high'
            },
            {
                'name': 'متطلبات السعودة',
                'description': 'يجب تضمين متطلبات السعودة في عقود العمل والخدمات',
                'legal_system': 'saudi',
                'jurisdiction': 'المملكة العربية السعودية',
                'rule_type': 'conditional',
                'rule_content': json.dumps({
                    'pattern': r'(سعودة|نطاقات|وزارة الموارد البشرية)',
                    'condition': 'employment_or_services',
                    'check_type': 'conditional_pattern'
                }),
                'severity': 'high'
            },
            {
                'name': 'ضريبة القيمة المضافة',
                'description': 'يجب تحديد المسؤولية عن ضريبة القيمة المضافة',
                'legal_system': 'saudi',
                'jurisdiction': 'المملكة العربية السعودية',
                'rule_type': 'mandatory',
                'rule_content': json.dumps({
                    'pattern': r'(ضريبة القيمة المضافة|VAT|15%|الزكاة والدخل)',
                    'check_type': 'pattern_match'
                }),
                'severity': 'high'
            },
            {
                'name': 'حماية البيانات الشخصية',
                'description': 'يجب الامتثال لنظام حماية البيانات الشخصية',
                'legal_system': 'saudi',
                'jurisdiction': 'المملكة العربية السعودية',
                'rule_type': 'conditional',
                'rule_content': json.dumps({
                    'pattern': r'(حماية البيانات|البيانات الشخصية|الخصوصية)',
                    'condition': 'data_processing',
                    'check_type': 'conditional_pattern'
                }),
                'severity': 'high'
            },
            {
                'name': 'التحكيم والنزاعات',
                'description': 'يُنصح بتحديد آلية تسوية النزاعات',
                'legal_system': 'saudi',
                'jurisdiction': 'المملكة العربية السعودية',
                'rule_type': 'recommended',
                'rule_content': json.dumps({
                    'pattern': r'(تحكيم|مركز التحكيم|تسوية النزاعات)',
                    'check_type': 'pattern_match'
                }),
                'severity': 'medium'
            }
        ]
        
        # Add rules if they don't exist
        try:
            existing_rules = self.get_compliance_rules()
            if len(existing_rules) == 0:
                logger.info("Initializing default compliance rules...")
                
                for rule in kuwait_rules + saudi_rules:
                    self.add_compliance_rule(**rule)
                
                logger.info(f"Added {len(kuwait_rules + saudi_rules)} compliance rules")
        except Exception as e:
            logger.error(f"Error initializing compliance rules: {e}")
    
    def add_compliance_rule(self, name: str, description: str, legal_system: str,
                           jurisdiction: str, rule_type: str, rule_content: str,
                           severity: str = 'medium', created_by: str = None) -> Optional[str]:
        """Add a new compliance rule"""
        try:
            rule_id = str(uuid.uuid4())

            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO compliance_rules 
                    (id, name, description, legal_system, jurisdiction, rule_type,
                     rule_content, severity, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (rule_id, name, description, legal_system, jurisdiction, rule_type,
                      rule_content, severity, created_by))
                
                conn.commit()
                logger.info(f"Compliance rule added: {rule_id}")
                return rule_id
                
        except Exception as e:
            logger.error(f"Error adding compliance rule: {e}")
            return None
    
    def get_compliance_rules(self, legal_system: str = None, rule_type: str = None) -> List[Dict[str, Any]]:
        """Get compliance rules with optional filtering"""
        try:
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT id, name, description, legal_system, jurisdiction, rule_type,
                           rule_content, severity, created_at, updated_at
                    FROM compliance_rules 
                    WHERE is_active = 1
                '''
                params = []
                
                if legal_system:
                    query += ' AND legal_system = ?'
                    params.append(legal_system)
                
                if rule_type:
                    query += ' AND rule_type = ?'
                    params.append(rule_type)
                
                query += ' ORDER BY severity DESC, name'
                
                cursor.execute(query, params)
                
                rules = []
                for row in cursor.fetchall():
                    rules.append({
                        'id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'legal_system': row[3],
                        'jurisdiction': row[4],
                        'rule_type': row[5],
                        'rule_content': json.loads(row[6]) if row[6] else {},
                        'severity': row[7],
                        'created_at': row[8],
                        'updated_at': row[9]
                    })
                
                return rules
                
        except Exception as e:
            logger.error(f"Error getting compliance rules: {e}")
            return []
    
    def check_contract_compliance(self, contract_text: str, legal_system: str) -> Dict[str, Any]:
        """Check contract compliance against applicable rules"""
        try:
            rules = self.get_compliance_rules(legal_system=legal_system)
            
            compliance_results = {
                'overall_score': 0,
                'total_rules': len(rules),
                'passed_rules': 0,
                'failed_rules': 0,
                'warnings': 0,
                'critical_issues': 0,
                'rule_results': [],
                'recommendations': [],
                'legal_system': legal_system
            }
            
            for rule in rules:
                result = self._check_single_rule(contract_text, rule)
                compliance_results['rule_results'].append(result)
                
                if result['status'] == 'passed':
                    compliance_results['passed_rules'] += 1
                elif result['status'] == 'failed':
                    compliance_results['failed_rules'] += 1
                    if rule['severity'] == 'high':
                        compliance_results['critical_issues'] += 1
                    else:
                        compliance_results['warnings'] += 1
                
                # Add recommendations for failed rules
                if result['status'] == 'failed':
                    compliance_results['recommendations'].append({
                        'rule_name': rule['name'],
                        'severity': rule['severity'],
                        'recommendation': result.get('recommendation', rule['description'])
                    })
            
            # Calculate overall score
            if compliance_results['total_rules'] > 0:
                compliance_results['overall_score'] = round(
                    (compliance_results['passed_rules'] / compliance_results['total_rules']) * 100, 2
                )
            
            return compliance_results
            
        except Exception as e:
            logger.error(f"Error checking contract compliance: {e}")
            return {
                'overall_score': 0,
                'error': str(e),
                'legal_system': legal_system
            }
    
    def _check_single_rule(self, contract_text: str, rule: Dict[str, Any]) -> Dict[str, Any]:
        """Check a single compliance rule against contract text"""
        try:
            rule_content = rule['rule_content']
            check_type = rule_content.get('check_type', 'pattern_match')
            
            result = {
                'rule_id': rule['id'],
                'rule_name': rule['name'],
                'rule_type': rule['rule_type'],
                'severity': rule['severity'],
                'status': 'failed',
                'details': '',
                'recommendation': rule['description']
            }
            
            if check_type == 'pattern_match':
                pattern = rule_content.get('pattern', '')
                if pattern and re.search(pattern, contract_text, re.IGNORECASE):
                    result['status'] = 'passed'
                    result['details'] = 'تم العثور على النص المطلوب في العقد'
                else:
                    result['details'] = f'لم يتم العثور على النص المطلوب: {pattern}'
            
            elif check_type == 'content_analysis':
                required_elements = rule_content.get('required_elements', [])
                found_elements = []
                
                for element in required_elements:
                    if self._check_content_element(contract_text, element):
                        found_elements.append(element)
                
                if len(found_elements) >= len(required_elements) * 0.7:  # 70% threshold
                    result['status'] = 'passed'
                    result['details'] = f'تم العثور على {len(found_elements)} من {len(required_elements)} عناصر مطلوبة'
                else:
                    result['details'] = f'تم العثور على {len(found_elements)} فقط من {len(required_elements)} عناصر مطلوبة'
            
            elif check_type == 'conditional_pattern':
                condition = rule_content.get('condition', '')
                pattern = rule_content.get('pattern', '')
                
                if self._check_condition(contract_text, condition):
                    if pattern and re.search(pattern, contract_text, re.IGNORECASE):
                        result['status'] = 'passed'
                        result['details'] = 'تم الوفاء بالشرط والنص المطلوب'
                    else:
                        result['details'] = f'الشرط مطبق ولكن لم يتم العثور على النص المطلوب: {pattern}'
                else:
                    result['status'] = 'not_applicable'
                    result['details'] = 'الشرط غير مطبق على هذا العقد'
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking single rule: {e}")
            return {
                'rule_id': rule['id'],
                'rule_name': rule['name'],
                'status': 'error',
                'details': f'خطأ في فحص القاعدة: {str(e)}'
            }
    
    def _check_content_element(self, contract_text: str, element: str) -> bool:
        """Check if a content element exists in the contract"""
        element_patterns = {
            'اسم كامل': r'[أ-ي\s]{3,}',
            'عنوان': r'(عنوان|شارع|منطقة|مدينة)',
            'رقم مدني أو تجاري': r'\d{8,}',
            'مبلغ محدد': r'\d+([,\.]\d+)?',
            'عملة': r'(دينار|ريال|درهم|دولار|KWD|SAR|AED|USD)',
            'تاريخ استحقاق': r'(\d{1,2}[/-]\d{1,2}[/-]\d{4}|\d{1,2}\s+(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر))'
        }
        
        pattern = element_patterns.get(element, element)
        return bool(re.search(pattern, contract_text, re.IGNORECASE))
    
    def _check_condition(self, contract_text: str, condition: str) -> bool:
        """Check if a condition applies to the contract"""
        condition_patterns = {
            'employment_or_services': r'(عقد عمل|خدمات|موظف|عامل|راتب)',
            'data_processing': r'(بيانات|معلومات شخصية|خصوصية|معالجة البيانات)',
            'commercial_contract': r'(تجاري|بيع|شراء|توريد)',
            'long_term_contract': r'(سنة|سنوات|\d+\s*شهر)'
        }
        
        pattern = condition_patterns.get(condition, condition)
        return bool(re.search(pattern, contract_text, re.IGNORECASE))
    
    def get_compliance_summary(self, legal_system: str) -> Dict[str, Any]:
        """Get compliance rules summary for a legal system"""
        try:
            rules = self.get_compliance_rules(legal_system=legal_system)
            
            summary = {
                'total_rules': len(rules),
                'mandatory_rules': len([r for r in rules if r['rule_type'] == 'mandatory']),
                'recommended_rules': len([r for r in rules if r['rule_type'] == 'recommended']),
                'conditional_rules': len([r for r in rules if r['rule_type'] == 'conditional']),
                'severity_distribution': {
                    'high': len([r for r in rules if r['severity'] == 'high']),
                    'medium': len([r for r in rules if r['severity'] == 'medium']),
                    'low': len([r for r in rules if r['severity'] == 'low'])
                },
                'legal_system': legal_system
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting compliance summary: {e}")
            return {'error': str(e), 'legal_system': legal_system}
